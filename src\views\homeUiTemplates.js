import { attachmentHandlers } from '../utils/attachmentHandlers.js';

export const getPublicReceiversTemplate = () => `
<div class="col-lg-12 col-md-12 col-sm-12 mb-5">
    <div class="card border-0 shadow-sm">
        <div class="accordion fintech-accordion" id="receiversAccordion">
            <div class="accordion-item border-0">
                <h2 class="accordion-header" id="headingOneReceivers">
                    <button class="accordion-button fintech-accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapseOneReceivers" aria-expanded="false" aria-controls="collapseOneReceivers">
                        <div class="d-flex align-items-center w-100 gap-2">
                            <div class="accordion-icon rounded-circle bg-primary bg-opacity-10 p-2 me-3 d-flex align-items-center justify-content-center">
                                <i class="bi bi-people text-white fs-5"></i>
                            </div>
                            <div class="d-flex flex-column justify-content-center">
                                <span class="fw-bold d-block text-white fs-6">Available Billers</span>
                                <small class="text-white fs-12">Select a biller to make payment</small>
                            </div>
                            <i class="bi bi-chevron-down ms-auto"></i>
                        </div>
                    </button>
                </h2>
                <div id="collapseOneReceivers" class="accordion-collapse collapse"
                    aria-labelledby="headingOneReceivers" data-bs-parent="#receiversAccordion">
                    <div class="accordion-body fintech-inner-accordion p-0">
                        <div class="card-header bg-white border-0 py-2 mx-0">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <span class="input-group-text bg-light border-end-0">
                                            <i class="bi bi-search text-muted"></i>
                                        </span>
                                        <input type="text" id="public-receivers-search" class="form-control border-start-0 bg-light" placeholder="Search receivers by name...">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0 bg-transparent">
                            <div class="table-responsive">
                                <table class="table table-hover align-middle mb-0" id="public-receivers-table">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="fw-semibold text-dark border-0 px-4 py-3" style="min-width: 180px;">
                                                <i class="bi bi-building me-2 text-primary"></i>
                                                Biller Name
                                            </th>
                                            <th class="fw-semibold text-dark border-0 px-4 py-3 text-center">
                                                <i class="bi bi-gear me-2 text-primary"></i>
                                                Action
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody id="public-receivers-tbody">
                                        <!-- Rows will be populated by JS -->
                                    </tbody>
                                </table>
                            </div>
                            <div id="public-receivers-pagination" class="d-flex justify-content-center align-items-center gap-2 py-4"></div>
                            <div id="public-receivers-empty" class="text-center py-5">
                                <div class="empty-state">
                                    <div class="spinner-border text-primary mb-3" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <h6 class="text-white">Loading public receivers...</h6>
                                    <p class="text-white small mb-0">Please wait while we fetch available billers</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>`;

export const getQuickHelpTemplate = () => `
<div class="col-lg-12 col-md-12 col-sm-12 mb-4">
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white border-0 py-4">
            <div class="d-flex justify-content-between align-items-center">
                <button class="btn btn-link text-dark p-0 text-decoration-none d-flex align-items-center w-100" type="button" data-bs-toggle="collapse" data-bs-target="#collapseQuickHelp" aria-expanded="false" aria-controls="collapseQuickHelp">
                    <div class="d-flex align-items-center flex-grow-1">
                        <i class="bi bi-question-circle me-3 fs-4 text-white"></i>
                        <div>
                            <h5 class="card-title mb-1 fw-bold text-white">Quick Help & Guide</h5>
                            <p class="text-white small mb-0">Quick how-to guide</p>
                        </div>
                    </div>
                    <div class="d-flex align-items-center gap-3">
                        <span class="badge bg-info bg-opacity-10 text-white px-3 py-2 rounded-pill">
                            <i class="bi bi-lightbulb me-1 text-white"></i>
                            Tips
                        </span>
                        <i class="bi bi-chevron-down text-white fs-5"></i>
                    </div>
                </button>
            </div>
        </div>
        <div id="collapseQuickHelp" class="collapse">
            <div class="card-body p-4">
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="help-item">
                            <div class="d-flex align-items-start">
                                <div class="help-icon bg-primary bg-opacity-10 rounded-2 p-2 me-3">
                                    <i class="bi bi-plus-circle text-white"></i>
                                </div>
                                <div>
                                    <h6 class="fw-semibold mb-1 text-white">Create Payment</h6>
                                    <p class="text-white small mb-0">Generate vouchers for merchants and manage transactions</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="help-item">
                            <div class="d-flex align-items-start">
                                <div class="help-icon bg-success bg-opacity-10 rounded-2 p-2 me-3">
                                    <i class="bi bi-send text-white"></i>
                                </div>
                                <div>
                                    <h6 class="fw-semibold mb-1 text-white">Direct Pay</h6>
                                    <p class="text-white small mb-0">Send instant payments to registered receivers</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="help-item">
                            <div class="d-flex align-items-start">
                                <div class="help-icon bg-warning bg-opacity-10 rounded-2 p-2 me-3">
                                    <i class="bi bi-people text-white"></i>
                                </div>
                                <div>
                                    <h6 class="fw-semibold mb-1 text-white">Public Pay</h6>
                                    <p class="text-white small mb-0">Pay bills to registered public receivers</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="help-item">
                            <div class="d-flex align-items-start">
                                <div class="help-icon bg-info bg-opacity-10 rounded-2 p-2 me-3">
                                    <i class="bi bi-graph-up text-white"></i>
                                </div>
                                <div>
                                    <h6 class="fw-semibold mb-1 text-white">Analytics</h6>
                                    <p class="text-white small mb-0">Track your payment history and performance</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>`;

export const getTransactionsTemplate = () => `
<div class="col-lg-12 col-md-12 col-sm-12 mb-4">
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white border-0 py-4">
            <div class="d-flex justify-content-between align-items-center">
                <button class="btn btn-link text-dark p-0 text-decoration-none d-flex align-items-center w-100" type="button" data-bs-toggle="collapse" data-bs-target="#collapseRecentTransactions" aria-expanded="false" aria-controls="collapseRecentTransactions">
                    <div class="d-flex align-items-center flex-grow-1">
                        <i class="bi bi-clock-history me-3 fs-4 text-primary"></i>
                        <div>
                            <h5 class="card-title mb-1 fw-bold text-white">Recent Transactions</h5>
                            <p class="text-white small mb-0">Your latest payment activity</p>
                        </div>
                    </div>
                    <div class="d-flex align-items-center gap-3">
                        <span class="badge bg-primary bg-opacity-10 text-white px-3 py-2 rounded-pill">
                            <i class="bi bi-activity me-1 text-white"></i>
                            Live
                        </span>
                        <i class="bi bi-chevron-down text-white fs-5"></i>
                    </div>
                </button>
            </div>
        </div>
        <div id="collapseRecentTransactions" class="collapse">
            <div class="card-header bg-white border-0 py-3 border-top">
                <div class="row">
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="bi bi-search text-muted"></i>
                            </span>
                            <input type="text" id="transactions-search" class="form-control border-start-0 bg-light" placeholder="Search transactions...">
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th class="fw-semibold text-dark border-0 py-3 px-4">Date</th>
                            <th class="fw-semibold text-dark border-0 py-3">Amount</th>
                            <th class="fw-semibold text-dark border-0 py-3">Status</th>
                        </tr>
                    </thead>
                    <tbody id="transactions-tbody">
                        <!-- Enhanced loading state -->
                        <tr>
                            <td colspan="4" class="text-center py-5">
                                <div class="d-flex flex-column align-items-center">
                                    <div class="spinner-border text-primary mb-3" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="text-white mb-0">Loading transactions...</p>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div id="transactions-pagination" class="d-flex justify-content-center align-items-center gap-2 py-4"></div>
            <div id="transactions-empty" class="text-center py-5">
                <div class="empty-state">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h6 class="text-white">Loading transactions...</h6>
                    <p class="text-white small mb-0">Please wait while we fetch your transaction history</p>
                </div>
            </div>
            </div>
        </div>
    </div>
</div>`;

export const getMainContentTemplate = () => {
    const showGuide = false; // Always collapsed for better UI

    return `
<div class="row">
    <div class="col-12">
        <!-- Enhanced Hero Section -->
        <div class="hero-section mb-4 mt-5">
            <div class="card border-0 shadow-lg bg-gradient-primary">
                <div class="card-body p-4 p-md-5">
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <div class="hero-content">
                                <h1 class="display-6 fw-bold text-white mb-3" id="greeting">Welcome</h1>
                                <p class="lead text-white-50 mb-4">Secure payment solutions at your fingertips</p>
                                <div class="d-flex flex-wrap gap-3">
                                    <button type="button" class="btn btn-warning btn-lg px-4 py-2 shadow-sm w-100" id="new_transaction">
                                        <i class="bi bi-receipt me-2"></i>
                                        Create Voucher
                                    </button>
                                    <button type="button" class="btn btn-outline-light btn-lg px-4 py-2 w-100" data-bs-toggle="modal" data-bs-target="#directPayModal">
                                        <i class="bi bi-send me-2"></i>
                                        Direct Pay
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 text-center d-none d-lg-block">
                            <div class="hero-illustration">
                                <i class="bi bi-shield-check display-1 text-white opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>
</div>`;
};

export const getStatsContentTemplate = (activeVouchers, redeemedVouchers, expiredVouchers) => `
<div class="col-lg-12 col-md-12 col-sm-12 mb-4">
    <div class="card border-0 shadow-lg bg-gradient" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="card-header border-0 py-4 bg-transparent">
            <div class="d-flex justify-content-between align-items-center">
                <button class="btn btn-link text-white p-0 text-decoration-none d-flex align-items-center w-100" type="button" data-bs-toggle="collapse" data-bs-target="#collapseVoucherAnalytics" aria-expanded="false" aria-controls="collapseVoucherAnalytics">
                    <div class="d-flex align-items-center flex-grow-1">
                        <i class="bi bi-graph-up me-3 fs-4"></i>
                        <div>
                            <h4 class="card-title mb-1 fw-bold text-white">Analytics</h4>
                            <p class="text-white-50 mb-0 fs-6">Real-time insights</p>
                        </div>
                    </div>
                    <div class="d-flex align-items-center gap-3">
                        <span class="badge bg-white bg-opacity-20 text-white px-3 py-2 rounded-pill">
                            <i class="bi bi-clock me-1"></i>
                            Live Data
                        </span>
                        <i class="bi bi-chevron-down text-white fs-5"></i>
                    </div>
                </button>
            </div>
        </div>
        <div id="collapseVoucherAnalytics" class="collapse">
            <div class="card-body pt-0">
            <div class="row g-4">
                <!-- Active Vouchers Card -->
                <div class="col-lg-4 col-md-6">
                    <div class="stat-card bg-white bg-opacity-15 backdrop-blur rounded-4 p-4 h-100 border border-white border-opacity-20 hover-lift">
                        <div class="d-flex align-items-start justify-content-between mb-4">
                            <div class="stat-icon-wrapper">
                                <div class="stat-icon bg-success bg-opacity-20 rounded-3 p-3 d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                    <i class="bi bi-check-circle-fill text-white fs-3"></i>
                                </div>
                            </div>
                            <div class="trend-indicator">
                                <span class="badge bg-success bg-opacity-30 text-white px-2 py-1 rounded-pill small">
                                    <i class="bi bi-arrow-up me-1"></i>Active
                                </span>
                            </div>
                        </div>
                        <div class="stat-content">
                            <h2 class="fw-bold mb-2 text-white display-6">${activeVouchers}</h2>
                            <p class="text-white-75 mb-1 fw-medium">Active Vouchers</p>
                            <p class="text-white-50 small mb-0">Ready for redemption</p>
                        </div>
                        <div class="stat-footer mt-3 pt-3 border-top border-white border-opacity-20">
                            <small class="text-white-50">
                                <i class="bi bi-info-circle me-1"></i>
                                Currently available
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Redeemed Vouchers Card -->
                <div class="col-lg-4 col-md-6">
                    <div class="stat-card bg-white bg-opacity-15 backdrop-blur rounded-4 p-4 h-100 border border-white border-opacity-20 hover-lift">
                        <div class="d-flex align-items-start justify-content-between mb-4">
                            <div class="stat-icon-wrapper">
                                <div class="stat-icon bg-primary bg-opacity-20 rounded-3 p-3 d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                    <i class="bi bi-arrow-repeat text-white fs-3"></i>
                                </div>
                            </div>
                            <div class="trend-indicator">
                                <span class="badge bg-primary bg-opacity-30 text-white px-2 py-1 rounded-pill small">
                                    <i class="bi bi-check me-1"></i>Complete
                                </span>
                            </div>
                        </div>
                        <div class="stat-content">
                            <h2 class="fw-bold mb-2 text-white display-6">${redeemedVouchers}</h2>
                            <p class="text-white-75 mb-1 fw-medium">Redeemed Vouchers</p>
                            <p class="text-white-50 small mb-0">Successfully processed</p>
                        </div>
                        <div class="stat-footer mt-3 pt-3 border-top border-white border-opacity-20">
                            <small class="text-white-50">
                                <i class="bi bi-graph-up me-1"></i>
                                Revenue generated
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Expired Vouchers Card -->
                <div class="col-lg-4 col-md-6">
                    <div class="stat-card bg-white bg-opacity-15 backdrop-blur rounded-4 p-4 h-100 border border-white border-opacity-20 hover-lift">
                        <div class="d-flex align-items-start justify-content-between mb-4">
                            <div class="stat-icon-wrapper">
                                <div class="stat-icon bg-warning bg-opacity-20 rounded-3 p-3 d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                    <i class="bi bi-clock-history text-white fs-3"></i>
                                </div>
                            </div>
                            <div class="trend-indicator">
                                <span class="badge bg-warning bg-opacity-30 text-white px-2 py-1 rounded-pill small">
                                    <i class="bi bi-exclamation-triangle me-1"></i>Expired
                                </span>
                            </div>
                        </div>
                        <div class="stat-content">
                            <h2 class="fw-bold mb-2 text-white display-6">${expiredVouchers}</h2>
                            <p class="text-white-75 mb-1 fw-medium">Expired Vouchers</p>
                            <p class="text-white-50 small mb-0">Past redemption date</p>
                        </div>
                        <div class="stat-footer mt-3 pt-3 border-top border-white border-opacity-20">
                            <small class="text-white-50">
                                <i class="bi bi-calendar-x me-1"></i>
                                No longer valid
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Summary Row -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="summary-card bg-white bg-opacity-10 rounded-3 p-4 border border-white border-opacity-20">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h6 class="text-white mb-2 fw-semibold">
                                    <i class="bi bi-pie-chart me-2"></i>
                                    Total Vouchers Overview
                                </h6>
                                <p class="text-white-50 mb-0 small">
                                    Comprehensive view of all voucher activities across your merchant account
                                </p>
                            </div>
                            <div class="col-md-4 text-md-end">
                                <div class="d-flex justify-content-md-end justify-content-start align-items-center mt-3 mt-md-0">
                                    <div class="total-count me-4">
                                        <span class="text-white-50 small d-block">Total</span>
                                        <span class="text-white fw-bold fs-4">${parseInt(activeVouchers) + parseInt(redeemedVouchers) + parseInt(expiredVouchers)}</span>
                                    </div>
                                    <button class="btn btn-outline-light btn-sm rounded-pill px-3">
                                        <i class="bi bi-arrow-right me-1"></i>
                                        View Details
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </div>
    </div>
</div>

<style>
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.backdrop-blur {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.text-white-75 {
    color: rgba(255, 255, 255, 0.75) !important;
}

.text-white-50 {
    color: rgba(255, 255, 255, 0.5) !important;
}

.stat-card {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover::before {
    opacity: 1;
}

.summary-card {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Collapsible Analytics Styling */
[data-bs-toggle="collapse"] .bi-chevron-down {
    transition: transform 0.3s ease;
}

[data-bs-toggle="collapse"][aria-expanded="false"] .bi-chevron-down {
    transform: rotate(-90deg);
}

[data-bs-toggle="collapse"]:hover {
    opacity: 0.9;
}

/* Pagination Styling */
.btn-outline-secondary {
    border-color: rgba(255, 255, 255, 0.3);
    color: rgba(255, 255, 255, 0.8);
}

.btn-outline-secondary:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
}

.btn-outline-secondary:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

@media (max-width: 768px) {
    .display-6 {
        font-size: 2rem;
    }

    .stat-icon {
        width: 50px !important;
        height: 50px !important;
    }

    .stat-icon i {
        font-size: 1.5rem !important;
    }

    /* Mobile pagination */
    .btn-sm {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
}
</style>`;

export const newTransactionModalTemplate = () => `
<div class="modal fade newTransaction" id="newTransactionModal" tabindex="-1" role="dialog" aria-labelledby="newTransactionModalTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-white" id="newTransactionModalTitle">
                    <i class="bi bi-plus-circle me-2"></i>New Payment
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-4" id="merchant-selects-container">
                    <div class="input-group">
                        <span class="input-group-text"  style="min-width: auto;"><i class="bi bi-shop me-2 text-primary"></i></span>
                        <select class="form-control" id="merchants">
                            <option value="">Select Merchant</option>
                        </select>
                    </div>
                </div>
                <div class="mb-4" id="branch-selects-container">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-building me-2 text-primary"></i></span>
                        <select class="form-control" id="branches" disabled>
                            <option value="">Select Branch</option>
                        </select>
                    </div>
                </div>
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-currency-exchange me-2 text-primary"></i></span>
                        <input type="number" id="amount" class="form-control" placeholder="Enter Amount">
                    </div>
                </div>
                <div class="mb-4" id="pay_from_div">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-wallet2 me-2 text-primary"></i></span>
                        <select class="form-control" id="pay_from">
                            <option value="">Payment Platform</option>
                            <option value="MTN_MOMO">MTN MoMo</option>
                            <option value="INSTACASH" disabled>InstaCash (Coming Soon)</option>
                            <option value="UNAYO" disabled>Unayo (Coming Soon)</option>
                            <option value="DELTAPAY" disabled>DeltaPay (Coming Soon)</option>
                            <option value="EMALI" disabled>E-mali (Coming Soon)</option>
                        </select>
                    </div>
                </div>
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-receipt me-2 text-primary"></i></span>
                        <input type="text" id="default_ref" class="form-control" placeholder="Reference">
                    </div>
                </div>
                <div class="accordion mb-2" id="advancedOptionsAccordion">
                    <div class="accordion-item border-0">
                        <h2 class="accordion-header" id="advancedOptionsHeading">
                            <button class="accordion-button collapsed bg-light py-2" type="button" data-bs-toggle="collapse"
                                data-bs-target="#advancedOptionsCollapse" aria-expanded="false" aria-controls="advancedOptionsCollapse" style="min-height: 48px;">
                                <div class="d-flex align-items-center">
                                    <span class="input-group-text bg-transparent border-0 p-0 me-2" style="height: 38px;">
                                        <i class="bi bi-ui-checks-grid text-primary"></i>
                                    </span>
                                    <div>
                                        <span class="fw-bold d-block text-white">Additional Details</span>
                                    </div>
                                </div>
                            </button>
                        </h2>
                        <div id="advancedOptionsCollapse" class="accordion-collapse collapse"
                            aria-labelledby="advancedOptionsHeading" data-bs-parent="#advancedOptionsAccordion">
                            <div class="accordion-body">
                                <div class="mb-4" id="reference-container"></div>
                                <button type="button" class="btn btn-outline-warning w-100 mt-2" id="addReference">
                                    <i class="bi bi-plus-circle me-2"></i>Add Extra Reference
                                </button>
                                ${attachmentHandlers.attachmentInputTemplate()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer flex-column flex-sm-row gap-2">
                <button type="button" class="btn btn-primary w-100 w-sm-auto d-flex align-items-center justify-content-center" id="confirm_transaction">
                    <i class="bi bi-check-circle-fill me-2"></i>
                    <span>Review & Continue</span>
                </button>
                <button type="button" class="btn btn-warning w-100 w-sm-auto d-flex align-items-center justify-content-center" id="confirm_direct_pay">
                    <i class="bi bi-check-circle-fill me-2"></i>
                    <span>Review & Continue</span>
                </button>
            </div>
        </div>
    </div>
</div>`;

export const newPublicReceiverTransactionModalTemplate = () => `
<div class="modal fade newTransaction" id="payReceiverModal" tabindex="-1" role="dialog" aria-labelledby="payReceiverModalTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-white" id="payReceiverModalTitle">
                    <i class="bi bi-plus-circle me-2"></i>Public Pay
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="receiver-id" class="d-none">
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-currency-exchange me-2 text-primary"></i></span>
                        <input type="number" id="amount-p" class="form-control" placeholder="Enter Amount">
                    </div>
                </div>
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-wallet2 me-2 text-primary"></i></span>
                        <select class="form-control" id="pay_from-p">
                            <option value="">Payment Platform</option>
                            <option value="MTN_MOMO">MTN MoMo</option>
                            <option value="INSTACASH" disabled>InstaCash (Coming Soon)</option>
                            <option value="UNAYO" disabled>Unayo (Coming Soon)</option>
                            <option value="DELTAPAY" disabled>DeltaPay (Coming Soon)</option>
                            <option value="EMALI" disabled>E-mali (Coming Soon)</option>
                        </select>
                    </div>
                </div>
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-receipt me-2 text-primary"></i></span>
                        <input type="text" id="default_ref-p" class="form-control" placeholder="Reference">
                    </div>
                </div>
                <div class="accordion mb-2" id="advancedOptionsAccordion2">
                    <div class="accordion-item border-0">
                        <h2 class="accordion-header" id="advancedOptionsHeading2">
                            <button class="accordion-button collapsed bg-light py-2" type="button" data-bs-toggle="collapse"
                                data-bs-target="#advancedOptionsCollapse2" aria-expanded="false" aria-controls="advancedOptionsCollapse2" style="min-height: 48px;">
                                <div class="d-flex align-items-center">
                                    <span class="input-group-text bg-transparent border-0 p-0 me-2" style="height: 38px;">
                                        <i class="bi bi-ui-checks-grid text-primary"></i>
                                    </span>
                                    <div>
                                        <span class="fw-bold d-block text-white">Additional Details</span>
                                    </div>
                                </div>
                            </button>
                        </h2>
                        <div id="advancedOptionsCollapse2" class="accordion-collapse collapse"
                            aria-labelledby="advancedOptionsHeading2" data-bs-parent="#advancedOptionsAccordion2">
                            <div class="accordion-body">
                                <div class="mb-4" id="public-reference-container"></div>
                                <button type="button" class="btn btn-outline-warning w-100 mt-2" id="addPublicReference">
                                    <i class="bi bi-plus-circle me-2"></i>Add Extra Reference
                                </button>
                                ${attachmentHandlers.attachmentInputTemplate()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer flex-column flex-sm-row gap-2">
                <button type="button" class="btn btn-primary w-100 w-sm-auto d-flex align-items-center justify-content-center" id="confirm-public-pay">
                    <i class="bi bi-check-circle me-2"></i>
                    <span>Confirm Payment Details</span>
                </button>
            </div>
        </div>
    </div>
</div>`;

export const newDirectPayModalTemplate = () => `
<div class="modal fade newTransaction" id="directPayModal" tabindex="-1" role="dialog" aria-labelledby="payReceiverModalTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-white" id="directPayModalTitle">
                    <i class="bi bi-plus-circle me-2"></i>Direct Pay
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-buildings me-2 text-primary"></i></span>
                        <input type="number" id="merchant_code-d" class="form-control" placeholder="Merchant Code (4-8 digits)" min="1000" max="99999999" pattern="[0-9]{4,8}" title="Enter a 4-8 digit merchant code">
                    </div>
                    <div class="form-text text-white-50 small mt-1">
                        <i class="bi bi-info-circle me-1"></i>
                        Enter a 4-6 digit merchant code or a phone number
                    </div>
                </div>
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-currency-exchange me-2 text-primary"></i></span>
                        <input type="number" id="amount-d" class="form-control" placeholder="Enter Amount">
                    </div>
                </div>
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-wallet2 me-2 text-primary"></i></span>
                        <select class="form-control" id="pay_from-d">
                            <option value="">Payment Platform</option>
                            <option value="MTN_MOMO">MTN MoMo</option>
                            <option value="INSTACASH" disabled>InstaCash (Coming Soon)</option>
                            <option value="UNAYO" disabled>Unayo (Coming Soon)</option>
                            <option value="DELTAPAY" disabled>DeltaPay (Coming Soon)</option>
                            <option value="EMALI" disabled>E-mali (Coming Soon)</option>
                        </select>
                    </div>
                </div>
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-receipt me-2 text-primary"></i></span>
                        <input type="text" id="default_ref-d" class="form-control" placeholder="Reference">
                    </div>
                </div>
                <div class="accordion mb-2" id="advancedOptionsAccordion2">
                    <div class="accordion-item border-0">
                        <h2 class="accordion-header" id="advancedOptionsHeading2">
                            <button class="accordion-button collapsed bg-light py-2" type="button" data-bs-toggle="collapse"
                                data-bs-target="#advancedOptionsCollapse2" aria-expanded="false" aria-controls="advancedOptionsCollapse2" style="min-height: 48px;">
                                <div class="d-flex align-items-center">
                                    <span class="input-group-text bg-transparent border-0 p-0 me-2" style="height: 38px;">
                                        <i class="bi bi-ui-checks-grid text-primary"></i>
                                    </span>
                                    <div>
                                        <span class="fw-bold d-block text-white">Additional Details</span>
                                    </div>
                                </div>
                            </button>
                        </h2>
                        <div id="advancedOptionsCollapse2" class="accordion-collapse collapse"
                            aria-labelledby="advancedOptionsHeading2" data-bs-parent="#advancedOptionsAccordion2">
                            <div class="accordion-body">
                                <div class="mb-4" id="direct-reference-container"></div>
                                <button type="button" class="btn btn-outline-warning w-100 mt-2" id="addDirectReference">
                                    <i class="bi bi-plus-circle me-2"></i>Add Extra Reference
                                </button>
                                ${attachmentHandlers.attachmentInputTemplate()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer flex-column flex-sm-row gap-2">
                <button type="button" class="btn btn-primary w-100 w-sm-auto d-flex align-items-center justify-content-center" id="confirm-direct-pay">
                    <i class="bi bi-check-circle me-2"></i>
                    <span>Confirm Payment Details</span>
                </button>
            </div>
        </div>
    </div>
</div>`;

export const confirmTransactionModalTemplate = (
    paymentType,
    merchantID,
    amount,
    charge,
    total,
    payFrom,
    defReference,
    extraRefsHTML,
    attachmentData
) => `
<div class="modal fade newTransaction" id="confirmTransactionModal" tabindex="-1" role="dialog" aria-labelledby="confirmTransactionModalTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-light">
                <h5 class="modal-title text-white" id="confirmTransactionModalTitle">
                    <i class="bi bi-shield-check me-2"></i>Confirm Payment Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="c_merchant" value="${merchantID}">
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-wallet2 me-2 text-primary"></i></span>
                        <input type="text" id="c_pay_from" class="form-control fw-bold" value="${payFrom}" readonly>
                    </div>
                </div>
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-currency-exchange me-2 text-primary"></i></span>
                        <input type="text" id="c_amount" class="form-control" value="${amount}" readonly>
                    </div>
                </div>
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-percent me-2 text-primary"></i></span>
                        <input type="text" id="c_charge" class="form-control" value="${charge}" readonly>
                    </div>
                </div>
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-clipboard-check me-2 text-primary"></i></span>
                        <input type="text" id="c_total" class="form-control bg-primary text-white fw-bold" value="${total}" readonly>
                    </div>
                </div>
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-receipt me-2 text-primary"></i></span>
                        <input type="text" id="c_default_ref" class="form-control" value="${defReference}" readonly>
                    </div>
                </div>
                ${extraRefsHTML}
                ${attachmentData ? `
                    <div class="mb-4">
                        <label class="form-label d-flex align-items-center">
                            <i class="bi bi-paperclip me-2 text-primary"></i>
                            <span>ATTACHMENT</span>
                        </label>
                        <div class="card border-primary">
                            <div class="card-body p-3">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-file-earmark me-2 text-primary"></i>
                                    <span class="flex-grow-1 text-truncate">${attachmentData.name}</span>
                                </div>
                                ${attachmentData.type.startsWith('image/') ? `
                                    <img src="${attachmentData.preview}" class="mt-3 img-fluid rounded" style="max-height: 200px; width: auto;" alt="Preview">
                                ` : ''}
                            </div>
                        </div>
                    </div>
                ` : ''}
            </div>
            <div class="modal-footer flex-column flex-sm-row gap-2">
                <button type="button"
                    class="btn btn-outline-primary w-100 w-sm-auto d-flex align-items-center justify-content-center text-white"
                    data-bs-dismiss="modal"
                    data-bs-toggle="modal"
                    data-bs-target="#newTransactionModal">
                    <i class="bi bi-pencil me-2"></i>
                    <span>Edit</span>
                </button>
                ${paymentType === 'voucher' ? `
                <button type="button" class="btn btn-primary w-100 w-sm-auto d-flex align-items-center justify-content-center" id="authorize_transaction">
                    <i class="bi bi-shield-lock me-2"></i>
                    <span>Confirm & Create Voucher</span>
                </button>
                ` : ''}
                ${paymentType === 'direct_pay' ? `
                <button type="button" class="btn btn-warning w-100 w-sm-auto d-flex align-items-center justify-content-center" id="pay_now">
                    <i class="bi bi-lightning-charge me-2"></i>
                    <span>Confirm & Pay With MTN MoMo</span>
                </button>
                ` : ''}
            </div>
        </div>
    </div>
</div>`;

export const confirmPublicPayModalTemplate = (receiverID, amount, payFrom, charge, total, defReference, extraRefsHTML, attachmentData) => `
<div class="modal fade newTransaction" id="confirmPublicPayModal" tabindex="-1" role="dialog" aria-labelledby="confirmPublicPayModalTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-light">
                <h5 class="modal-title text-white" id="confirmPublicPayModalTitle">
                    <i class="bi bi-shield-check me-2"></i>Confirm Public Pay
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="confirm-receiver-id" value="${receiverID}">
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-wallet2 me-2 text-primary"></i></span>
                        <input type="text" id="confirm-payfrom-p" class="form-control fw-bold" value="${payFrom}" readonly>
                    </div>
                </div>
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-currency-exchange me-2 text-primary"></i></span>
                        <input type="text" id="confirm-amount-p" class="form-control" value="${amount}" readonly>
                    </div>
                </div>
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-percent me-2 text-primary"></i></span>
                        <input type="text" id="confirm-charge-p" class="form-control" value="${charge}" readonly>
                    </div>
                </div>
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-clipboard-check me-2 text-primary"></i></span>
                        <input type="text" id="confirm-total-p" class="form-control bg-primary text-white fw-bold" value="${total}" readonly>
                    </div>
                </div>
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-receipt me-2 text-primary"></i></span>
                        <input type="text" id="confirm-ref-p" class="form-control" value="${defReference}" readonly>
                    </div>
                </div>
                ${extraRefsHTML}
                ${attachmentData ? `
                    <div class="mb-4">
                        <label class="form-label d-flex align-items-center">
                            <i class="bi bi-paperclip me-2 text-primary"></i>
                            <span>ATTACHMENT</span>
                        </label>
                        <div class="card border-primary">
                            <div class="card-body p-3">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-file-earmark me-2 text-primary"></i>
                                    <span class="flex-grow-1 text-truncate">${attachmentData.name}</span>
                                </div>
                                ${attachmentData.type.startsWith('image/') ? `
                                    <img src="${attachmentData.preview}" class="mt-3 img-fluid rounded" style="max-height: 200px; width: auto;" alt="Preview">
                                ` : ''}
                            </div>
                        </div>
                    </div>
                ` : ''}
            </div>
            <div class="modal-footer flex-column flex-sm-row gap-2">
                <button type="button"
                    class="btn btn-outline-primary w-100 w-sm-auto d-flex align-items-center justify-content-center text-white"
                    data-bs-dismiss="modal"
                    data-bs-toggle="modal"
                    data-bs-target="#payReceiverModal">
                    <i class="bi bi-pencil me-2"></i>
                    <span>Edit</span>
                </button>
                <button type="button" class="btn btn-primary w-100 w-sm-auto d-flex align-items-center justify-content-center" id="initiate-public-pay">
                    <i class="bi bi-shield-lock me-2"></i>
                    <span>Confirm & Pay</span>
                </button>
            </div>
        </div>
    </div>
</div>`;

export const confirmDirectPayModalTemplate = (merchantCode, amount, payFrom, charge, total, defReference, extraRefsHTML, attachmentData) => `
<div class="modal fade newTransaction" id="confirmDirectPayModal" tabindex="-1" role="dialog" aria-labelledby="confirmPublicPayModalTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-light">
                <h5 class="modal-title text-white" id="confirmDirectPayModalTitle">
                    <i class="bi bi-shield-check me-2"></i>Confirm Direct Payment
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="confirm-merchant-code-d" value="${merchantCode}">
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-wallet2 me-2 text-primary"></i></span>
                        <input type="text" id="confirm-payfrom-d" class="form-control fw-bold" value="${payFrom}" readonly>
                    </div>
                </div>
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-currency-exchange me-2 text-primary"></i></span>
                        <input type="text" id="confirm-amount-d" class="form-control" value="${amount}" readonly>
                    </div>
                </div>
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-percent me-2 text-primary"></i></span>
                        <input type="text" id="confirm-charge-d" class="form-control" value="${charge}" readonly>
                    </div>
                </div>
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-clipboard-check me-2 text-primary"></i></span>
                        <input type="text" id="confirm-total-d" class="form-control bg-primary text-white fw-bold" value="${total}" readonly>
                    </div>
                </div>
                <div class="mb-4">
                    <div class="input-group">
                        <span class="input-group-text" style="min-width: auto;"><i class="bi bi-receipt me-2 text-primary"></i></span>
                        <input type="text" id="confirm-ref-d" class="form-control" value="${defReference}" readonly>
                    </div>
                </div>
                ${extraRefsHTML}
                ${attachmentData ? `
                    <div class="mb-4">
                        <label class="form-label d-flex align-items-center">
                            <i class="bi bi-paperclip me-2 text-primary"></i>
                            <span>ATTACHMENT</span>
                        </label>
                        <div class="card border-primary">
                            <div class="card-body p-3">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-file-earmark me-2 text-primary"></i>
                                    <span class="flex-grow-1 text-truncate">${attachmentData.name}</span>
                                </div>
                                ${attachmentData.type.startsWith('image/') ? `
                                    <img src="${attachmentData.preview}" class="mt-3 img-fluid rounded" style="max-height: 200px; width: auto;" alt="Preview">
                                ` : ''}
                            </div>
                        </div>
                    </div>
                ` : ''}
            </div>
            <div class="modal-footer flex-column flex-sm-row gap-2">
                <button type="button"
                    class="btn btn-outline-primary w-100 w-sm-auto d-flex align-items-center justify-content-center text-white"
                    data-bs-dismiss="modal"
                    data-bs-toggle="modal"
                    data-bs-target="#directPayModal">
                    <i class="bi bi-pencil me-2"></i>
                    <span>Edit</span>
                </button>
                <button type="button" class="btn btn-primary w-100 w-sm-auto d-flex align-items-center justify-content-center" id="initiate-direct-pay">
                    <i class="bi bi-shield-lock me-2"></i>
                    <span>Confirm & Pay</span>
                </button>
            </div>
        </div>
    </div>
</div>`;

export const successModalTemplate = (title = "Success", paymentInfo) => `
<div class="modal fade" id="successModal" tabindex="-1" role="dialog" aria-labelledby="successModalTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-secondary" id="successModalTitle">
                    <i class="bi bi-check-circle-fill me-2 text-success"></i>${title}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>${paymentInfo}</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>`;

export const mainPageLayoutTemplate = (headerHtml, sidebarHtml, mainContentHtml, newTransactionModalHtml, newPublicReceiverModalHtml, newDirectPayModalHtml, publicReceiversHtml, quickHelpHtml, statsContentHtml, transactionsHtml) => `
${headerHtml}
${sidebarHtml}
<div id="app-content">
    <div class="app-content-area">
        <div class="container-fluid">
            ${mainContentHtml}
            ${newTransactionModalHtml}
            ${newPublicReceiverModalHtml}
            ${newDirectPayModalHtml}
            <div class="row mt-5">
                ${publicReceiversHtml}
                ${quickHelpHtml}
                ${statsContentHtml}
                ${transactionsHtml}
            </div>
        </div>
    </div>
</div>
`;
