import Alert from '../components/alert.js';
import ApiService from '../services/api.js';
import * as Templates from './homeUiTemplates.js';
import { attachmentHandlers } from '../utils/attachmentHandlers.js';

// Utility function to safely hide modals and prevent memory leaks
function safeHideModal(modalId) {
    return new Promise((resolve) => {
        const modalElement = document.getElementById(modalId);
        if (!modalElement) {
            resolve();
            return;
        }

        const bsModal = bootstrap.Modal.getInstance(modalElement);
        if (!bsModal) {
            resolve();
            return;
        }

        // Add one-time listener for when modal is fully hidden
        modalElement.addEventListener('hidden.bs.modal', () => {
            resolve();
        }, { once: true });

        // Hide the modal
        bsModal.hide();
    });
}

// Helper to safely add event listeners, replacing old ones to avoid duplicates
function replaceAndAddListener(elementId, eventType, handler) {
    const element = document.getElementById(elementId);
    if (element) {
        // Store reference to handler for cleanup
        if (!element._eventHandlers) {
            element._eventHandlers = new Map();
        }

        // Remove existing handler if it exists
        const existingHandler = element._eventHandlers.get(eventType);
        if (existingHandler) {
            element.removeEventListener(eventType, existingHandler);
        }

        // Add new handler
        element.addEventListener(eventType, handler);
        element._eventHandlers.set(eventType, handler);

        return element;
    }
    console.warn(`Element with ID '${elementId}' not found for event listener.`);
    return null;
}

// Function to clean up all event listeners on an element
function cleanupEventListeners(element) {
    if (element && element._eventHandlers) {
        element._eventHandlers.forEach((handler, eventType) => {
            element.removeEventListener(eventType, handler);
        });
        element._eventHandlers.clear();
    }
}

// Helper to collect values from dynamically added reference inputs
function collectExtraReferences(modalElement) {
    if (!modalElement) {
        console.warn('Modal element not provided to collect references');
        return [];
    }

    const refs = [];
    modalElement.querySelectorAll(".reference-input").forEach(input => {
        if (input.value.trim() !== '') {
            refs.push({
                id: input.id,
                label: input.previousElementSibling && input.previousElementSibling.tagName === 'LABEL' 
                    ? input.previousElementSibling.textContent 
                    : input.placeholder,
                value: input.value.trim()
            });
        }
    });
    return refs;
}

// Helper to generate HTML for displaying collected references (read-only)
function generateExtraRefsHTML(extraRefs) {
    let extraRefsHTML = "";
    extraRefs.forEach((refObj, index) => {
        const labelText = refObj.label || `REFERENCE ${index + 1}`;
        extraRefsHTML += `
            <div class="mb-3">
                <label class="form-label" for="${refObj.id}-readonly">${labelText}</label>
                <input type="text" id="${refObj.id}-readonly" class="form-control" value="${refObj.value}" readonly>
            </div>
        `;
    });
    return extraRefsHTML;
}

// Generic modal display function with improved error handling
function displayModal(modalId, modalHTML, onShownCallback, onHiddenCallback) {
    return new Promise((resolve, reject) => {
        try {
            const existingModal = document.getElementById(modalId);
            if (existingModal) {
                const bsModal = bootstrap.Modal.getInstance(existingModal);
                if (bsModal) {
                    // Wait for modal to be fully hidden before proceeding
                    existingModal.addEventListener('hidden.bs.modal', () => {
                        existingModal.remove();
                        createNewModal();
                    }, { once: true });
                    bsModal.hide();
                } else {
                    existingModal.remove();
                    createNewModal();
                }
            } else {
                createNewModal();
            }

            function createNewModal() {
                try {
                    document.body.insertAdjacentHTML('beforeend', modalHTML);
                    const modalElement = document.getElementById(modalId);
                    if (!modalElement) {
                        console.error(`Modal element with ID '${modalId}' not found after insertion.`);
                        reject(new Error(`Modal element with ID '${modalId}' not found`));
                        return;
                    }

                    const modal = new bootstrap.Modal(modalElement, {
                        backdrop: 'static',
                        keyboard: false
                    });

                    if (onShownCallback) {
                        modalElement.addEventListener('shown.bs.modal', () => {
                            try {
                                onShownCallback();
                            } catch (error) {
                                console.error('Error in onShownCallback:', error);
                            }
                        }, { once: true });
                    }

                    if (onHiddenCallback) {
                        modalElement.addEventListener('hidden.bs.modal', () => {
                            try {
                                onHiddenCallback();
                            } catch (error) {
                                console.error('Error in onHiddenCallback:', error);
                            } finally {
                                // Use setTimeout to ensure DOM cleanup happens after all events
                                setTimeout(() => {
                                    if (modalElement && modalElement.parentNode) {
                                        modalElement.remove();
                                    }
                                }, 100);
                            }
                        }, { once: true });
                    } else {
                        modalElement.addEventListener('hidden.bs.modal', () => {
                            setTimeout(() => {
                                if (modalElement && modalElement.parentNode) {
                                    modalElement.remove();
                                }
                            }, 100);
                        }, { once: true });
                    }

                    modal.show();
                    resolve(modal);
                } catch (error) {
                    console.error('Error creating modal:', error);
                    reject(error);
                }
            }
        } catch (error) {
            console.error('Error in displayModal:', error);
            reject(error);
        }
    });
}

// --- Event Listener Initialization ---
export function initializeEventListeners(viewInstance) {
    replaceAndAddListener('new_transaction', 'click', () => {
        const modalElement = document.getElementById('newTransactionModal');
        if (modalElement) {
            const modal = bootstrap.Modal.getOrCreateInstance(modalElement);
            modal.show();
        }
    });

    replaceAndAddListener('confirm_transaction', 'click', (e) => viewInstance.createTransactionStepOne(e, "voucher"));
    replaceAndAddListener('confirm_direct_pay', 'click', (e) => viewInstance.createTransactionStepOne(e, "direct_pay"));
    //replaceAndAddListener('pay_now', 'click', (e) => viewInstance.payNowWithMOMO(e));

    // Dynamic "Add Reference" buttons
    let referenceCount = 1;
    const addReferenceBtn = document.getElementById("addReference");
    const referenceContainer = document.getElementById("reference-container");
    if (addReferenceBtn && referenceContainer) {
        // Use a new, cloned button to ensure old listeners are removed if this function is called multiple times
        const newAddReferenceBtn = addReferenceBtn.cloneNode(true);
        addReferenceBtn.parentNode.replaceChild(newAddReferenceBtn, addReferenceBtn);
        newAddReferenceBtn.addEventListener("click", () => {
            const inputId = `ref_${referenceCount}`;
            const newRefInput = document.createElement("input");
            newRefInput.type = "text";
            newRefInput.className = "form-control reference-input mt-2"; // Common class for collection
            newRefInput.placeholder = `Reference ${referenceCount}`;
            newRefInput.id = inputId;
            referenceContainer.appendChild(newRefInput);
            referenceCount++;
        });
    }


    let pReferenceCount = 1;
    const addPReferenceBtn = document.getElementById("addPublicReference");
    const pReferenceContainer = document.getElementById("public-reference-container");
    if (addPReferenceBtn && pReferenceContainer) {
         const newAddPReferenceBtn = addPReferenceBtn.cloneNode(true);
         addPReferenceBtn.parentNode.replaceChild(newAddPReferenceBtn, addPReferenceBtn);
         newAddPReferenceBtn.addEventListener("click", () => {
            const inputId = `public_ref_${pReferenceCount}`;
            const newRefInput = document.createElement("input");
            newRefInput.type = "text";
            newRefInput.className = "form-control reference-input mt-2"; // Common class
            newRefInput.placeholder = `Reference ${pReferenceCount}`;
            newRefInput.id = inputId;
            pReferenceContainer.appendChild(newRefInput);
            pReferenceCount++;
        });
    }

    let dReferenceCount = 1;
    const addDReferenceBtn = document.getElementById("addDirectReference");
    const dReferenceContainer = document.getElementById("direct-reference-container");
    if (addDReferenceBtn && dReferenceContainer) {
         const newAddDReferenceBtn = addDReferenceBtn.cloneNode(true);
         addDReferenceBtn.parentNode.replaceChild(newAddDReferenceBtn, addDReferenceBtn);
         newAddDReferenceBtn.addEventListener("click", () => {
            const inputId = `direct_ref_${dReferenceCount}`;
            const newRefInput = document.createElement("input");
            newRefInput.type = "text";
            newRefInput.className = "form-control reference-input mt-2"; // Common class
            newRefInput.placeholder = `Reference ${dReferenceCount}`;
            newRefInput.id = inputId;
            dReferenceContainer.appendChild(newRefInput);
            dReferenceCount++;
        });
    }

    // Initialize handlers when modals are shown
    document.getElementById('newTransactionModal')?.addEventListener('shown.bs.modal', function() {
        attachmentHandlers.initializeAttachmentHandlers(this);
    });

    document.getElementById('payReceiverModal')?.addEventListener('shown.bs.modal', function() {
        attachmentHandlers.initializeAttachmentHandlers(this);
    });

    document.getElementById('directPayModal')?.addEventListener('shown.bs.modal', function() {
        attachmentHandlers.initializeAttachmentHandlers(this);

        // Add real-time validation for merchant code
        const merchantCodeInput = document.getElementById('merchant_code-d');
        if (merchantCodeInput) {
            merchantCodeInput.addEventListener('input', function(e) {
                // Remove any non-numeric characters
                let value = e.target.value.replace(/\D/g, '');

                // Limit to 8 digits maximum
                if (value.length > 8) {
                    value = value.substring(0, 8);
                }

                e.target.value = value;

                // Visual feedback
                const isValid = value.length >= 4 && value.length <= 8;
                if (value.length > 0) {
                    if (isValid) {
                        e.target.classList.remove('is-invalid');
                        e.target.classList.add('is-valid');
                    } else {
                        e.target.classList.remove('is-valid');
                        e.target.classList.add('is-invalid');
                    }
                } else {
                    e.target.classList.remove('is-valid', 'is-invalid');
                }
            });

            // Prevent paste of non-numeric content
            merchantCodeInput.addEventListener('paste', function(e) {
                setTimeout(() => {
                    // Clean the input after paste
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.length > 8) {
                        value = value.substring(0, 8);
                    }
                    e.target.value = value;
                    e.target.dispatchEvent(new Event('input'));
                }, 0);
            });
        }
    });

    replaceAndAddListener('confirm-public-pay', 'click', (e) => {
        if (validatePublicReceiverPayment(e)) {
            showConfirmPublicReceiverPaymentModal(viewInstance);
        }
    });

    replaceAndAddListener('confirm-direct-pay', 'click', (e) => {
        if (validateDirectPayment(e)) {
            showConfirmDirectPaymentModal(viewInstance);
        }
    });
}

// --- UI Update Functions ---
export async function setGreeting() {
    let username = '';
    try {
        const loginPhone = localStorage.getItem('login_phone');
        if (loginPhone) {
            const response = await ApiService.getCustomerName(loginPhone);
            const fullName = response.name || '';
            if (fullName !== '') {
                const firstName = fullName.trim().split(' ')[0];
                username = firstName.charAt(0).toUpperCase() + firstName.slice(1).toLowerCase();
            }
        }
    } catch (err) {
        console.warn("Could not fetch customer name for greeting:", err);
    }

    const hour = new Date().getHours();
    const greetingText = hour < 12 ? "Good morning" : hour < 18 ? "Good afternoon" : "Good evening";
    const greetingElement = document.getElementById("greeting");
    if (greetingElement) {
        greetingElement.textContent = `${greetingText} ${username}`;
    }
}

export async function initializeMerchantData() {
    const merchantSelect = document.getElementById('merchants');
    const branchSelect = document.getElementById('branches');
    const merchantContainer = document.getElementById('merchant-selects-container');

    if (!merchantSelect || !branchSelect || !merchantContainer) {
        console.warn("Merchant/Branch select elements not found.");
        return;
    }

    try {
        const merchantData = await ApiService.getMerchants();
        const merchants = merchantData.merchants || [];

        merchantSelect.innerHTML = `<option value="">Select Merchant</option>`; // Reset
        merchants.forEach(merchant => {
            const option = document.createElement('option');
            option.value = merchant.id;
            option.textContent = merchant.trading_name;
            merchantSelect.appendChild(option);
        });

        // Event listener for merchant selection change
        merchantSelect.addEventListener('change', function () {
            const selectedMerchantId = this.value;
            branchSelect.innerHTML = `<option value="">Select Branch</option>`; // Reset branches
            branchSelect.disabled = true;

            if (!selectedMerchantId) return;

            const selectedMerchant = merchants.find(m => String(m.id) === String(selectedMerchantId));
            if (selectedMerchant && Array.isArray(selectedMerchant.branches) && selectedMerchant.branches.length > 0) {
                selectedMerchant.branches.forEach(branch => {
                    const option = document.createElement('option');
                    option.value = branch.id; // Assuming branch has an 'id'
                    option.textContent = branch.address; // Assuming branch has an 'address'
                    branchSelect.appendChild(option);
                });
                branchSelect.disabled = false;
            } else {
                // No branches or merchant not found
                branchSelect.innerHTML = `<option value="">No branches available</option>`;
                branchSelect.disabled = true;
            }
        });

    } catch (error) {
        console.error('Error loading merchant data:', error);
        Alert.showAlert(merchantContainer, 'Failed to load merchant data. Please try refreshing.', 'danger');
    }
}

export function requestVouchers() {
    const tbody = document.getElementById("transactions-tbody");
    const searchInput = document.getElementById("transactions-search");
    const emptyMsg = document.getElementById("transactions-empty");
    const pagination = document.getElementById("transactions-pagination");

    if (!tbody || !emptyMsg || !pagination) {
        console.error("Required transactions table elements not found in DOM");
        return;
    }

    // Search input is optional
    if (!searchInput) {
        console.warn("transactions-search element not found, search functionality will be disabled");
    }
    const PAGE_SIZE = 5;
    let currentPage = 1;
    let filteredData = [];
    tbody.innerHTML = '<tr><td colspan="3" class="text-center text-muted py-4 fs-12">Loading transactions...</td></tr>';
    fetchVouchers()
        .then(voucherData => {
            if (!Array.isArray(voucherData) || voucherData.length === 0) {
                tbody.innerHTML = '';

                // Show appropriate empty state message
                emptyMsg.innerHTML = `
                    <div class="text-center py-5">
                        <div class="empty-state">
                            <i class="bi bi-inbox display-4 text-white mb-3"></i>
                            <h6 class="text-white">No transactions found</h6>
                            <p class="text-white small mb-3">Your transaction history will appear here once you make payments</p>
                            <button class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#newTransactionModal">
                                <i class="bi bi-plus-circle me-1"></i>
                                Create First Transaction
                            </button>
                        </div>
                    </div>
                `;
                emptyMsg.classList.remove('d-none');
                pagination.innerHTML = '';
                return;
            }
            emptyMsg.classList.add('d-none');
            filteredData = voucherData;
            renderTransactionsTable(filteredData, tbody, currentPage, PAGE_SIZE);
            renderPagination(filteredData.length, currentPage, PAGE_SIZE, pagination, page => {
                currentPage = page;
                renderTransactionsTable(filteredData, tbody, currentPage, PAGE_SIZE);
            });

            // Attach search handler if search input exists
            if (searchInput) {
                searchInput.value = '';
                searchInput.oninput = function() {
                    const query = this.value.trim().toLowerCase();
                    filteredData = voucherData.filter(v =>
                        (v.created_at && new Date(v.created_at).toLocaleDateString().toLowerCase().includes(query)) ||
                        (v.amount && v.amount.toString().toLowerCase().includes(query)) ||
                        (v.status && v.status.toLowerCase().includes(query))
                    );
                    currentPage = 1;
                    renderTransactionsTable(filteredData, tbody, currentPage, PAGE_SIZE);
                    renderPagination(filteredData.length, currentPage, PAGE_SIZE, pagination, page => {
                        currentPage = page;
                        renderTransactionsTable(filteredData, tbody, currentPage, PAGE_SIZE);
                    });
                    if (filteredData.length === 0) {
                        emptyMsg.classList.remove('d-none');
                    } else {
                        emptyMsg.classList.add('d-none');
                    }
                };
            }
        })
        .catch(err => {
            console.error("Error loading transactions:", err);
            tbody.innerHTML = '';

            // Show error state in empty message
            emptyMsg.innerHTML = `
                <div class="text-center py-5">
                    <div class="empty-state">
                        <i class="bi bi-exclamation-triangle display-4 text-warning mb-3"></i>
                        <h6 class="text-white">Failed to load transactions</h6>
                        <p class="text-white small mb-3">There was an error loading your transaction history</p>
                        <button class="btn btn-outline-primary btn-sm" onclick="window.location.reload()">
                            <i class="bi bi-arrow-clockwise me-1"></i>
                            Retry
                        </button>
                    </div>
                </div>
            `;
            emptyMsg.classList.remove('d-none');
            pagination.innerHTML = '';
        });
}

function renderTransactionsTable(data, tbody, page, pageSize) {
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const pageData = data.slice(start, end);
    tbody.innerHTML = pageData.map(v => {
        const date = v.created_at ? new Date(v.created_at) : new Date();
        const dateStr = `<span class='fw-semibold text-dark fs-12'>${date.toLocaleDateString()}</span><br><small class='text-muted fs-12'>${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</small>`;
        const amountStr = `<span class='fw-semibold text-success fs-12'>${parseFloat(v.amount).toFixed(2)}</span>`;
        const status = (v.status || '').toLowerCase();
        const colors = {
            active: 'success',
            pending: 'warning',
            cancelled: 'danger',
            expired: 'secondary',
            redeemed: 'primary'
        };
        const color = colors[status] || 'primary';
        const statusStr = `<span class='badge bg-${color}-subtle text-${color} rounded-pill px-3 py-1 fs-12'><i class='bi bi-circle-fill me-1 small'></i>${status.charAt(0).toUpperCase() + status.slice(1)}</span>`;
        return `
            <tr>
                <td class='align-middle'>${dateStr}</td>
                <td class='align-middle'>${amountStr}</td>
                <td class='align-middle'>${statusStr}</td>
            </tr>
        `;
    }).join('');
}

function renderPagination(totalItems, currentPage, pageSize, container, onPageChange) {
    const totalPages = Math.ceil(totalItems / pageSize);
    if (totalPages <= 1) {
        container.innerHTML = '';
        return;
    }
    let html = '';
    html += `<button class="btn btn-sm btn-outline-secondary" ${currentPage === 1 ? 'disabled' : ''} data-page="prev">&laquo; Prev</button>`;
    html += `<span class="mx-2">Page ${currentPage} of ${totalPages}</span>`;
    html += `<button class="btn btn-sm btn-outline-secondary" ${currentPage === totalPages ? 'disabled' : ''} data-page="next">Next &raquo;</button>`;
    container.innerHTML = html;
    container.querySelectorAll('button[data-page]').forEach(btn => {
        btn.onclick = () => {
            let newPage = currentPage;
            if (btn.getAttribute('data-page') === 'prev' && currentPage > 1) newPage--;
            if (btn.getAttribute('data-page') === 'next' && currentPage < totalPages) newPage++;
            if (newPage !== currentPage) onPageChange(newPage);
        };
    });
}

async function fetchVouchers() {
    try {
        const phone = localStorage.getItem('login_phone');
        if (!phone) {
            console.warn('No login phone found in localStorage');
            return [];
        }

        const voucherData = await ApiService.getVouchers(phone);

        // Handle different response formats
        if (Array.isArray(voucherData)) {
            return voucherData;
        } else if (voucherData && Array.isArray(voucherData.vouchers)) {
            return voucherData.vouchers;
        } else if (voucherData && voucherData.data && Array.isArray(voucherData.data)) {
            return voucherData.data;
        } else {
            console.warn('Unexpected voucher data format:', voucherData);
            return [];
        }
    } catch (error) {
        console.error('Error fetching vouchers:', error);

        const errorMessage = error.responseJSON?.error ||
                    error.responseJSON?.message ||
                    error.message ||
                    'Failed to fetch transactions.';

        // Don't show alert for transactions, just log the error
        console.error('Voucher fetch error:', errorMessage);
        return [];
    }
}

export function requestPublicReceivers() {
    const tbody = document.getElementById("public-receivers-tbody");
    const searchInput = document.getElementById("public-receivers-search");
    const emptyMsg = document.getElementById("public-receivers-empty");
    const pagination = document.getElementById("public-receivers-pagination");
    if (!tbody || !searchInput || !emptyMsg || !pagination) {
        console.error("Public receivers table elements not found in DOM");
        return;
    }
    const PAGE_SIZE = 5;
    let currentPage = 1;
    let filteredData = [];
    tbody.innerHTML = '<tr><td colspan="2" class="text-center text-muted py-4 fs-12">Loading receivers...</td></tr>';
    fetchPublicReceivers()
        .then(receiverData => {
            if (!Array.isArray(receiverData) || receiverData.length === 0) {
                tbody.innerHTML = '';

                // Show appropriate empty state message
                emptyMsg.innerHTML = `
                    <div class="text-center py-5">
                        <div class="empty-state">
                            <i class="bi bi-building display-4 text-white mb-3"></i>
                            <h6 class="text-white">No public receivers found</h6>
                            <p class="text-white small mb-3">No billers are currently available for payments</p>
                            <button class="btn btn-outline-primary btn-sm" onclick="window.location.reload()">
                                <i class="bi bi-arrow-clockwise me-1"></i>
                                Refresh
                            </button>
                        </div>
                    </div>
                `;
                emptyMsg.classList.remove('d-none');
                pagination.innerHTML = '';
                return;
            }
            emptyMsg.classList.add('d-none');
            filteredData = receiverData;
            renderPublicReceiversTable(filteredData, tbody, currentPage, PAGE_SIZE);
            renderPagination(filteredData.length, currentPage, PAGE_SIZE, pagination, page => {
                currentPage = page;
                renderPublicReceiversTable(filteredData, tbody, currentPage, PAGE_SIZE);
            });
            // Attach search handler
            searchInput.value = '';
            searchInput.oninput = function() {
                const query = this.value.trim().toLowerCase();
                filteredData = receiverData.filter(r =>
                    r.name.toLowerCase().includes(query) ||
                    (r.description && r.description.toLowerCase().includes(query))
                );
                currentPage = 1;
                renderPublicReceiversTable(filteredData, tbody, currentPage, PAGE_SIZE);
                renderPagination(filteredData.length, currentPage, PAGE_SIZE, pagination, page => {
                    currentPage = page;
                    renderPublicReceiversTable(filteredData, tbody, currentPage, PAGE_SIZE);
                });
                if (filteredData.length === 0) {
                    emptyMsg.classList.remove('d-none');
                } else {
                    emptyMsg.classList.add('d-none');
                }
            };
        })
        .catch(err => {
            console.error("Error loading public receivers:", err);
            tbody.innerHTML = '';

            // Show error state in empty message
            emptyMsg.innerHTML = `
                <div class="text-center py-5">
                    <div class="empty-state">
                        <i class="bi bi-exclamation-triangle display-4 text-warning mb-3"></i>
                        <h6 class="text-white">Failed to load public receivers</h6>
                        <p class="text-white small mb-3">There was an error loading available billers</p>
                        <button class="btn btn-outline-primary btn-sm" onclick="window.location.reload()">
                            <i class="bi bi-arrow-clockwise me-1"></i>
                            Retry
                        </button>
                    </div>
                </div>
            `;
            emptyMsg.classList.remove('d-none');
            pagination.innerHTML = '';
        });
    // Attach pay button handler after rendering
    tbody.onclick = function(e) {
        const payBtn = e.target.closest('.pay-btn');
        if (!payBtn) return;
        const id = payBtn.dataset.id;
        const modal = new bootstrap.Modal(document.getElementById('payReceiverModal'));
        document.getElementById("receiver-id").value = id;
        modal.show();
    };
}

function renderPublicReceiversTable(data, tbody, page, pageSize) {
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const pageData = data.slice(start, end);
    tbody.innerHTML = pageData.map(r => `
        <tr>
            <td class="align-middle fw-semibold text-dark fs-12">
                <div class="d-flex align-items-center gap-2">
                    <div class="receiver-icon rounded-circle bg-transparent p-2 me-2 flex-shrink-0" style="width:38px;height:38px;">
                        <i class="bi bi-building text-primary fs-4"></i>
                    </div>
                    <span>${r.name}</span>
                </div>
            </td>
            <td class="align-middle">
                <button class="btn btn-primary btn-lg w-100 pay-btn d-flex align-items-center justify-content-center gap-2 py-2"
                    data-id="${r.id}">
                    <i class="bi bi-wallet2 fs-5 text-white"></i>
                    <span class="fs-12">Pay</span>
                </button>
            </td>
        </tr>
    `).join('');
}

async function fetchPublicReceivers() {
    const mainContent = document.querySelector('.app-content-area');

    try {
        const receiverData = await ApiService.getPublicReceivers();
        return receiverData["active_public_receivers"];
    } catch (error) {
        const errorMessage = error.responseJSON?.error || 
                    error.responseJSON?.message || 
                    error.message || 
                    'Failed to fetch public receivers.';

        Alert.showAlert(mainContent, errorMessage, 'danger');
    }
}

// --- Modal Management and Validation ---
export function validatePublicReceiverPayment(event) {
    const modalBody = event.target.closest('.modal-content')?.querySelector('.modal-body');
    const amountInput = document.getElementById('amount-p');
    const platformInput = document.getElementById('pay_from-p');
    let errors = [];

    if (!amountInput || !platformInput) {
        errors.push("Form elements are missing. Cannot validate.");
    } else {
        const amount = amountInput.value.trim();
        const platform = platformInput.value.trim();
        if (!amount) errors.push("Amount is required.");
        else if (isNaN(amount) || Number(amount) <= 0) errors.push("Amount must be a positive number.");
        if (!platform) errors.push("Please select a payment platform.");
        else if (platform !== "MTN_MOMO") errors.push("Only MTN MoMo is currently available. Other payment platforms are coming soon.");
    }

    if (errors.length > 0) {
        if (modalBody) Alert.showAlert(modalBody, errors.join('\n'), 'warning');
        else console.warn("Validation errors for public receiver payment:", errors.join('\n'));
        return false;
    }
    return true;
}

export function validateDirectPayment(event) {
    const modalBody = event.target.closest('.modal-content')?.querySelector('.modal-body');
    const merchantCodeInput = document.getElementById('merchant_code-d');
    const amountInput = document.getElementById('amount-d');
    const platformInput = document.getElementById('pay_from-d');
    let errors = [];

    if (!merchantCodeInput || !amountInput || !platformInput) {
        errors.push("Form elements are missing. Cannot validate.");
    } else {
        const merchantCode = merchantCodeInput.value.trim();
        const amount = amountInput.value.trim();
        const platform = platformInput.value.trim();

        if (!merchantCode) {
            errors.push("Merchant code is required.");
        } else if (!/^\d{4,8}$/.test(merchantCode)) {
            errors.push("Merchant code must be 4-8 digits only.");
        }

        if (!amount) errors.push("Amount is required.");
        else if (isNaN(amount) || Number(amount) <= 0) errors.push("Amount must be a positive number.");
        if (!platform) errors.push("Please select a payment platform.");
        else if (platform !== "MTN_MOMO") errors.push("Only MTN MoMo is currently available. Other payment platforms are coming soon.");
    }

    if (errors.length > 0) {
        if (modalBody) Alert.showAlert(modalBody, errors.join('\n'), 'warning');
        else console.warn("Validation errors for direct payment:", errors.join('\n'));
        return false;
    }
    return true;
}

export async function showConfirmPublicReceiverPaymentModal(viewInstance) {
    const receiverID = document.getElementById("receiver-id")?.value;
    const amount = document.getElementById("amount-p")?.value;
    const payFrom = document.getElementById("pay_from-p")?.value;
    const reference = document.getElementById("default_ref-p")?.value;
    const modalBody = document.getElementById('payReceiverModal')?.querySelector('.modal-body');

    if (!receiverID || !amount || !payFrom) {
        if (modalBody) Alert.showAlert(modalBody, "Missing required fields to confirm payment.", 'danger');
        else console.error("Missing required fields for public receiver payment confirmation.");
        return;
    }

    // Get the source modal element and collect data
    const payReceiverModalElement = document.getElementById('payReceiverModal');
    const extraRefs = collectExtraReferences(payReceiverModalElement);
    const attachmentData = attachmentHandlers.getAttachmentData(payReceiverModalElement);

    try {
        const price = await ApiService.getPricing(amount, "voucher", payFrom);
        const charge = (Number(price['service_charge'])).toFixed(2);
        const total = (Number(price['total_amount'])).toFixed(2);
        const extraRefsHTML = generateExtraRefsHTML(extraRefs);

        // Pass attachment data to template
        const modalHTML = Templates.confirmPublicPayModalTemplate(
            receiverID, 
            amount, 
            payFrom, 
            charge, 
            total,
            reference,
            extraRefsHTML,
            attachmentData // Add attachment data as last parameter
        );
        
        // Hide the source modal
        if (payReceiverModalElement) {
            const bsModal = bootstrap.Modal.getInstance(payReceiverModalElement);
            if (bsModal) {
                payReceiverModalElement.addEventListener('hidden.bs.modal', () => {
                    // Show confirmation modal after source modal is fully hidden
                    displayModal('confirmPublicPayModal', modalHTML, () => {
                        replaceAndAddListener('initiate-public-pay', 'click', (e) =>
                            viewInstance.payPublicReceiver(e, extraRefs, attachmentData) // Pass attachment to payment handler
                        );
                    }).catch(error => {
                        console.error('Error displaying confirmation modal:', error);
                    });
                }, { once: true });
                bsModal.hide();
            } else {
                // If no modal instance, show confirmation modal directly
                displayModal('confirmPublicPayModal', modalHTML, () => {
                    replaceAndAddListener('initiate-public-pay', 'click', (e) =>
                        viewInstance.payPublicReceiver(e, extraRefs, attachmentData) // Pass attachment to payment handler
                    );
                }).catch(error => {
                    console.error('Error displaying confirmation modal:', error);
                });
            }
        } else {
            // If no source modal element, show confirmation modal directly
            displayModal('confirmPublicPayModal', modalHTML, () => {
                replaceAndAddListener('initiate-public-pay', 'click', (e) =>
                    viewInstance.payPublicReceiver(e, extraRefs, attachmentData) // Pass attachment to payment handler
                );
            }).catch(error => {
                console.error('Error displaying confirmation modal:', error);
            });
        }

    } catch (error) {
        console.error("Error fetching pricing for public receiver:", error);
        if (modalBody) Alert.showAlert(modalBody, 'Could not fetch pricing details. Please try again.', 'danger');
    }
}

export async function showConfirmDirectPaymentModal(viewInstance) {
    const merchantCode = document.getElementById("merchant_code-d")?.value;
    const amount = document.getElementById("amount-d")?.value;
    const payFrom = document.getElementById("pay_from-d")?.value;
    const reference = document.getElementById("default_ref-d")?.value;
    const modalBody = document.getElementById('directPayModal')?.querySelector('.modal-body');

    if (!merchantCode || !amount || !payFrom) {
        if (modalBody) Alert.showAlert(modalBody, "Missing required fields to confirm payment.", 'danger');
        else console.error("Missing required fields for direct payment confirmation.");
        return;
    }

    const directPayModalElement = document.getElementById('directPayModal');
    const extraRefs = collectExtraReferences(directPayModalElement);
    const attachmentData = attachmentHandlers.getAttachmentData(directPayModalElement);

    try {
        const price = await ApiService.getPricing(amount, "direct", payFrom);
        const charge = (Number(price['service_charge'])).toFixed(2);
        const total = (Number(price['total_amount'])).toFixed(2);
        const extraRefsHTML = generateExtraRefsHTML(extraRefs);

        // Pass attachment data to template
        const modalHTML = Templates.confirmDirectPayModalTemplate(
            merchantCode,
            amount,
            payFrom,
            charge,
            total,
            reference,
            extraRefsHTML,
            attachmentData // Add attachment data as last parameter
        );

        // Hide the source modal safely
        if (directPayModalElement) {
            const bsModal = bootstrap.Modal.getInstance(directPayModalElement);
            if (bsModal) {
                directPayModalElement.addEventListener('hidden.bs.modal', () => {
                    // Show confirmation modal after source modal is fully hidden
                    displayModal('confirmDirectPayModal', modalHTML, () => {
                        replaceAndAddListener('initiate-direct-pay', 'click', (e) =>
                            viewInstance.payDirectMerchant(e, extraRefs, attachmentData) // Pass attachment to payment handler
                        );
                    }).catch(error => {
                        console.error('Error displaying confirmation modal:', error);
                    });
                }, { once: true });
                bsModal.hide();
            } else {
                // If no modal instance, show confirmation modal directly
                displayModal('confirmDirectPayModal', modalHTML, () => {
                    replaceAndAddListener('initiate-direct-pay', 'click', (e) =>
                        viewInstance.payDirectMerchant(e, extraRefs, attachmentData) // Pass attachment to payment handler
                    );
                }).catch(error => {
                    console.error('Error displaying confirmation modal:', error);
                });
            }
        } else {
            // If no source modal element, show confirmation modal directly
            displayModal('confirmDirectPayModal', modalHTML, () => {
                replaceAndAddListener('initiate-direct-pay', 'click', (e) =>
                    viewInstance.payDirectMerchant(e, extraRefs, attachmentData) // Pass attachment to payment handler
                );
            }).catch(error => {
                console.error('Error displaying confirmation modal:', error);
            });
        }

    } catch (error) {
        console.error("Error fetching pricing for direct payment:", error);
        if (modalBody) Alert.showAlert(modalBody, 'Could not fetch pricing details. Please try again.', 'danger');
    }
}

export function showConfirmTransactionModal(viewInstance, paymentType, merchantID, amount, charge, total, payFrom, reference, extraRefs) {
    // Get attachment data from the source modal
    const newTransactionModalElement = document.getElementById('newTransactionModal');
    const attachmentData = attachmentHandlers.getAttachmentData(newTransactionModalElement);
    const extraRefsHTML = generateExtraRefsHTML(extraRefs);
    
    // Pass attachment data to template
    const modalHTML = Templates.confirmTransactionModalTemplate(
        paymentType,
        merchantID, 
        amount, 
        charge, 
        total, 
        payFrom,
        reference,
        extraRefsHTML,
        attachmentData
    );

    if (newTransactionModalElement) {
        const bsModal = bootstrap.Modal.getInstance(newTransactionModalElement);
        if (bsModal) {
            newTransactionModalElement.addEventListener('hidden.bs.modal', () => {
                // Show confirmation modal after source modal is fully hidden
                showConfirmationModal();
            }, { once: true });
            bsModal.hide();
        } else {
            // If no modal instance, show confirmation modal directly
            showConfirmationModal();
        }
    } else {
        // If no source modal element, show confirmation modal directly
        showConfirmationModal();
    }

    function showConfirmationModal() {
        if (paymentType === "voucher") {
            displayModal('confirmTransactionModal', modalHTML, () => {
                replaceAndAddListener('authorize_transaction', 'click', (e) =>
                    viewInstance.createTransactionStepTwo(e, extraRefs, attachmentData) // Pass attachment to step two
                );
            }).catch(error => {
                console.error('Error displaying confirmation modal:', error);
            });
        } else if (paymentType === "direct_pay") {
            displayModal('confirmTransactionModal', modalHTML, () => {
                replaceAndAddListener('pay_now', 'click', (e) =>
                    viewInstance.payNowWithMOMO(e) // Pass attachment to step two
                );
            }).catch(error => {
                console.error('Error displaying confirmation modal:', error);
            });
        }
    }
}

export function showSuccessModal(title = "Success", paymentInfo, onHiddenCallback) {
    const modalHTML = Templates.successModalTemplate(title, paymentInfo);
    displayModal('successModal', modalHTML, null, onHiddenCallback);
}

// Function to set up toggles for transaction modals
export function setupTransactionModalToggles() {
    const modal = document.getElementById('newTransactionModal');
    const payFromDiv = document.getElementById('pay_from_div');
    const payFromSelect = document.getElementById('pay_from');
    let lastMode = "voucher";

    if (modal) {
        modal.addEventListener('show.bs.modal', (event) => {
            // Always get fresh references
            const confirmDirectPay = document.getElementById('confirm_direct_pay');
            const createVoucherBtn = document.getElementById('confirm_transaction');
            // Detect trigger
            let trigger = event.relatedTarget;
            if (trigger && trigger.dataset && trigger.dataset.mode) {
                lastMode = trigger.dataset.mode;
            } else {
                lastMode = "voucher";
            }
            // Hide both
            confirmDirectPay?.classList.add('d-none');
            createVoucherBtn?.classList.add('d-none');
        });

        modal.addEventListener('shown.bs.modal', () => {
            // Always get fresh references
            const confirmDirectPay = document.getElementById('confirm_direct_pay');
            const createVoucherBtn = document.getElementById('confirm_transaction');
            if (lastMode === "voucher") {
                confirmDirectPay?.classList.add('d-none');
                createVoucherBtn?.classList.remove('d-none');
                payFromDiv?.classList.remove('d-none');
                if (payFromSelect) payFromSelect.value = "";
            } else if (lastMode === "direct_pay") {
                confirmDirectPay?.classList.remove('d-none');
                createVoucherBtn?.classList.add('d-none');
                payFromDiv?.classList.add('d-none');
                if (payFromSelect) payFromSelect.value = "MTN_MOMO";
            }
        });
    }

    // Set lastMode on trigger button click
    const newTransactionBtn = document.getElementById('new_transaction');
    const directPayBtn = document.getElementById('direct_pay');
    newTransactionBtn?.addEventListener('click', e => {
        lastMode = e.currentTarget.dataset.mode || "voucher";
    });
    directPayBtn?.addEventListener('click', e => {
        lastMode = e.currentTarget.dataset.mode || "direct_pay";
    });
}

// Expose collection of references if needed by HomeView directly before showing a modal
export { collectExtraReferences };