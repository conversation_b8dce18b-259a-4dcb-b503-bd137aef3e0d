import { BASE_URL, navigateTo } from '../../router.js';
import { API_CONFIG } from '../../services/api.js';

// Attempt SSO login and store the returned local token
window.addEventListener('DOMContentLoaded', function () {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('sso') && urlParams.get('sso') === 'true') {
        trySSOLogin();
    }
});

function trySSOLogin() {
  const req = $.ajax({
    url: `${API_CONFIG.BASE_URL}/auth/sso`,
    method: 'POST',
    dataType: 'json',
    xhrFields: {
      withCredentials: true // This ensures cookies are sent with the request
    },
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    }
  });

  req.done(function(data) {
    try {
      const parsed = typeof data === 'string' ? JSON.parse(data) : data;

      if (parsed.token && parsed.phone) {
        localStorage.setItem('user-token', parsed.token);
        localStorage.setItem('login_phone', parsed.phone);
        window.is_authenticated = true;
        localStorage.setItem('is_authenticated', 'true');
        
        navigateTo(`${BASE_URL}/`);
      } else {
        console.log('Parsed error:', parsed);
        throw new Error('SSO Login failed, please enter your phone number to login.');
      }

    } catch (e) {
      console.error('Verification failed:', e);
      showError(getErrorMessageFromResponse(e));
    }
  });

  req.fail(function(jqXHR, textStatus, errorThrown) {
    console.log('SSO Login failed:', jqXHR);
    
    let errorMessage = 'An unexpected error occurred. Please try again.';
    
    switch(jqXHR.status) {
      case 401:
        errorMessage = 'SSO Login failed. Please enter your phone number to login.';
        break;
      case 403:
        errorMessage = 'Access denied. Please contact support if this persists.';
        break;
      case 404:
        errorMessage = 'SSO service not found. Please try again later.';
        break;
      case 500:
        errorMessage = 'Server error. Please try again later.';
        break;
      case 0:
        errorMessage = 'Network error. Please check your internet connection.';
        break;
    }

    showError(errorMessage);
    
    // If it's not a network error, redirect to regular login after a short delay
    if (jqXHR.status !== 0) {
      setTimeout(() => {
        navigateTo(`${BASE_URL}/login`);
      }, 2000);
    }
  });
}

function getErrorMessageFromResponse(error) {
  if (error.status === 401) return 'Invalid verification code';
  if (error.status === 404) return 'User not found';
  if (error.status === 429) return 'Too many attempts. Please try again later.';
  if (!navigator.onLine) return 'No internet connection. Please check your network.';
  return 'An unexpected error occurred. Please try again.';
}

function showError(message) {
  Swal.fire({
    title: 'Error',
    text: message,
    icon: 'error',
    customClass: {
      popup: 'my-swal-popup',
      title: 'my-swal-title',
      content: 'my-swal-content',
      htmlContainer: 'my-swal-text',
      confirmButton: 'my-confirm-button'
    },
    confirmButtonText: 'OK'
  });
}