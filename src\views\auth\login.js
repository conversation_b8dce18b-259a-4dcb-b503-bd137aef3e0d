import { BASE_URL, navigateTo } from '../../router.js';
import ApiService from '../../services/api.js';

export default class AuthLoginView {
    constructor() {
        this.state = {
            title: 'Welcome back to LockedPay',
            subtitle: 'Please enter your phone number to continue',
            logo: {
                src: 'assets/images/brand/logo/logo.svg',
                alt: 'LockedPay'
            },
            formFields: {
                phone: {
                    id: 'phone',
                    label: 'PHONE NUMBER',
                    type: 'tel',
                    placeholder: 'Enter your phone number',
                    required: true
                }
            },
            error: {
                visible: false,
                message: ''
            }
        };
    }

    getErrorMessage() {
        if (!this.state.error.visible) return '';
        return `
            <div class="alert alert-danger alert-dismissible fade show rounded-3 border-0 shadow-sm" role="alert">
                <div class="d-flex align-items-center">
                    <i class="bi bi-exclamation-circle-fill me-2"></i>
                    <div class="flex-grow-1">${this.state.error.message}</div>
                    <button type="button" class="btn-close small shadow-none" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            </div>
        `;
    }

    getFormField(field) {
        const { id, label, type, placeholder, required } = field;
        return `
            <div class="mb-4">
                <label for="${id}" class="form-label small fw-bold text-white mb-2">${label}</label>
                <div class="input-group input-group-lg border rounded-3 p-1" style="max-width: 100%;">
                    <span class="input-group-text border-0 bg-light-soft px-3" id="cCodePrepend" style="min-width: 90px;">
                        <i class="bi bi-phone me-1"></i>+268
                    </span>
                    <input maxlength="8" type="tel" inputmode="numeric" pattern="[0-9]{8}" autocomplete="tel"
                        id="${id}" 
                        class="form-control border-0 shadow-none bg-light-soft"
                        style="min-width:0; flex:1 1 0%;"
                        name="${id}" placeholder="${placeholder}" aria-describedby="cCodePrepend"
                        ${required ? 'required' : ''} />
                </div>
            </div>
        `;
    }

    getLoginForm() {
        return `
            <form class="needs-validation" novalidate>
                ${this.getErrorMessage()}
                ${this.getFormField(this.state.formFields.phone)}
                
                <div class="mt-4">
                    <button id="verify_phone" class="btn btn-primary btn-lg w-100 rounded-3 shadow-sm">
                        <i class="bi bi-shield-check me-2"></i>Verify Phone
                    </button>
                </div>
                <p class="text-center small text-white mt-4">
                    <i class="bi bi-shield-lock me-1"></i>
                    Your phone number is securely encrypted
                </p>
            </form>
        `;
    }

    render() {
        return `
            <main class="container-fluid bg-light min-vh-100">
                <div class="row align-items-center justify-content-center min-vh-100">
                    <div class="col-11 col-sm-8 col-md-6 col-lg-5 col-xl-4">
                        <div class="card border-0 rounded-4 shadow-sm auth-card">
                            <div class="card-body p-4 p-sm-5">
                                <div class="text-center mb-5">
                                    <a href="#!" class="d-inline-block mb-4">
                                        <img src="${this.state.logo.src}" 
                                             class="img-fluid" style="height: 48px;"
                                             alt="${this.state.logo.alt}" />
                                    </a>
                                    <h4 class="fw-bold text-white mb-2">${this.state.title}</h4>
                                    <p class="text-white mb-0">${this.state.subtitle}</p>
                                </div>
                                ${this.getLoginForm()}
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        `;
    }

    async handleSignin(event) {
        event.preventDefault();
        const btn = document.getElementById('verify_phone');
        const phoneInput = document.getElementById('phone');
        const phone = phoneInput.value.trim();

        // Validate phone number: must be 8 digits
        if (!/^\d{8}$/.test(phone)) {
            this.state.error = {
                visible: true,
                message: 'Please enter a valid 8-digit phone number.'
            };
            this.updateFormError();
            return;
        }

        if (btn) {
            btn.innerHTML = `<span class="spinner-grow spinner-grow-sm" role="status"></span> Requesting...`;
            btn.disabled = true;
        }

        try {
            localStorage.setItem('login_phone', phone);
            const response = await ApiService.verifyPhone({ phone });
            if (btn) {
                btn.innerHTML = `Verify Phone`;
                btn.disabled = false;
            }
            navigateTo(`${BASE_URL}/auth_otp`);
        } catch (error) {
            if (btn) {
                btn.innerHTML = `Verify Phone`;
                btn.disabled = false;
            }
            let errorMessage = 'An unexpected error occurred. Please try again.';
            if (error.status === 401) {
                errorMessage = 'Invalid phone number';
            } else if (error.status === 404) {
                errorMessage = 'User not found';
            } else if (error.status === 429) {
                errorMessage = 'Too many login attempts. Please try again later.';
            } else if (!navigator.onLine) {
                errorMessage = 'No internet connection. Please check your network.';
            }
            this.state.error = {
                visible: true,
                message: errorMessage
            };
            this.updateFormError();
        }
    }

    afterRender() {
        this.clearStoredData();
        this.preventBackNavigation();
        this.initializeEventListeners();
    }

    updateFormError() {
        const errorContainer = document.querySelector('.alert-warning');
        if (errorContainer) {
            errorContainer.parentNode.removeChild(errorContainer);
        }
        const form = document.querySelector('form.needs-validation');
        if (form && this.state.error.visible) {
            form.insertAdjacentHTML('afterbegin', this.getErrorMessage());
            this.initializeEventListeners(); // Reattach close event
        }
    }

    clearStoredData() {
        localStorage.removeItem('user-token');
        localStorage.removeItem('nav-toggled');
        sessionStorage.clear();
    }

    clearError() {
        this.state.error = {
            visible: false,
            message: ''
        };
    }

    preventBackNavigation() {
        history.pushState(null, null, location.href);
        window.onpopstate = () => history.go(1);
    }

    initializeEventListeners() {
        const verifyBtn = document.getElementById('verify_phone');
        if (verifyBtn) {
            verifyBtn.addEventListener('click', (e) => this.handleSignin(e));
        }
    
        // Add error dismissal handler
        const errorCloseBtn = document.querySelector('.alert .btn-close');
        if (errorCloseBtn) {
            errorCloseBtn.addEventListener('click', () => {
                this.clearError();
                const formContainer = document.querySelector('.card-body');
                if (formContainer) {
                    formContainer.innerHTML = `
                        <div class="text-center mb-5">
                            <a href="#!" class="d-inline-block mb-4">
                                <img src="${this.state.logo.src}" 
                                        class="img-fluid" style="height: 48px;"
                                        alt="${this.state.logo.alt}" />
                            </a>
                            <h4 class="fw-bold text-white mb-2">${this.state.title}</h4>
                            <p class="text-white mb-0">${this.state.subtitle}</p>
                        </div>
                        ${this.getLoginForm()}
                    `;
                    this.initializeEventListeners();
                }
            });
        }
    }
}