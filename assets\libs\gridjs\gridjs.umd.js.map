{"version": 3, "file": "gridjs.umd.js", "sources": ["../src/types.ts", "../node_modules/preact/dist/preact.module.js", "../src/util/id.ts", "../src/base.ts", "../src/view/htmlElement.tsx", "../src/util/html.ts", "../src/pipeline/processor.ts", "../src/cell.ts", "../src/row.ts", "../src/tabular.ts", "../src/util/array.ts", "../src/util/eventEmitter.ts", "../src/util/deepEqual.ts", "../src/pipeline/filter/globalSearch.ts", "../src/operator/search.ts", "../src/util/className.ts", "../src/pipeline/filter/serverGlobalSearch.ts", "../node_modules/preact/hooks/dist/hooks.module.js", "../src/hooks/useConfig.ts", "../src/i18n/en_US.ts", "../src/i18n/language.ts", "../src/view/plugin/search/actions.ts", "../src/hooks/useStore.ts", "../src/hooks/useSelector.ts", "../src/view/plugin/search/search.tsx", "../src/util/debounce.ts", "../src/pipeline/limit/pagination.ts", "../src/pipeline/limit/serverPagination.ts", "../src/view/plugin/pagination.tsx", "../src/util/width.ts", "../src/view/table/shadow.tsx", "../src/util/string.ts", "../src/plugin.ts", "../src/util/log.ts", "../src/header.ts", "../src/storage/storage.ts", "../src/storage/memory.ts", "../src/storage/server.ts", "../src/storage/storageUtils.ts", "../src/pipeline/pipeline.ts", "../src/pipeline/extractor/storage.ts", "../src/pipeline/transformer/arrayToTabular.ts", "../src/pipeline/initiator/server.ts", "../src/pipeline/transformer/storageResponseToArray.ts", "../src/pipeline/pipelineUtils.ts", "../src/state/store.ts", "../src/config.ts", "../src/view/table/td.tsx", "../src/view/table/tr.tsx", "../src/view/table/messageRow.tsx", "../src/view/table/tbody.tsx", "../src/pipeline/sort/native.ts", "../src/view/plugin/sort/actions.ts", "../src/pipeline/sort/server.ts", "../src/view/plugin/sort/sort.tsx", "../src/util/throttle.ts", "../src/view/plugin/resize/resize.tsx", "../src/view/table/th.tsx", "../src/view/table/thead.tsx", "../src/util/table.ts", "../src/view/actions.ts", "../src/view/table/table.tsx", "../src/view/headerContainer.tsx", "../src/view/footerContainer.tsx", "../src/view/container.tsx", "../src/grid.ts"], "sourcesContent": ["import { ComponentChild } from 'preact';\nimport Row from './row';\nimport { SortConfig } from './view/plugin/sort/sort';\nimport { JSXInternal } from 'preact/src/jsx';\nimport { Plugin } from './plugin';\n\nexport type OneDArray<T> = T[];\nexport type TwoDArray<T> = T[][];\n\n/**\n * Table cell types\n */\nexport type TCell = number | string | boolean | ComponentChild | HTMLElement;\n// Array of Arrays\nexport type TDataArrayRow = OneDArray<TCell>;\nexport type TDataArray = OneDArray<TDataArrayRow>;\n// Array of Objects\nexport type TDataObjectRow = { [key: string]: TCell };\nexport type TDataObject = OneDArray<TDataObjectRow>;\n// (Array of Arrays) and (Array of Objects)\nexport type TData = TDataArray | TDataObject;\n\n// Table header cell type\nexport interface TColumn {\n  id?: string;\n  // default data for all columns\n  data?: ((row: TDataArrayRow | TDataObjectRow) => TCell) | TCell;\n  // column label\n  name?: string | ComponentChild;\n  plugin?: Plugin<any>;\n  // column width\n  width?: string;\n  minWidth?: string;\n  sort?: SortConfig;\n  columns?: OneDArray<TColumn>;\n  resizable?: boolean;\n  hidden?: boolean;\n  formatter?: (cell: TCell, row: Row, column: TColumn) => ComponentChild;\n  // HTML attributes to be added to all cells and header of this column\n  attributes?:\n    | ((\n        // this is null when `attributes` is called for a th\n        cell: TCell | null,\n        row: Row | null,\n        column: TColumn,\n      ) => JSXInternal.HTMLAttributes<HTMLTableCellElement>)\n    | JSXInternal.HTMLAttributes<HTMLTableCellElement>;\n}\n\n// Comparator function for the sorting plugin\nexport type Comparator<T> = (a: T, b: T) => number;\n\nexport interface TColumnSort {\n  index: number;\n  // 1 ascending, -1 descending\n  direction?: 1 | -1;\n}\n\n// container status\nexport enum Status {\n  Init,\n  Loading,\n  Loaded,\n  Rendered,\n  Error,\n}\n\nexport type CSSDeclaration = {\n  [key: string]: string | number;\n};\n", "var n,l,u,i,t,o,r,f={},e=[],c=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function s(n,l){for(var u in l)n[u]=l[u];return n}function a(n){var l=n.parentNode;l&&l.removeChild(n)}function h(l,u,i){var t,o,r,f={};for(r in u)\"key\"==r?t=u[r]:\"ref\"==r?o=u[r]:f[r]=u[r];if(arguments.length>2&&(f.children=arguments.length>3?n.call(arguments,2):i),\"function\"==typeof l&&null!=l.defaultProps)for(r in l.defaultProps)void 0===f[r]&&(f[r]=l.defaultProps[r]);return v(l,f,t,o,null)}function v(n,i,t,o,r){var f={type:n,props:i,key:t,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==r?++u:r};return null==r&&null!=l.vnode&&l.vnode(f),f}function y(){return{current:null}}function p(n){return n.children}function d(n,l){this.props=n,this.context=l}function _(n,l){if(null==l)return n.__?_(n.__,n.__.__k.indexOf(n)+1):null;for(var u;l<n.__k.length;l++)if(null!=(u=n.__k[l])&&null!=u.__e)return u.__e;return\"function\"==typeof n.type?_(n):null}function k(n){var l,u;if(null!=(n=n.__)&&null!=n.__c){for(n.__e=n.__c.base=null,l=0;l<n.__k.length;l++)if(null!=(u=n.__k[l])&&null!=u.__e){n.__e=n.__c.base=u.__e;break}return k(n)}}function b(n){(!n.__d&&(n.__d=!0)&&t.push(n)&&!g.__r++||o!==l.debounceRendering)&&((o=l.debounceRendering)||setTimeout)(g)}function g(){for(var n;g.__r=t.length;)n=t.sort(function(n,l){return n.__v.__b-l.__v.__b}),t=[],n.some(function(n){var l,u,i,t,o,r;n.__d&&(o=(t=(l=n).__v).__e,(r=l.__P)&&(u=[],(i=s({},t)).__v=t.__v+1,j(r,t,i,l.__n,void 0!==r.ownerSVGElement,null!=t.__h?[o]:null,u,null==o?_(t):o,t.__h),z(u,t),t.__e!=o&&k(t)))})}function w(n,l,u,i,t,o,r,c,s,a){var h,y,d,k,b,g,w,x=i&&i.__k||e,C=x.length;for(u.__k=[],h=0;h<l.length;h++)if(null!=(k=u.__k[h]=null==(k=l[h])||\"boolean\"==typeof k?null:\"string\"==typeof k||\"number\"==typeof k||\"bigint\"==typeof k?v(null,k,null,null,k):Array.isArray(k)?v(p,{children:k},null,null,null):k.__b>0?v(k.type,k.props,k.key,k.ref?k.ref:null,k.__v):k)){if(k.__=u,k.__b=u.__b+1,null===(d=x[h])||d&&k.key==d.key&&k.type===d.type)x[h]=void 0;else for(y=0;y<C;y++){if((d=x[y])&&k.key==d.key&&k.type===d.type){x[y]=void 0;break}d=null}j(n,k,d=d||f,t,o,r,c,s,a),b=k.__e,(y=k.ref)&&d.ref!=y&&(w||(w=[]),d.ref&&w.push(d.ref,null,k),w.push(y,k.__c||b,k)),null!=b?(null==g&&(g=b),\"function\"==typeof k.type&&k.__k===d.__k?k.__d=s=m(k,s,n):s=A(n,k,d,x,b,s),\"function\"==typeof u.type&&(u.__d=s)):s&&d.__e==s&&s.parentNode!=n&&(s=_(d))}for(u.__e=g,h=C;h--;)null!=x[h]&&N(x[h],x[h]);if(w)for(h=0;h<w.length;h++)M(w[h],w[++h],w[++h])}function m(n,l,u){for(var i,t=n.__k,o=0;t&&o<t.length;o++)(i=t[o])&&(i.__=n,l=\"function\"==typeof i.type?m(i,l,u):A(u,i,i,t,i.__e,l));return l}function x(n,l){return l=l||[],null==n||\"boolean\"==typeof n||(Array.isArray(n)?n.some(function(n){x(n,l)}):l.push(n)),l}function A(n,l,u,i,t,o){var r,f,e;if(void 0!==l.__d)r=l.__d,l.__d=void 0;else if(null==u||t!=o||null==t.parentNode)n:if(null==o||o.parentNode!==n)n.appendChild(t),r=null;else{for(f=o,e=0;(f=f.nextSibling)&&e<i.length;e+=1)if(f==t)break n;n.insertBefore(t,o),r=o}return void 0!==r?r:t.nextSibling}function C(n,l,u,i,t){var o;for(o in u)\"children\"===o||\"key\"===o||o in l||H(n,o,null,u[o],i);for(o in l)t&&\"function\"!=typeof l[o]||\"children\"===o||\"key\"===o||\"value\"===o||\"checked\"===o||u[o]===l[o]||H(n,o,l[o],u[o],i)}function $(n,l,u){\"-\"===l[0]?n.setProperty(l,u):n[l]=null==u?\"\":\"number\"!=typeof u||c.test(l)?u:u+\"px\"}function H(n,l,u,i,t){var o;n:if(\"style\"===l)if(\"string\"==typeof u)n.style.cssText=u;else{if(\"string\"==typeof i&&(n.style.cssText=i=\"\"),i)for(l in i)u&&l in u||$(n.style,l,\"\");if(u)for(l in u)i&&u[l]===i[l]||$(n.style,l,u[l])}else if(\"o\"===l[0]&&\"n\"===l[1])o=l!==(l=l.replace(/Capture$/,\"\")),l=l.toLowerCase()in n?l.toLowerCase().slice(2):l.slice(2),n.l||(n.l={}),n.l[l+o]=u,u?i||n.addEventListener(l,o?T:I,o):n.removeEventListener(l,o?T:I,o);else if(\"dangerouslySetInnerHTML\"!==l){if(t)l=l.replace(/xlink(H|:h)/,\"h\").replace(/sName$/,\"s\");else if(\"href\"!==l&&\"list\"!==l&&\"form\"!==l&&\"tabIndex\"!==l&&\"download\"!==l&&l in n)try{n[l]=null==u?\"\":u;break n}catch(n){}\"function\"==typeof u||(null==u||!1===u&&-1==l.indexOf(\"-\")?n.removeAttribute(l):n.setAttribute(l,u))}}function I(n){this.l[n.type+!1](l.event?l.event(n):n)}function T(n){this.l[n.type+!0](l.event?l.event(n):n)}function j(n,u,i,t,o,r,f,e,c){var a,h,v,y,_,k,b,g,m,x,A,C,$,H,I,T=u.type;if(void 0!==u.constructor)return null;null!=i.__h&&(c=i.__h,e=u.__e=i.__e,u.__h=null,r=[e]),(a=l.__b)&&a(u);try{n:if(\"function\"==typeof T){if(g=u.props,m=(a=T.contextType)&&t[a.__c],x=a?m?m.props.value:a.__:t,i.__c?b=(h=u.__c=i.__c).__=h.__E:(\"prototype\"in T&&T.prototype.render?u.__c=h=new T(g,x):(u.__c=h=new d(g,x),h.constructor=T,h.render=O),m&&m.sub(h),h.props=g,h.state||(h.state={}),h.context=x,h.__n=t,v=h.__d=!0,h.__h=[],h._sb=[]),null==h.__s&&(h.__s=h.state),null!=T.getDerivedStateFromProps&&(h.__s==h.state&&(h.__s=s({},h.__s)),s(h.__s,T.getDerivedStateFromProps(g,h.__s))),y=h.props,_=h.state,v)null==T.getDerivedStateFromProps&&null!=h.componentWillMount&&h.componentWillMount(),null!=h.componentDidMount&&h.__h.push(h.componentDidMount);else{if(null==T.getDerivedStateFromProps&&g!==y&&null!=h.componentWillReceiveProps&&h.componentWillReceiveProps(g,x),!h.__e&&null!=h.shouldComponentUpdate&&!1===h.shouldComponentUpdate(g,h.__s,x)||u.__v===i.__v){for(h.props=g,h.state=h.__s,u.__v!==i.__v&&(h.__d=!1),h.__v=u,u.__e=i.__e,u.__k=i.__k,u.__k.forEach(function(n){n&&(n.__=u)}),A=0;A<h._sb.length;A++)h.__h.push(h._sb[A]);h._sb=[],h.__h.length&&f.push(h);break n}null!=h.componentWillUpdate&&h.componentWillUpdate(g,h.__s,x),null!=h.componentDidUpdate&&h.__h.push(function(){h.componentDidUpdate(y,_,k)})}if(h.context=x,h.props=g,h.__v=u,h.__P=n,C=l.__r,$=0,\"prototype\"in T&&T.prototype.render){for(h.state=h.__s,h.__d=!1,C&&C(u),a=h.render(h.props,h.state,h.context),H=0;H<h._sb.length;H++)h.__h.push(h._sb[H]);h._sb=[]}else do{h.__d=!1,C&&C(u),a=h.render(h.props,h.state,h.context),h.state=h.__s}while(h.__d&&++$<25);h.state=h.__s,null!=h.getChildContext&&(t=s(s({},t),h.getChildContext())),v||null==h.getSnapshotBeforeUpdate||(k=h.getSnapshotBeforeUpdate(y,_)),I=null!=a&&a.type===p&&null==a.key?a.props.children:a,w(n,Array.isArray(I)?I:[I],u,i,t,o,r,f,e,c),h.base=u.__e,u.__h=null,h.__h.length&&f.push(h),b&&(h.__E=h.__=null),h.__e=!1}else null==r&&u.__v===i.__v?(u.__k=i.__k,u.__e=i.__e):u.__e=L(i.__e,u,i,t,o,r,f,c);(a=l.diffed)&&a(u)}catch(n){u.__v=null,(c||null!=r)&&(u.__e=e,u.__h=!!c,r[r.indexOf(e)]=null),l.__e(n,u,i)}}function z(n,u){l.__c&&l.__c(u,n),n.some(function(u){try{n=u.__h,u.__h=[],n.some(function(n){n.call(u)})}catch(n){l.__e(n,u.__v)}})}function L(l,u,i,t,o,r,e,c){var s,h,v,y=i.props,p=u.props,d=u.type,k=0;if(\"svg\"===d&&(o=!0),null!=r)for(;k<r.length;k++)if((s=r[k])&&\"setAttribute\"in s==!!d&&(d?s.localName===d:3===s.nodeType)){l=s,r[k]=null;break}if(null==l){if(null===d)return document.createTextNode(p);l=o?document.createElementNS(\"http://www.w3.org/2000/svg\",d):document.createElement(d,p.is&&p),r=null,c=!1}if(null===d)y===p||c&&l.data===p||(l.data=p);else{if(r=r&&n.call(l.childNodes),h=(y=i.props||f).dangerouslySetInnerHTML,v=p.dangerouslySetInnerHTML,!c){if(null!=r)for(y={},k=0;k<l.attributes.length;k++)y[l.attributes[k].name]=l.attributes[k].value;(v||h)&&(v&&(h&&v.__html==h.__html||v.__html===l.innerHTML)||(l.innerHTML=v&&v.__html||\"\"))}if(C(l,p,y,o,c),v)u.__k=[];else if(k=u.props.children,w(l,Array.isArray(k)?k:[k],u,i,t,o&&\"foreignObject\"!==d,r,e,r?r[0]:i.__k&&_(i,0),c),null!=r)for(k=r.length;k--;)null!=r[k]&&a(r[k]);c||(\"value\"in p&&void 0!==(k=p.value)&&(k!==l.value||\"progress\"===d&&!k||\"option\"===d&&k!==y.value)&&H(l,\"value\",k,y.value,!1),\"checked\"in p&&void 0!==(k=p.checked)&&k!==l.checked&&H(l,\"checked\",k,y.checked,!1))}return l}function M(n,u,i){try{\"function\"==typeof n?n(u):n.current=u}catch(n){l.__e(n,i)}}function N(n,u,i){var t,o;if(l.unmount&&l.unmount(n),(t=n.ref)&&(t.current&&t.current!==n.__e||M(t,null,u)),null!=(t=n.__c)){if(t.componentWillUnmount)try{t.componentWillUnmount()}catch(n){l.__e(n,u)}t.base=t.__P=null,n.__c=void 0}if(t=n.__k)for(o=0;o<t.length;o++)t[o]&&N(t[o],u,i||\"function\"!=typeof n.type);i||null==n.__e||a(n.__e),n.__=n.__e=n.__d=void 0}function O(n,l,u){return this.constructor(n,u)}function P(u,i,t){var o,r,e;l.__&&l.__(u,i),r=(o=\"function\"==typeof t)?null:t&&t.__k||i.__k,e=[],j(i,u=(!o&&t||i).__k=h(p,null,[u]),r||f,f,void 0!==i.ownerSVGElement,!o&&t?[t]:r?null:i.firstChild?n.call(i.childNodes):null,e,!o&&t?t:r?r.__e:i.firstChild,o),z(e,u)}function S(n,l){P(n,l,S)}function q(l,u,i){var t,o,r,f=s({},l.props);for(r in u)\"key\"==r?t=u[r]:\"ref\"==r?o=u[r]:f[r]=u[r];return arguments.length>2&&(f.children=arguments.length>3?n.call(arguments,2):i),v(l.type,f,t||l.key,o||l.ref,null)}function B(n,l){var u={__c:l=\"__cC\"+r++,__:n,Consumer:function(n,l){return n.children(l)},Provider:function(n){var u,i;return this.getChildContext||(u=[],(i={})[l]=this,this.getChildContext=function(){return i},this.shouldComponentUpdate=function(n){this.props.value!==n.value&&u.some(b)},this.sub=function(n){u.push(n);var l=n.componentWillUnmount;n.componentWillUnmount=function(){u.splice(u.indexOf(n),1),l&&l.call(n)}}),n.children}};return u.Provider.__=u.Consumer.contextType=u}n=e.slice,l={__e:function(n,l,u,i){for(var t,o,r;l=l.__;)if((t=l.__c)&&!t.__)try{if((o=t.constructor)&&null!=o.getDerivedStateFromError&&(t.setState(o.getDerivedStateFromError(n)),r=t.__d),null!=t.componentDidCatch&&(t.componentDidCatch(n,i||{}),r=t.__d),r)return t.__E=t}catch(l){n=l}throw n}},u=0,i=function(n){return null!=n&&void 0===n.constructor},d.prototype.setState=function(n,l){var u;u=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=s({},this.state),\"function\"==typeof n&&(n=n(s({},u),this.props)),n&&s(u,n),null!=n&&this.__v&&(l&&this._sb.push(l),b(this))},d.prototype.forceUpdate=function(n){this.__v&&(this.__e=!0,n&&this.__h.push(n),b(this))},d.prototype.render=p,t=[],g.__r=0,r=0;export{d as Component,p as Fragment,q as cloneElement,B as createContext,h as createElement,y as createRef,h,S as hydrate,i as isValidElement,l as options,P as render,x as toChildArray};\n//# sourceMappingURL=preact.module.js.map\n", "export type ID = string;\n\nexport function generateUUID(): ID {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n    const r = (Math.random() * 16) | 0,\n      v = c == 'x' ? r : (r & 0x3) | 0x8;\n    return v.toString(16);\n  });\n}\n", "import { generateUUID, ID } from './util/id';\n\nclass Base {\n  private readonly _id: ID;\n\n  constructor(id?: ID) {\n    this._id = id || generateUUID();\n  }\n\n  get id(): ID {\n    return this._id;\n  }\n}\n\nexport default Base;\n", "import { h } from 'preact';\n\nexport interface HTMLContentProps {\n  content: string;\n  parentElement?: string;\n}\n\nexport function HTMLElement(props: HTMLContentProps) {\n  return h(props.parentElement || 'span', {\n    dangerouslySetInnerHTML: { __html: props.content },\n  });\n}\n", "import { h, VNode } from 'preact';\nimport { HTMLElement } from '../view/htmlElement';\n\nexport function decode(content: string): string {\n  const value = new DOMParser().parseFromString(content, 'text/html');\n  return value.documentElement.textContent;\n}\n\nexport function html(content: string, parentElement?: string): VNode {\n  return h(HTMLElement, { content: content, parentElement: parentElement });\n}\n", "// The order of enum items define the processing order of the processor type\n// e.g. Extractor = 0 will be processed before Transformer = 1\nimport { generateUUID, ID } from '../util/id';\nimport { EventEmitter } from '../util/eventEmitter';\nimport { deepEqual } from '../util/deepEqual';\n\nexport enum ProcessorType {\n  Initiator,\n  ServerFilter,\n  ServerSort,\n  ServerLimit,\n  Extractor,\n  Transformer,\n  Filter,\n  Sort,\n  Limit,\n}\n\ninterface PipelineProcessorEvents {\n  propsUpdated: <T, P>(processor: PipelineProcessor<T, P>) => void;\n  beforeProcess: (...args) => void;\n  afterProcess: (...args) => void;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface PipelineProcessorProps {}\n\nexport abstract class PipelineProcessor<\n  T,\n  P extends Partial<PipelineProcessorProps>,\n> extends EventEmitter<PipelineProcessorEvents> {\n  public readonly id: ID;\n  private _props: P;\n\n  abstract get type(): ProcessorType;\n  protected abstract _process(...args): T | Promise<T>;\n  protected validateProps?(...args): void;\n\n  constructor(props?: Partial<P>) {\n    super();\n\n    this._props = {} as P;\n    this.id = generateUUID();\n\n    if (props) this.setProps(props);\n  }\n\n  /**\n   * process is used to call beforeProcess and afterProcess callbacks\n   * This function is just a wrapper that calls _process()\n   *\n   * @param args\n   */\n  process(...args): T | Promise<T> {\n    if (this.validateProps instanceof Function) {\n      this.validateProps(...args);\n    }\n\n    this.emit('beforeProcess', ...args);\n    const result = this._process(...args);\n    this.emit('afterProcess', ...args);\n    return result;\n  }\n\n  setProps(props: Partial<P>): this {\n    const updatedProps = {\n      ...this._props,\n      ...props,\n    };\n\n    if (!deepEqual(updatedProps, this._props)) {\n      this._props = updatedProps;\n      this.emit('propsUpdated', this);\n    }\n\n    return this;\n  }\n\n  get props(): P {\n    return this._props;\n  }\n}\n", "import Base from './base';\nimport { TCell } from './types';\nimport { html } from './util/html';\nimport { ComponentChild } from 'preact';\n\nclass Cell extends Base {\n  // because a Cell is a subset of TCell type\n  public data: number | string | boolean | ComponentChild;\n\n  constructor(data: TCell) {\n    super();\n\n    this.update(data);\n  }\n\n  private cast(data: TCell): number | string | boolean | ComponentChild {\n    if (data instanceof HTMLElement) {\n      return html(data.outerHTML);\n    }\n\n    return data;\n  }\n\n  /**\n   * Updates the Cell's data\n   *\n   * @param data\n   */\n  public update(data: TCell): Cell {\n    this.data = this.cast(data);\n    return this;\n  }\n}\n\nexport default Cell;\n", "import Cell from './cell';\nimport Base from './base';\nimport { TCell } from './types';\n\nclass Row extends Base {\n  private _cells: Cell[];\n\n  constructor(cells?: Cell[]) {\n    super();\n\n    this.cells = cells || [];\n  }\n\n  public cell(index: number): Cell {\n    return this._cells[index];\n  }\n\n  public get cells(): Cell[] {\n    return this._cells;\n  }\n\n  public set cells(cells: Cell[]) {\n    this._cells = cells;\n  }\n\n  public toArray(): TCell[] {\n    return this.cells.map((cell) => cell.data);\n  }\n\n  /**\n   * Creates a new Row from an array of Cell(s)\n   * This method generates a new ID for the Row and all nested elements\n   *\n   * @param cells\n   * @returns Row\n   */\n  static fromCells(cells: Cell[]): Row {\n    return new Row(cells.map((cell) => new Cell(cell.data)));\n  }\n\n  get length(): number {\n    return this.cells.length;\n  }\n}\n\nexport default Row;\n", "import Base from './base';\nimport Row from './row';\nimport Cell from './cell';\nimport { OneDArray, TCell, TwoDArray } from './types';\nimport { oneDtoTwoD } from './util/array';\n\nclass Tabular extends Base {\n  private _rows: Row[];\n  private _length: number;\n\n  constructor(rows?: Row[] | Row) {\n    super();\n\n    if (rows instanceof Array) {\n      this.rows = rows;\n    } else if (rows instanceof Row) {\n      this.rows = [rows];\n    } else {\n      this.rows = [];\n    }\n  }\n\n  get rows(): Row[] {\n    return this._rows;\n  }\n\n  set rows(rows: Row[]) {\n    this._rows = rows;\n  }\n\n  get length(): number {\n    return this._length || this.rows.length;\n  }\n\n  // we want to sent the length when storage is ServerStorage\n  set length(len: number) {\n    this._length = len;\n  }\n\n  public toArray(): TCell[][] {\n    return this.rows.map((row) => row.toArray());\n  }\n\n  /**\n   * Creates a new Tabular from an array of Row(s)\n   * This method generates a new ID for the Tabular and all nested elements\n   *\n   * @param rows\n   * @returns Tabular\n   */\n  static fromRows(rows: Row[]): Tabular {\n    return new Tabular(rows.map((row) => Row.fromCells(row.cells)));\n  }\n\n  /**\n   * Creates a new Tabular from a 2D array\n   * This method generates a new ID for the Tabular and all nested elements\n   *\n   * @param data\n   * @returns Tabular\n   */\n  static fromArray<T extends TCell>(\n    data: OneDArray<T> | TwoDArray<T>,\n  ): Tabular {\n    data = oneDtoTwoD(data);\n\n    return new Tabular(\n      data.map((row) => new Row(row.map((cell) => new Cell(cell)))),\n    );\n  }\n}\n\nexport default Tabular;\n", "import { OneDArray, TwoDArray } from '../types';\n\nexport function oneDtoTwoD<T>(data: OneDArray<T> | TwoDArray<T>): TwoDArray<T> {\n  if (data[0] && !(data[0] instanceof Array)) {\n    return [data] as TwoDArray<T>;\n  }\n\n  return data as TwoDArray<T>;\n}\n\nexport function flatten<T>(arrays: TwoDArray<T>): OneDArray<T> {\n  return arrays.reduce((prev, x) => prev.concat(x), []);\n}\n", "type EventArgs<T> = [T] extends [(...args: infer U) => any]\n  ? U\n  : [T] extends [void]\n  ? []\n  : [T];\n\n/**\n * Example:\n *\n * export interface BaseEvents<P, S> {\n *   SET_STATE: (component: BaseComponent<P, S>, state: S) => void;\n * }\n */\n\nexport interface EventEmitter<EventTypes> {\n  addListener<EventName extends keyof EventTypes>(\n    event: EventName,\n    listener: (...args: EventArgs<EventTypes[EventName]>) => void,\n  ): EventEmitter<EventTypes>;\n\n  on<EventName extends keyof EventTypes>(\n    event: EventName,\n    listener: (...args: EventArgs<EventTypes[EventName]>) => void,\n  ): EventEmitter<EventTypes>;\n\n  off<EventName extends keyof EventTypes>(\n    event: EventName,\n    listener: (...args: EventArgs<EventTypes[EventName]>) => void,\n  ): EventEmitter<EventTypes>;\n\n  emit<EventName extends keyof EventTypes>(\n    event: EventName,\n    ...args: EventArgs<EventTypes[EventName]>\n  ): boolean;\n}\n\nexport class EventEmitter<EventTypes> {\n  private callbacks: { [event: string]: ((...args) => void)[] };\n\n  // because we are using EventEmitter as a mixin and the\n  // constructor won't be called by the applyMixins function\n  // see src/base.ts and src/util/applyMixin.ts\n  private init(event?: string): void {\n    if (!this.callbacks) {\n      this.callbacks = {};\n    }\n\n    if (event && !this.callbacks[event]) {\n      this.callbacks[event] = [];\n    }\n  }\n\n  listeners(): { [event: string]: ((...args) => void)[] } {\n    return this.callbacks;\n  }\n\n  on<EventName extends keyof EventTypes>(\n    event: EventName,\n    listener: (...args: EventArgs<EventTypes[EventName]>) => void,\n  ): EventEmitter<EventTypes> {\n    this.init(event as string);\n    this.callbacks[event as string].push(listener);\n    return this;\n  }\n\n  off<EventName extends keyof EventTypes>(\n    event: EventName,\n    listener: (...args: EventArgs<EventTypes[EventName]>) => void,\n  ): EventEmitter<EventTypes> {\n    const eventName = event as string;\n\n    this.init();\n\n    if (!this.callbacks[eventName] || this.callbacks[eventName].length === 0) {\n      // there is no callbacks with this key\n      return this;\n    }\n\n    this.callbacks[eventName] = this.callbacks[eventName].filter(\n      (value) => value != listener,\n    );\n\n    return this;\n  }\n\n  emit<EventName extends keyof EventTypes>(\n    event: EventName,\n    ...args: EventArgs<EventTypes[EventName]>\n  ): boolean {\n    const eventName = event as string;\n\n    this.init(eventName);\n\n    if (this.callbacks[eventName].length > 0) {\n      this.callbacks[eventName].forEach((value) => value(...args));\n      return true;\n    }\n\n    return false;\n  }\n}\n", "/**\n * Returns true if both objects are equal\n * @param a left object\n * @param b right object\n * @returns\n */\nexport function deepEqual<A, B>(obj1: A, obj2: B) {\n  // If objects are not the same type, return false\n  if (typeof obj1 !== typeof obj2) {\n    return false;\n  }\n  // If objects are both null or undefined, return true\n  if (obj1 === null && obj2 === null) {\n    return true;\n  }\n  // If objects are both primitive types, compare them directly\n  if (typeof obj1 !== 'object') {\n    // eslint-disable-next-line\n    // @ts-ignore\n    return obj1 === obj2;\n  }\n  // If objects are arrays, compare their elements recursively\n  if (Array.isArray(obj1) && Array.isArray(obj2)) {\n    if (obj1.length !== obj2.length) {\n      return false;\n    }\n    for (let i = 0; i < obj1.length; i++) {\n      if (!deepEqual(obj1[i], obj2[i])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  // If objects are VNodes, compare their props only\n  if (\n    // eslint-disable-next-line no-prototype-builtins\n    obj1.hasOwnProperty('constructor') &&\n    // eslint-disable-next-line no-prototype-builtins\n    obj2.hasOwnProperty('constructor') &&\n    // eslint-disable-next-line no-prototype-builtins\n    obj1.hasOwnProperty('props') &&\n    // eslint-disable-next-line no-prototype-builtins\n    obj2.hasOwnProperty('props') &&\n    // eslint-disable-next-line no-prototype-builtins\n    obj1.hasOwnProperty('key') &&\n    // eslint-disable-next-line no-prototype-builtins\n    obj2.hasOwnProperty('key') &&\n    // eslint-disable-next-line no-prototype-builtins\n    obj1.hasOwnProperty('ref') &&\n    // eslint-disable-next-line no-prototype-builtins\n    obj2.hasOwnProperty('ref') &&\n    // eslint-disable-next-line no-prototype-builtins\n    obj1.hasOwnProperty('type') &&\n    // eslint-disable-next-line no-prototype-builtins\n    obj2.hasOwnProperty('type')\n  ) {\n    return deepEqual(obj1['props'], obj2['props']);\n  }\n  // If objects are both objects, compare their properties recursively\n  const keys1 = Object.keys(obj1);\n  const keys2 = Object.keys(obj2);\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n  for (const key of keys1) {\n    // eslint-disable-next-line no-prototype-builtins\n    if (!obj2.hasOwnProperty(key) || !deepEqual(obj1[key], obj2[key])) {\n      return false;\n    }\n  }\n  return true;\n}\n", "import search from '../../operator/search';\nimport Tabular from '../../tabular';\nimport {\n  PipelineProcessor,\n  PipelineProcessorProps,\n  ProcessorType,\n} from '../processor';\nimport { OneDArray, TCell, TColumn } from '../../types';\n\ninterface GlobalSearchFilterProps extends PipelineProcessorProps {\n  keyword: string;\n  columns: OneDArray<TColumn>;\n  ignoreHiddenColumns: boolean;\n  selector?: (cell: TCell, rowIndex: number, cellIndex: number) => string;\n}\n\nclass GlobalSearchFilter extends PipelineProcessor<\n  Tabular,\n  GlobalSearchFilterProps\n> {\n  get type(): ProcessorType {\n    return ProcessorType.Filter;\n  }\n\n  _process(data: Tabular): Tabular {\n    if (this.props.keyword) {\n      return search(\n        String(this.props.keyword).trim(),\n        this.props.columns,\n        this.props.ignoreHiddenColumns,\n        data,\n        this.props.selector,\n      );\n    }\n\n    return data;\n  }\n}\n\nexport default GlobalSearchFilter;\n", "import Tabular from '../tabular';\nimport { VNode } from 'preact';\nimport { HTMLContentProps } from '../view/htmlElement';\nimport { OneDArray, TCell, TColumn } from '../types';\n\nexport default function (\n  keyword: string,\n  columns: OneDArray<TColumn>,\n  ignoreHiddenColumns: boolean,\n  tabular: Tabular,\n  selector?: (cell: TCell, rowIndex: number, cellIndex: number) => string,\n): Tabular {\n  // escape special regex chars\n  keyword = keyword.replace(/[-[\\]{}()*+?.,\\\\^$|#\\s]/g, '\\\\$&');\n\n  return new Tabular(\n    tabular.rows.filter((row, rowIndex) =>\n      row.cells.some((cell, cellIndex) => {\n        if (!cell) {\n          return false;\n        }\n\n        if (ignoreHiddenColumns) {\n          if (\n            columns &&\n            columns[cellIndex] &&\n            typeof columns[cellIndex] === 'object'\n          ) {\n            const typedColumn = columns[cellIndex] as TColumn;\n            if (typedColumn.hidden) {\n              return false;\n            }\n          }\n        }\n\n        let data = '';\n\n        if (typeof selector === 'function') {\n          data = selector(cell.data, rowIndex, cellIndex);\n        } else if (typeof cell.data === 'object') {\n          // HTMLContent element\n          const element = cell.data as VNode<HTMLContentProps>;\n          if (element && element.props && element.props.content) {\n            // TODO: we should only search in the content of the element. props.content is the entire HTML element\n            data = element.props.content;\n          }\n        } else {\n          // primitive types\n          data = String(cell.data);\n        }\n\n        return new RegExp(keyword, 'gi').test(data);\n      }),\n    ),\n  );\n}\n", "import { JSXInternal } from 'preact/src/jsx';\n\nexport function className(...args: string[]): string {\n  const prefix = 'gridjs';\n\n  return `${prefix}${args.reduce(\n    (prev: string, cur: string) => `${prev}-${cur}`,\n    '',\n  )}`;\n}\n\nexport function classJoin(\n  ...classNames: (undefined | string | JSXInternal.SignalLike<string>)[]\n): string {\n  return classNames\n    .map((x) => (x ? x.toString() : ''))\n    .filter((x) => x)\n    .reduce((className, prev) => `${className || ''} ${prev}`, '')\n    .trim();\n}\n", "import {\n  PipelineProcessor,\n  PipelineProcessorProps,\n  ProcessorType,\n} from '../processor';\nimport { ServerStorageOptions } from '../../storage/server';\n\ninterface ServerGlobalSearchFilterProps extends PipelineProcessorProps {\n  keyword?: string;\n  url?: (prevUrl: string, keyword: string) => string;\n  body?: (prevBody: BodyInit, keyword: string) => BodyInit;\n}\n\nclass ServerGlobalSearchFilter extends PipelineProcessor<\n  ServerStorageOptions,\n  ServerGlobalSearchFilterProps\n> {\n  get type(): ProcessorType {\n    return ProcessorType.ServerFilter;\n  }\n\n  _process(options?: ServerStorageOptions): ServerStorageOptions {\n    if (!this.props.keyword) return options;\n\n    const updates = {};\n\n    if (this.props.url) {\n      updates['url'] = this.props.url(options.url, this.props.keyword);\n    }\n\n    if (this.props.body) {\n      updates['body'] = this.props.body(options.body, this.props.keyword);\n    }\n\n    return {\n      ...options,\n      ...updates,\n    };\n  }\n}\n\nexport default ServerGlobalSearchFilter;\n", "import{options as n}from\"preact\";var t,r,u,i,o=0,f=[],c=[],e=n.__b,a=n.__r,v=n.diffed,l=n.__c,m=n.unmount;function d(t,u){n.__h&&n.__h(r,t,o||u),o=0;var i=r.__H||(r.__H={__:[],__h:[]});return t>=i.__.length&&i.__.push({__V:c}),i.__[t]}function p(n){return o=1,y(B,n)}function y(n,u,i){var o=d(t++,2);if(o.t=n,!o.__c&&(o.__=[i?i(u):B(void 0,u),function(n){var t=o.__N?o.__N[0]:o.__[0],r=o.t(t,n);t!==r&&(o.__N=[r,o.__[1]],o.__c.setState({}))}],o.__c=r,!r.u)){r.u=!0;var f=r.shouldComponentUpdate;r.shouldComponentUpdate=function(n,t,r){if(!o.__c.__H)return!0;var u=o.__c.__H.__.filter(function(n){return n.__c});if(u.every(function(n){return!n.__N}))return!f||f.call(this,n,t,r);var i=!1;return u.forEach(function(n){if(n.__N){var t=n.__[0];n.__=n.__N,n.__N=void 0,t!==n.__[0]&&(i=!0)}}),!(!i&&o.__c.props===n)&&(!f||f.call(this,n,t,r))}}return o.__N||o.__}function h(u,i){var o=d(t++,3);!n.__s&&z(o.__H,i)&&(o.__=u,o.i=i,r.__H.__h.push(o))}function s(u,i){var o=d(t++,4);!n.__s&&z(o.__H,i)&&(o.__=u,o.i=i,r.__h.push(o))}function _(n){return o=5,F(function(){return{current:n}},[])}function A(n,t,r){o=6,s(function(){return\"function\"==typeof n?(n(t()),function(){return n(null)}):n?(n.current=t(),function(){return n.current=null}):void 0},null==r?r:r.concat(n))}function F(n,r){var u=d(t++,7);return z(u.__H,r)?(u.__V=n(),u.i=r,u.__h=n,u.__V):u.__}function T(n,t){return o=8,F(function(){return n},t)}function q(n){var u=r.context[n.__c],i=d(t++,9);return i.c=n,u?(null==i.__&&(i.__=!0,u.sub(r)),u.props.value):n.__}function x(t,r){n.useDebugValue&&n.useDebugValue(r?r(t):t)}function P(n){var u=d(t++,10),i=p();return u.__=n,r.componentDidCatch||(r.componentDidCatch=function(n,t){u.__&&u.__(n,t),i[1](n)}),[i[0],function(){i[1](void 0)}]}function V(){var n=d(t++,11);if(!n.__){for(var u=r.__v;null!==u&&!u.__m&&null!==u.__;)u=u.__;var i=u.__m||(u.__m=[0,0]);n.__=\"P\"+i[0]+\"-\"+i[1]++}return n.__}function b(){for(var t;t=f.shift();)if(t.__P&&t.__H)try{t.__H.__h.forEach(k),t.__H.__h.forEach(w),t.__H.__h=[]}catch(r){t.__H.__h=[],n.__e(r,t.__v)}}n.__b=function(n){r=null,e&&e(n)},n.__r=function(n){a&&a(n),t=0;var i=(r=n.__c).__H;i&&(u===r?(i.__h=[],r.__h=[],i.__.forEach(function(n){n.__N&&(n.__=n.__N),n.__V=c,n.__N=n.i=void 0})):(i.__h.forEach(k),i.__h.forEach(w),i.__h=[])),u=r},n.diffed=function(t){v&&v(t);var o=t.__c;o&&o.__H&&(o.__H.__h.length&&(1!==f.push(o)&&i===n.requestAnimationFrame||((i=n.requestAnimationFrame)||j)(b)),o.__H.__.forEach(function(n){n.i&&(n.__H=n.i),n.__V!==c&&(n.__=n.__V),n.i=void 0,n.__V=c})),u=r=null},n.__c=function(t,r){r.some(function(t){try{t.__h.forEach(k),t.__h=t.__h.filter(function(n){return!n.__||w(n)})}catch(u){r.some(function(n){n.__h&&(n.__h=[])}),r=[],n.__e(u,t.__v)}}),l&&l(t,r)},n.unmount=function(t){m&&m(t);var r,u=t.__c;u&&u.__H&&(u.__H.__.forEach(function(n){try{k(n)}catch(n){r=n}}),u.__H=void 0,r&&n.__e(r,u.__v))};var g=\"function\"==typeof requestAnimationFrame;function j(n){var t,r=function(){clearTimeout(u),g&&cancelAnimationFrame(t),setTimeout(n)},u=setTimeout(r,100);g&&(t=requestAnimationFrame(r))}function k(n){var t=r,u=n.__c;\"function\"==typeof u&&(n.__c=void 0,u()),r=t}function w(n){var t=r;n.__c=n.__(),r=t}function z(n,t){return!n||n.length!==t.length||t.some(function(t,r){return t!==n[r]})}function B(n,t){return\"function\"==typeof t?t(n):t}export{T as useCallback,q as useContext,x as useDebugValue,h as useEffect,P as useErrorBoundary,V as useId,A as useImperativeHandle,s as useLayoutEffect,F as useMemo,y as useReducer,_ as useRef,p as useState};\n//# sourceMappingURL=hooks.module.js.map\n", "import { useContext } from 'preact/hooks';\nimport { Config, ConfigContext } from '../config';\n\nexport function useConfig(): Config {\n  return useContext(ConfigContext);\n}\n", "export default {\n  search: {\n    placeholder: 'Type a keyword...',\n  },\n  sort: {\n    sortAsc: 'Sort column ascending',\n    sortDesc: 'Sort column descending',\n  },\n  pagination: {\n    previous: 'Previous',\n    next: 'Next',\n    navigate: (page, pages) => `Page ${page} of ${pages}`,\n    page: (page) => `Page ${page}`,\n    showing: 'Showing',\n    of: 'of',\n    to: 'to',\n    results: 'results',\n  },\n  loading: 'Loading...',\n  noRecordsFound: 'No matching records found',\n  error: 'An error happened while fetching the data',\n};\n", "import { useConfig } from '../hooks/useConfig';\nimport enUS from './en_US';\ntype MessageFormat = (...args) => string;\ntype Message = string | MessageFormat;\nexport type Language = { [key: string]: Message | Language };\n\nexport class Translator {\n  private readonly _language: Language;\n  private readonly _defaultLanguage: Language;\n\n  constructor(language?: Language) {\n    this._language = language;\n    this._defaultLanguage = enUS;\n  }\n\n  /**\n   * Tries to split the message with \".\" and find\n   * the key in the given language\n   *\n   * @param message\n   * @param lang\n   */\n  getString(message: string, lang: Language): MessageFormat {\n    if (!lang || !message) return null;\n\n    const splitted = message.split('.');\n    const key = splitted[0];\n\n    if (lang[key]) {\n      const val = lang[key];\n\n      if (typeof val === 'string') {\n        return (): string => val;\n      } else if (typeof val === 'function') {\n        return val;\n      } else {\n        return this.getString(splitted.slice(1).join('.'), val);\n      }\n    }\n\n    return null;\n  }\n\n  translate(message: string, ...args): string {\n    const translated = this.getString(message, this._language);\n    let messageFormat;\n\n    if (translated) {\n      messageFormat = translated;\n    } else {\n      messageFormat = this.getString(message, this._defaultLanguage);\n    }\n\n    if (messageFormat) {\n      return messageFormat(...args);\n    }\n\n    return message;\n  }\n}\n\nexport function useTranslator() {\n  const config = useConfig();\n\n  return function (message: string, ...args): string {\n    return config.translator.translate(message, ...args);\n  };\n}\n", "export const SearchKeyword = (payload) => (state) => {\n  return {\n    ...state,\n    search: {\n      keyword: payload,\n    },\n  };\n};\n", "import { useConfig } from './useConfig';\n\nexport function useStore() {\n  const config = useConfig();\n  return config.store;\n}\n", "import { useEffect, useState } from 'preact/hooks';\nimport { useStore } from './useStore';\n\nexport default function useSelector<T>(selector: (state) => T) {\n  const store = useStore();\n  const [current, setCurrent] = useState(selector(store.getState()));\n\n  useEffect(() => {\n    const unsubscribe = store.subscribe(() => {\n      const updated = selector(store.getState());\n\n      if (current !== updated) {\n        setCurrent(updated);\n      }\n    });\n\n    return unsubscribe;\n  }, []);\n\n  return current;\n}\n", "import { h, JSX } from 'preact';\nimport GlobalSearchFilter from '../../../pipeline/filter/globalSearch';\nimport { classJoin, className } from '../../../util/className';\nimport ServerGlobalSearchFilter from '../../../pipeline/filter/serverGlobalSearch';\nimport { TCell } from '../../../types';\nimport { useConfig } from '../../../hooks/useConfig';\nimport { useCallback, useEffect, useState } from 'preact/hooks';\nimport { useTranslator } from '../../../i18n/language';\nimport * as actions from './actions';\nimport { useStore } from '../../../hooks/useStore';\nimport useSelector from '../../../hooks/useSelector';\nimport { debounce } from '../../../util/debounce';\n\nexport interface SearchConfig {\n  keyword?: string;\n  ignoreHiddenColumns?: boolean;\n  debounceTimeout?: number;\n  selector?: (cell: TCell, rowIndex: number, cellIndex: number) => string;\n  server?: {\n    url?: (prevUrl: string, keyword: string) => string;\n    body?: (prevBody: BodyInit, keyword: string) => BodyInit;\n  };\n}\n\nexport function Search() {\n  const [processor, setProcessor] = useState<\n    GlobalSearchFilter | ServerGlobalSearchFilter\n  >(undefined);\n  const config = useConfig();\n  const props = config.search as SearchConfig;\n  const _ = useTranslator();\n  const { dispatch } = useStore();\n  const state = useSelector((state) => state.search);\n\n  useEffect(() => {\n    if (!processor) return;\n\n    processor.setProps({\n      keyword: state?.keyword,\n    });\n  }, [state, processor]);\n\n  useEffect(() => {\n    if (props.server) {\n      setProcessor(\n        new ServerGlobalSearchFilter({\n          keyword: props.keyword,\n          url: props.server.url,\n          body: props.server.body,\n        }),\n      );\n    } else {\n      setProcessor(\n        new GlobalSearchFilter({\n          keyword: props.keyword,\n          columns: config.header && config.header.columns,\n          ignoreHiddenColumns:\n            props.ignoreHiddenColumns ||\n            props.ignoreHiddenColumns === undefined,\n          selector: props.selector,\n        }),\n      );\n    }\n\n    // initial search\n    if (props.keyword) dispatch(actions.SearchKeyword(props.keyword));\n  }, [props]);\n\n  useEffect(() => {\n    if (!processor) return undefined;\n\n    config.pipeline.register<object, object>(processor);\n\n    return () => config.pipeline.unregister<object, object>(processor);\n  }, [config, processor]);\n\n  const debouncedOnInput = useCallback(\n    debounce(\n      (event: JSX.TargetedEvent<HTMLInputElement>) => {\n        if (event.target instanceof HTMLInputElement) {\n          dispatch(actions.SearchKeyword(event.target.value));\n        }\n      },\n      processor instanceof ServerGlobalSearchFilter\n        ? props.debounceTimeout || 250\n        : 0,\n    ),\n    [props, processor],\n  );\n\n  return (\n    <div className={className(classJoin('search', config.className?.search))}>\n      <input\n        type=\"search\"\n        placeholder={_('search.placeholder')}\n        aria-label={_('search.placeholder')}\n        onInput={debouncedOnInput}\n        className={classJoin(className('input'), className('search', 'input'))}\n        defaultValue={state?.keyword || ''}\n      />\n    </div>\n  );\n}\n", "export const debounce = <F extends (...args: any[]) => any>(\n  func: F,\n  waitFor: number,\n) => {\n  let timeout;\n\n  return (...args: Parameters<F>): Promise<ReturnType<F>> =>\n    new Promise((resolve) => {\n      if (timeout) {\n        clearTimeout(timeout);\n      }\n\n      timeout = setTimeout(() => resolve(func(...args)), waitFor);\n    });\n};\n", "import Tabular from '../../tabular';\nimport {\n  PipelineProcessor,\n  PipelineProcessorProps,\n  ProcessorType,\n} from '../processor';\n\ninterface PaginationLimitProps extends PipelineProcessorProps {\n  page: number;\n  limit: number;\n}\n\nclass PaginationLimit extends PipelineProcessor<Tabular, PaginationLimitProps> {\n  protected validateProps(): void {\n    if (isNaN(Number(this.props.limit)) || isNaN(Number(this.props.page))) {\n      throw Error('Invalid parameters passed');\n    }\n  }\n\n  get type(): ProcessorType {\n    return ProcessorType.Limit;\n  }\n\n  protected _process(data: Tabular): Tabular {\n    const page = this.props.page;\n    const start = page * this.props.limit;\n    const end = (page + 1) * this.props.limit;\n\n    return new Tabular(data.rows.slice(start, end));\n  }\n}\n\nexport default PaginationLimit;\n", "import {\n  PipelineProcessor,\n  PipelineProcessorProps,\n  ProcessorType,\n} from '../processor';\nimport { ServerStorageOptions } from '../../storage/server';\n\ninterface ServerPaginationLimitProps extends PipelineProcessorProps {\n  page: number;\n  limit: number;\n  url?: (prevUrl: string, page: number, limit: number) => string;\n  body?: (prevBody: BodyInit, page: number, limit: number) => BodyInit;\n}\n\nclass ServerPaginationLimit extends PipelineProcessor<\n  ServerStorageOptions,\n  ServerPaginationLimitProps\n> {\n  get type(): ProcessorType {\n    return ProcessorType.ServerLimit;\n  }\n\n  _process(options?: ServerStorageOptions): ServerStorageOptions {\n    const updates = {};\n\n    if (this.props.url) {\n      updates['url'] = this.props.url(\n        options.url,\n        this.props.page,\n        this.props.limit,\n      );\n    }\n\n    if (this.props.body) {\n      updates['body'] = this.props.body(\n        options.body,\n        this.props.page,\n        this.props.limit,\n      );\n    }\n\n    return {\n      ...options,\n      ...updates,\n    };\n  }\n}\n\nexport default ServerPaginationLimit;\n", "import { h, Fragment } from 'preact';\nimport PaginationLimit from '../../pipeline/limit/pagination';\nimport { classJoin, className } from '../../util/className';\nimport ServerPaginationLimit from '../../pipeline/limit/serverPagination';\nimport { useConfig } from '../../hooks/useConfig';\nimport { useEffect, useRef, useState } from 'preact/hooks';\nimport { useTranslator } from '../../i18n/language';\n\nexport interface PaginationConfig {\n  limit?: number;\n  page?: number;\n  summary?: boolean;\n  nextButton?: boolean;\n  prevButton?: boolean;\n  buttonsCount?: number;\n  resetPageOnUpdate?: boolean;\n  server?: {\n    url?: (prevUrl: string, page: number, limit: number) => string;\n    body?: (prevBody: BodyInit, page: number, limit: number) => BodyInit;\n  };\n}\n\nexport function Pagination() {\n  const config = useConfig();\n  const {\n    server,\n    summary = true,\n    nextButton = true,\n    prevButton = true,\n    buttonsCount = 3,\n    limit = 10,\n    page = 0,\n    resetPageOnUpdate = true,\n  } = config.pagination as PaginationConfig;\n\n  const processor = useRef<PaginationLimit | ServerPaginationLimit>(null);\n  const [currentPage, setCurrentPage] = useState(page);\n  const [total, setTotal] = useState(0);\n  const _ = useTranslator();\n\n  useEffect(() => {\n    if (server) {\n      processor.current = new ServerPaginationLimit({\n        limit: limit,\n        page: currentPage,\n        url: server.url,\n        body: server.body,\n      });\n\n      config.pipeline.register(processor.current);\n    } else {\n      processor.current = new PaginationLimit({\n        limit: limit,\n        page: currentPage,\n      });\n\n      config.pipeline.register(processor.current);\n    }\n\n    if (processor.current instanceof ServerPaginationLimit) {\n      config.pipeline.on('afterProcess', (tabular) => setTotal(tabular.length));\n    } else if (processor.current instanceof PaginationLimit) {\n      // Pagination (all Limit processors) is the last step in the pipeline\n      // and we assume that at this stage, we have the rows that we care about.\n      // Let's grab the rows before processing Pagination and set total number of rows\n      processor.current.on('beforeProcess', (tabular) =>\n        setTotal(tabular.length),\n      );\n    }\n\n    config.pipeline.on('updated', onUpdate);\n\n    // we need to make sure that the state is set\n    // to the default props when an error happens\n    config.pipeline.on('error', () => {\n      setTotal(0);\n      setCurrentPage(0);\n    });\n\n    return () => {\n      config.pipeline.unregister<object, object>(processor.current);\n      config.pipeline.off('updated', onUpdate);\n    };\n  }, []);\n\n  const onUpdate = (updatedProcessor) => {\n    // this is to ensure that the current page is set to 0\n    // when a processor is updated for some reason\n    if (resetPageOnUpdate && updatedProcessor !== processor.current) {\n      setCurrentPage(0);\n\n      if (processor.current.props.page !== 0) {\n        processor.current.setProps({\n          page: 0,\n        });\n      }\n    }\n  };\n\n  const pages = () => Math.ceil(total / limit);\n\n  const setPage = (page: number) => {\n    if (page >= pages() || page < 0 || page === currentPage) {\n      return null;\n    }\n\n    setCurrentPage(page);\n\n    processor.current.setProps({\n      page: page,\n    });\n  };\n\n  const renderPages = () => {\n    if (buttonsCount <= 0) {\n      return null;\n    }\n\n    // how many pagination buttons to render?\n    const maxCount: number = Math.min(pages(), buttonsCount);\n\n    let pagePivot = Math.min(currentPage, Math.floor(maxCount / 2));\n    if (currentPage + Math.floor(maxCount / 2) >= pages()) {\n      pagePivot = maxCount - (pages() - currentPage);\n    }\n\n    return (\n      <Fragment>\n        {pages() > maxCount && currentPage - pagePivot > 0 && (\n          <Fragment>\n            <button\n              tabIndex={0}\n              role=\"button\"\n              onClick={() => setPage(0)}\n              title={_('pagination.firstPage')}\n              aria-label={_('pagination.firstPage')}\n              className={config.className.paginationButton}\n            >\n              {_('1')}\n            </button>\n            <button\n              tabIndex={-1}\n              className={classJoin(\n                className('spread'),\n                config.className.paginationButton,\n              )}\n            >\n              ...\n            </button>\n          </Fragment>\n        )}\n\n        {Array.from(Array(maxCount).keys())\n          .map((i) => currentPage + (i - pagePivot))\n          .map((i) => (\n            <button\n              tabIndex={0}\n              role=\"button\"\n              onClick={() => setPage(i)}\n              className={classJoin(\n                currentPage === i\n                  ? classJoin(\n                      className('currentPage'),\n                      config.className.paginationButtonCurrent,\n                    )\n                  : null,\n                config.className.paginationButton,\n              )}\n              title={_('pagination.page', i + 1)}\n              aria-label={_('pagination.page', i + 1)}\n            >\n              {_(`${i + 1}`)}\n            </button>\n          ))}\n\n        {pages() > maxCount && pages() > currentPage + pagePivot + 1 && (\n          <Fragment>\n            <button\n              tabIndex={-1}\n              className={classJoin(\n                className('spread'),\n                config.className.paginationButton,\n              )}\n            >\n              ...\n            </button>\n            <button\n              tabIndex={0}\n              role=\"button\"\n              onClick={() => setPage(pages() - 1)}\n              title={_('pagination.page', pages())}\n              aria-label={_('pagination.page', pages())}\n              className={config.className.paginationButton}\n            >\n              {_(`${pages()}`)}\n            </button>\n          </Fragment>\n        )}\n      </Fragment>\n    );\n  };\n\n  const renderSummary = () => {\n    return (\n      <Fragment>\n        {summary && total > 0 && (\n          <div\n            role=\"status\"\n            aria-live=\"polite\"\n            className={classJoin(\n              className('summary'),\n              config.className.paginationSummary,\n            )}\n            title={_('pagination.navigate', currentPage + 1, pages())}\n          >\n            {_('pagination.showing')} <b>{_(`${currentPage * limit + 1}`)}</b>{' '}\n            {_('pagination.to')}{' '}\n            <b>{_(`${Math.min((currentPage + 1) * limit, total)}`)}</b>{' '}\n            {_('pagination.of')} <b>{_(`${total}`)}</b>{' '}\n            {_('pagination.results')}\n          </div>\n        )}\n      </Fragment>\n    );\n  };\n\n  return (\n    <div\n      className={classJoin(\n        className('pagination'),\n        config.className.pagination,\n      )}\n    >\n      {renderSummary()}\n\n      <div className={className('pages')}>\n        {prevButton && (\n          <button\n            tabIndex={0}\n            role=\"button\"\n            disabled={currentPage === 0}\n            onClick={() => setPage(currentPage - 1)}\n            title={_('pagination.previous')}\n            aria-label={_('pagination.previous')}\n            className={classJoin(\n              config.className.paginationButton,\n              config.className.paginationButtonPrev,\n            )}\n          >\n            {_('pagination.previous')}\n          </button>\n        )}\n\n        {renderPages()}\n\n        {nextButton && (\n          <button\n            tabIndex={0}\n            role=\"button\"\n            disabled={pages() === currentPage + 1 || pages() === 0}\n            onClick={() => setPage(currentPage + 1)}\n            title={_('pagination.next')}\n            aria-label={_('pagination.next')}\n            className={classJoin(\n              config.className.paginationButton,\n              config.className.paginationButtonNext,\n            )}\n          >\n            {_('pagination.next')}\n          </button>\n        )}\n      </div>\n    </div>\n  );\n}\n", "export function width(width: string | number, containerWidth?: number): number {\n  if (typeof width == 'string') {\n    if (width.indexOf('%') > -1) {\n      return (containerWidth / 100) * parseInt(width, 10);\n    } else {\n      return parseInt(width, 10);\n    }\n  }\n\n  return width;\n}\n\nexport function px(width: number): string {\n  if (!width) return '';\n  return `${Math.floor(width)}px`;\n}\n", "import { h } from 'preact';\nimport { className } from '../../util/className';\n\n/**\n * ShadowTable renders a hidden table and is used to calculate the column's width\n * when autoWidth option is enabled\n */\nexport function ShadowTable(props: { tableRef: HTMLTableElement }) {\n  const shadowTable = props.tableRef.cloneNode(true) as HTMLTableElement;\n\n  shadowTable.style.position = 'absolute';\n  shadowTable.style.width = '100%';\n  shadowTable.style.zIndex = '-2147483640';\n  shadowTable.style.visibility = 'hidden';\n\n  return (\n    <div\n      ref={(nodeElement) => {\n        nodeElement && nodeElement.appendChild(shadowTable);\n      }}\n    />\n  );\n}\n\nexport function getShadowTableWidths(tempRef: HTMLDivElement): {\n  [columnId: string]: { minWidth: number; width: number };\n} {\n  const tableElement: HTMLTableElement = tempRef.querySelector(\n    'table',\n  ) as HTMLTableElement;\n\n  if (!tableElement) {\n    return {};\n  }\n\n  const tableClassName = tableElement.className;\n  const tableStyle = tableElement.style.cssText;\n  tableElement.className = `${tableClassName} ${className('shadowTable')}`;\n\n  tableElement.style.tableLayout = 'auto';\n  tableElement.style.width = 'auto';\n  tableElement.style.padding = '0';\n  tableElement.style.margin = '0';\n  tableElement.style.border = 'none';\n  tableElement.style.outline = 'none';\n\n  let obj = Array.from(\n    tableElement.parentNode.querySelectorAll<HTMLElement>('thead th'),\n  ).reduce((prev, current) => {\n    current.style.width = `${current.clientWidth}px`;\n\n    return {\n      [current.getAttribute('data-column-id')]: {\n        minWidth: current.clientWidth,\n      },\n      ...prev,\n    };\n  }, {});\n\n  tableElement.className = tableClassName;\n  tableElement.style.cssText = tableStyle;\n  tableElement.style.tableLayout = 'auto';\n\n  obj = Array.from(\n    tableElement.parentNode.querySelectorAll<HTMLElement>('thead th'),\n  ).reduce((prev, current) => {\n    prev[current.getAttribute('data-column-id')]['width'] = current.clientWidth;\n\n    return prev;\n  }, obj);\n\n  return obj;\n}\n", "export function camelCase(str: string): string {\n  if (!str) return '';\n\n  const words = str.split(' ');\n\n  // do not convert strings that are already in camelCase format\n  if (words.length === 1 && /([a-z][A-Z])+/g.test(str)) {\n    return str;\n  }\n\n  return words\n    .map(function (word, index) {\n      // if it is the first word, lowercase all the chars\n      if (index == 0) {\n        return word.toLowerCase();\n      }\n\n      // if it is not the first word only upper case the first char and lowercase the rest\n      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();\n    })\n    .join('');\n}\n", "import { Fragment, FunctionComponent, h } from 'preact';\nimport { useConfig } from './hooks/useConfig';\nimport log from './util/log';\n\nexport enum PluginPosition {\n  <PERSON>er,\n  Footer,\n  Cell,\n}\n\nexport interface Plugin<T extends FunctionComponent> {\n  id: string;\n  position: PluginPosition;\n  component: T;\n  order?: number;\n}\n\nexport class PluginManager {\n  private readonly plugins: Plugin<any>[];\n\n  constructor() {\n    this.plugins = [];\n  }\n\n  get<T extends FunctionComponent>(id: string): Plugin<T> | undefined {\n    return this.plugins.find((p) => p.id === id);\n  }\n\n  add<T extends FunctionComponent<any>>(plugin: Plugin<T>): this {\n    if (!plugin.id) {\n      log.error('Plugin ID cannot be empty');\n      return this;\n    }\n\n    if (this.get(plugin.id)) {\n      log.error(`Duplicate plugin ID: ${plugin.id}`);\n      return this;\n    }\n\n    this.plugins.push(plugin);\n    return this;\n  }\n\n  remove(id: string): this {\n    const plugin = this.get(id);\n\n    if (plugin) {\n      this.plugins.splice(this.plugins.indexOf(plugin), 1);\n    }\n\n    return this;\n  }\n\n  list<T extends FunctionComponent>(position?: PluginPosition): Plugin<T>[] {\n    let plugins: Plugin<T>[];\n\n    if (position != null || position != undefined) {\n      plugins = this.plugins.filter((p) => p.position === position);\n    } else {\n      plugins = this.plugins;\n    }\n\n    return plugins.sort((a, b) => (a.order && b.order ? a.order - b.order : 1));\n  }\n}\n\nexport function PluginRenderer(props: {\n  props?: any;\n  // to render a single plugin\n  pluginId?: string;\n  // to render all plugins in this PluginPosition\n  position?: PluginPosition;\n}) {\n  const config = useConfig();\n\n  if (props.pluginId) {\n    // render a single plugin\n    const plugin = config.plugin.get(props.pluginId);\n\n    if (!plugin) return null;\n\n    return h(\n      Fragment,\n      {},\n      h(plugin.component, {\n        plugin: plugin,\n        ...props.props,\n      }),\n    );\n  } else if (props.position !== undefined) {\n    // render using a specific plugin position\n    return h(\n      Fragment,\n      {},\n      config.plugin.list(props.position).map((p) => {\n        return h(p.component, { plugin: p, ...this.props.props });\n      }),\n    );\n  }\n\n  return null;\n}\n", "/**\n * Centralized logging lib\n *\n * This class needs some improvements but so far it has been used to have a coherent way to log\n */\nclass Logger {\n  private format(message: string, type: string): string {\n    return `[Grid.js] [${type.toUpperCase()}]: ${message}`;\n  }\n\n  error(message: string, throwException = false): void {\n    const msg = this.format(message, 'error');\n\n    if (throwException) {\n      throw Error(msg);\n    } else {\n      console.error(msg);\n    }\n  }\n\n  warn(message: string): void {\n    console.warn(this.format(message, 'warn'));\n  }\n\n  info(message: string): void {\n    console.info(this.format(message, 'info'));\n  }\n}\n\nexport default new Logger();\n", "import { OneDArray, TColumn, TwoDArray } from './types';\nimport Base from './base';\nimport { Config } from './config';\nimport { px, width } from './util/width';\nimport { getShadowTableWidths, ShadowTable } from './view/table/shadow';\nimport { ComponentChild, h, isValidElement, RefObject, render } from 'preact';\nimport { camelCase } from './util/string';\nimport { flatten } from './util/array';\nimport logger from './util/log';\nimport { PluginManager, PluginPosition } from './plugin';\nimport { GenericSortConfig } from './view/plugin/sort/sort';\n\nclass Header extends Base {\n  private _columns: OneDArray<TColumn>;\n\n  constructor() {\n    super();\n\n    this._columns = [];\n  }\n\n  get columns(): OneDArray<TColumn> {\n    return this._columns;\n  }\n\n  set columns(columns) {\n    this._columns = columns;\n  }\n\n  get visibleColumns(): OneDArray<TColumn> {\n    return this._columns.filter((c) => !c.hidden);\n  }\n\n  /**\n   * Tries to automatically adjust the width of columns based on:\n   *    - Header cell content\n   *    - Cell content of the first row\n   *    - Cell content of the last row\n   *\n   * @param config\n   */\n  adjustWidth(\n    config: Config,\n    tableRef: RefObject<HTMLTableElement>,\n    tempRef: RefObject<HTMLDivElement>,\n  ): this {\n    const container: Element = config.container;\n    const autoWidth = config.autoWidth;\n\n    if (!container) {\n      // we can't calculate the width because the container\n      // is unknown at this stage\n      return this;\n    }\n\n    // pixels\n    const containerWidth = container.clientWidth;\n\n    let widths = {};\n\n    if (tableRef.current && autoWidth) {\n      // let's create a shadow table with the first 10 rows of the data\n      // and let the browser to render the table with table-layout: auto\n      // no padding, margin or border to get the minimum space required\n      // to render columns. Once the table is rendered and widths are known,\n      // we unmount the shadow table from the DOM and set the correct width\n      render(\n        h(ShadowTable, {\n          tableRef: tableRef.current,\n        }),\n        tempRef.current,\n      );\n\n      widths = getShadowTableWidths(tempRef.current);\n    }\n\n    for (const column of flatten(Header.tabularFormat(this.columns))) {\n      // because we don't want to set the width of parent THs\n      if (column.columns && column.columns.length > 0) {\n        continue;\n      }\n\n      if (!column.width && autoWidth) {\n        // tries to find the corresponding cell\n        // from the ShadowTable and set the correct width\n\n        if (column.id in widths) {\n          // because a column can be hidden, too\n          column.width = px(widths[column.id]['width']);\n          column.minWidth = px(widths[column.id]['minWidth']);\n        }\n      } else {\n        // column width is already defined\n        // sets the column with based on the width of its container\n        column.width = px(width(column.width, containerWidth));\n      }\n    }\n\n    if (tableRef.current && autoWidth) {\n      // unmount the shadow table from temp\n      render(null, tempRef.current);\n    }\n\n    return this;\n  }\n\n  private setSort(\n    sortConfig: GenericSortConfig | boolean,\n    columns?: OneDArray<TColumn>,\n  ): void {\n    const cols = columns || this.columns || [];\n\n    for (const column of cols) {\n      // sorting can only be enabled for columns without any children\n      if (column.columns && column.columns.length > 0) {\n        column.sort = undefined;\n      } else if (column.sort === undefined && sortConfig) {\n        column.sort = {};\n      } else if (!column.sort) {\n        // false, null, etc.\n        column.sort = undefined;\n      } else if (typeof column.sort === 'object') {\n        column.sort = {\n          ...column.sort,\n        };\n      }\n\n      if (column.columns) {\n        this.setSort(sortConfig, column.columns);\n      }\n    }\n  }\n\n  private setResizable(resizable: boolean, columns?: OneDArray<TColumn>): void {\n    const cols = columns || this.columns || [];\n\n    for (const column of cols) {\n      if (column.resizable === undefined) {\n        column.resizable = resizable;\n      }\n\n      if (column.columns) {\n        this.setResizable(resizable, column.columns);\n      }\n    }\n  }\n\n  private setID(columns?: OneDArray<TColumn>): void {\n    const cols = columns || this.columns || [];\n\n    for (const column of cols) {\n      if (!column.id && typeof column.name === 'string') {\n        // let's guess the column ID if it's undefined\n        column.id = camelCase(column.name);\n      }\n\n      if (!column.id) {\n        logger.error(\n          `Could not find a valid ID for one of the columns. Make sure a valid \"id\" is set for all columns.`,\n        );\n      }\n\n      // nested columns\n      if (column.columns) {\n        this.setID(column.columns);\n      }\n    }\n  }\n\n  private populatePlugins(\n    pluginManager: PluginManager,\n    columns: OneDArray<TColumn>,\n  ): void {\n    // populate the cell columns\n    for (const column of columns) {\n      if (column.plugin !== undefined) {\n        pluginManager.add({\n          id: column.id,\n          ...column.plugin,\n          position: PluginPosition.Cell,\n        });\n      }\n    }\n  }\n\n  static fromColumns(\n    columns: OneDArray<TColumn | string | ComponentChild>,\n  ): Header {\n    const header = new Header();\n\n    for (const column of columns) {\n      if (typeof column === 'string' || isValidElement(column)) {\n        header.columns.push({\n          name: column,\n        });\n      } else if (typeof column === 'object') {\n        const typedColumn = column as TColumn;\n\n        if (typedColumn.columns) {\n          typedColumn.columns = Header.fromColumns(typedColumn.columns).columns;\n        }\n\n        // because the data for that cell is null\n        // if we are trying to render a plugin\n        if (typeof typedColumn.plugin === 'object') {\n          if (typedColumn.data === undefined) {\n            typedColumn.data = null;\n          }\n        }\n\n        // TColumn type\n        header.columns.push(column as TColumn);\n      }\n    }\n\n    return header;\n  }\n\n  static createFromConfig(config: Partial<Config>): Header | null {\n    const header = new Header();\n\n    // TODO: this part needs some refactoring\n    if (config.from) {\n      header.columns = Header.fromHTMLTable(config.from).columns;\n    } else if (config.columns) {\n      header.columns = Header.fromColumns(config.columns).columns;\n    } else if (\n      config.data &&\n      typeof config.data[0] === 'object' &&\n      !(config.data[0] instanceof Array)\n    ) {\n      // if data[0] is an object but not an Array\n      // used for when a JSON payload is provided\n      header.columns = Object.keys(config.data[0]).map((name) => {\n        return { name: name };\n      });\n    }\n\n    if (header.columns.length) {\n      header.setID();\n      header.setSort(config.sort);\n      header.setResizable(config.resizable);\n      header.populatePlugins(config.plugin, header.columns);\n      return header;\n    }\n\n    return null;\n  }\n\n  static fromHTMLTable(element: HTMLElement): Header {\n    const header = new Header();\n    const thead = element.querySelector('thead');\n    const ths = thead.querySelectorAll('th');\n\n    for (const th of ths as any) {\n      header.columns.push({\n        name: th.innerHTML,\n        width: th.width,\n      });\n    }\n\n    return header;\n  }\n\n  /**\n   * Converts the tree-like format of Header to a tabular format\n   *\n   * Example:\n   *\n   *    H1\n   *      H1-H1\n   *      H1-H2\n   *    H2\n   *      H2-H1\n   *\n   *    becomes:\n   *      [\n   *        [H1, H2],\n   *        [H1-H1, H1-H2, H2-H1]\n   *      ]\n   *\n   * @param columns\n   */\n  static tabularFormat(columns: OneDArray<TColumn>): TwoDArray<TColumn> {\n    let result: TwoDArray<TColumn> = [];\n    const cols = columns || [];\n    let nextRow = [];\n\n    if (cols && cols.length) {\n      result.push(cols);\n\n      for (const col of cols) {\n        if (col.columns && col.columns.length) {\n          nextRow = nextRow.concat(col.columns);\n        }\n      }\n\n      if (nextRow.length) {\n        result = result.concat(this.tabularFormat(nextRow));\n      }\n    }\n\n    return result;\n  }\n\n  /**\n   * Returns an array of leaf columns (last columns in the tree)\n   *\n   * @param columns\n   */\n  static leafColumns(columns: OneDArray<TColumn>): OneDArray<TColumn> {\n    let result: OneDArray<TColumn> = [];\n    const cols = columns || [];\n\n    if (cols && cols.length) {\n      for (const col of cols) {\n        if (!col.columns || col.columns.length === 0) {\n          result.push(col);\n        }\n\n        if (col.columns) {\n          result = result.concat(this.leafColumns(col.columns));\n        }\n      }\n    }\n\n    return result;\n  }\n\n  /**\n   * Returns the maximum depth of a column tree\n   * @param column\n   */\n  static maximumDepth(column: TColumn): number {\n    return this.tabularFormat([column]).length - 1;\n  }\n}\n\nexport default Header;\n", "/**\n * Base Storage class. All storage implementation must inherit this class\n */\nimport { TData } from '../types';\n\nabstract class Storage<I> {\n  /**\n   * Returns all rows based on ...args\n   * @param args\n   */\n  abstract get(...args): Promise<StorageResponse>;\n\n  /**\n   * To set all rows\n   *\n   * @param data\n   */\n  set?(data: I | ((...args) => void)): this;\n}\n\nexport interface StorageResponse {\n  data: TData;\n  total: number;\n}\n\nexport default Storage;\n", "import Storage, { StorageResponse } from './storage';\nimport { TData } from '../types';\n\nclass MemoryStorage extends Storage<TData> {\n  private data: (() => TData) | (() => Promise<TData>);\n\n  constructor(data: TData | (() => TData) | (() => Promise<TData>)) {\n    super();\n    this.set(data);\n  }\n\n  public async get(): Promise<StorageResponse> {\n    const data = await this.data();\n\n    return {\n      data: data,\n      total: data.length,\n    };\n  }\n\n  public set(data: TData | (() => TData) | (() => Promise<TData>)): this {\n    if (data instanceof Array) {\n      this.data = (): TData => data;\n    } else if (data instanceof Function) {\n      this.data = data;\n    }\n\n    return this;\n  }\n}\n\nexport default MemoryStorage;\n", "import Storage, { StorageResponse } from './storage';\nimport log from '../util/log';\n\nexport interface ServerStorageOptions extends RequestInit {\n  url: string;\n  // to format the data and columns\n  then?: (data: any) => any[][];\n  // to handle the response from the server. `handle` will\n  // be called first and then `then` callback will be invoked\n  // The purpose of this function is to handle the behaviour\n  // of server and either reject and resolve the initial response\n  // before calling the `then` function\n  handle?: (response: Response) => Promise<any>;\n  total?: (data: any) => number;\n  // to bypass the current implementation of ServerStorage and process the\n  // request manually (e.g. when user wants to connect their own SDK/HTTP Client)\n  data?: (opts: ServerStorageOptions) => Promise<StorageResponse>;\n}\n\nclass ServerStorage extends Storage<ServerStorageOptions> {\n  private readonly options: ServerStorageOptions;\n\n  constructor(options: ServerStorageOptions) {\n    super();\n    this.options = options;\n  }\n\n  private handler(response: Response): Promise<any> {\n    if (typeof this.options.handle === 'function') {\n      return this.options.handle(response);\n    }\n\n    if (response.ok) {\n      return response.json();\n    } else {\n      log.error(\n        `Could not fetch data: ${response.status} - ${response.statusText}`,\n        true,\n      );\n      return null;\n    }\n  }\n\n  public get(options?: ServerStorageOptions): Promise<StorageResponse> {\n    // this.options is the initial config object\n    // options is the runtime config passed by the pipeline (e.g. search component)\n    const opts = {\n      ...this.options,\n      ...options,\n    };\n\n    // if `options.data` is provided, the current ServerStorage\n    // implementation will be ignored and we let options.data to\n    // handle the request. Useful when HTTP client needs to be\n    // replaced with something else\n    if (typeof opts.data === 'function') {\n      return opts.data(opts);\n    }\n\n    return fetch(opts.url, opts)\n      .then(this.handler.bind(this))\n      .then((res) => {\n        return {\n          data: opts.then(res),\n          total: typeof opts.total === 'function' ? opts.total(res) : undefined,\n        };\n      });\n  }\n}\n\nexport default ServerStorage;\n", "import { Config } from '../config';\nimport MemoryStorage from './memory';\nimport Storage from './storage';\nimport ServerStorage from './server';\nimport log from '../util/log';\nimport { decode, html } from '../util/html';\n\nclass StorageUtils {\n  /**\n   * Accepts a Config object and tries to guess and return a Storage type\n   *\n   * @param config\n   */\n  public static createFromConfig(config: Config): Storage<any> {\n    let storage = null;\n    // `data` array is provided\n    if (config.data) {\n      storage = new MemoryStorage(config.data);\n    }\n\n    if (config.from) {\n      storage = new MemoryStorage(this.tableElementToArray(config.from));\n      // remove the source table element from the DOM\n      config.from.style.display = 'none';\n    }\n\n    if (config.server) {\n      storage = new ServerStorage(config.server);\n    }\n\n    if (!storage) {\n      log.error('Could not determine the storage type', true);\n    }\n\n    return storage;\n  }\n\n  /**\n   * Accepts a HTML table element and converts it into a 2D array of data\n   *\n   * TODO: This function can be a step in the pipeline: Convert Table -> Load into a memory storage -> ...\n   *\n   * @param element\n   */\n  static tableElementToArray(element: HTMLElement): any[][] {\n    const arr = [];\n    const tbody = element.querySelector('tbody');\n    const rows = tbody.querySelectorAll('tr');\n\n    for (const row of rows as any) {\n      const cells: HTMLElement[] = row.querySelectorAll('td');\n      const parsedRow = [];\n\n      for (const cell of cells) {\n        // try to capture a TD with single text element first\n        if (\n          cell.childNodes.length === 1 &&\n          cell.childNodes[0].nodeType === Node.TEXT_NODE\n        ) {\n          parsedRow.push(decode(cell.innerHTML));\n        } else {\n          parsedRow.push(html(cell.innerHTML));\n        }\n      }\n\n      arr.push(parsedRow);\n    }\n\n    return arr;\n  }\n}\n\nexport default StorageUtils;\n", "import { PipelineProcessor, ProcessorType } from './processor';\nimport { ID } from '../util/id';\nimport log from '../util/log';\nimport { EventEmitter } from '../util/eventEmitter';\n\ninterface PipelineEvents<R> {\n  /**\n   * Generic updated event. Triggers the callback function when the pipeline\n   * is updated, including when a new processor is registered, a processor's props\n   * get updated, etc.\n   */\n  updated: <T, P>(processor: PipelineProcessor<T, P>) => void;\n  /**\n   * Triggers the callback function when a new\n   * processor is registered successfully\n   */\n  afterRegister: () => void;\n  /**\n   * Triggers the callback when a registered\n   * processor's property is updated\n   */\n  propsUpdated: () => void;\n  /**\n   * Triggers the callback function when the pipeline\n   * is fully processed, before returning the results\n   *\n   * afterProcess will not be called if there is an\n   * error in the pipeline (i.e a step throw an Error)\n   */\n  afterProcess: (prev: R) => void;\n  /**\n   * Triggers the callback function when the pipeline\n   * fails to process all steps or at least one step\n   * throws an Error\n   */\n  error: <T>(prev: T) => void;\n}\n\nclass Pipeline<R> extends EventEmitter<PipelineEvents<R>> {\n  // available steps for this pipeline\n  private readonly _steps: Map<\n    ProcessorType,\n    PipelineProcessor<unknown, unknown>[]\n  > = new Map<ProcessorType, PipelineProcessor<unknown, unknown>[]>();\n  // used to cache the results of processors using their id field\n  private cache: Map<string, unknown> = new Map<string, unknown>();\n  // keeps the index of the last updated processor in the registered\n  // processors list and will be used to invalidate the cache\n  // -1 means all new processors should be processed\n  private lastProcessorIndexUpdated = -1;\n\n  constructor(steps?: PipelineProcessor<unknown, unknown>[]) {\n    super();\n\n    if (steps) {\n      steps.forEach((step) => this.register(step));\n    }\n  }\n\n  /**\n   * Clears the `cache` array\n   */\n  clearCache(): void {\n    this.cache = new Map<string, object>();\n    this.lastProcessorIndexUpdated = -1;\n  }\n\n  /**\n   * Registers a new processor\n   *\n   * @param processor\n   * @param priority\n   */\n  register<T, P>(\n    processor: PipelineProcessor<T, P>,\n    priority: number = null,\n  ): PipelineProcessor<T, P> {\n    if (!processor) {\n      throw Error('Processor is not defined');\n    }\n\n    if (processor.type === null) {\n      throw Error('Processor type is not defined');\n    }\n\n    if (this.findProcessorIndexByID(processor.id) > -1) {\n      throw Error(`Processor ID ${processor.id} is already defined`);\n    }\n\n    // binding the propsUpdated callback to the Pipeline\n    processor.on('propsUpdated', this.processorPropsUpdated.bind(this));\n\n    this.addProcessorByPriority(processor, priority);\n    this.afterRegistered(processor);\n\n    return processor;\n  }\n\n  /**\n   * Tries to register a new processor\n   * @param processor\n   * @param priority\n   */\n  tryRegister<T, P>(\n    processor: PipelineProcessor<T, P>,\n    priority: number = null,\n  ): PipelineProcessor<T, P> | undefined {\n    try {\n      return this.register(processor, priority);\n    } catch (_) {\n      // noop\n    }\n\n    return undefined;\n  }\n\n  /**\n   * Removes a processor from the list\n   *\n   * @param processor\n   */\n  unregister<T, P>(processor: PipelineProcessor<T, P>): void {\n    if (!processor) return;\n    if (this.findProcessorIndexByID(processor.id) === -1) return;\n\n    const subSteps = this._steps.get(processor.type);\n\n    if (subSteps && subSteps.length) {\n      this._steps.set(\n        processor.type,\n        subSteps.filter((proc) => proc != processor),\n      );\n      this.emit('updated', processor);\n    }\n  }\n\n  /**\n   * Registers a new processor\n   *\n   * @param processor\n   * @param priority\n   */\n  private addProcessorByPriority<T, P>(\n    processor: PipelineProcessor<T, P>,\n    priority: number,\n  ): void {\n    let subSteps = this._steps.get(processor.type);\n\n    if (!subSteps) {\n      const newSubStep = [];\n      this._steps.set(processor.type, newSubStep);\n      subSteps = newSubStep;\n    }\n\n    if (priority === null || priority < 0) {\n      subSteps.push(processor);\n    } else {\n      if (!subSteps[priority]) {\n        // slot is empty\n        subSteps[priority] = processor;\n      } else {\n        // slot is NOT empty\n        const first = subSteps.slice(0, priority - 1);\n        const second = subSteps.slice(priority + 1);\n\n        this._steps.set(processor.type, first.concat(processor).concat(second));\n      }\n    }\n  }\n\n  /**\n   * Flattens the _steps Map and returns a list of steps with their correct priorities\n   */\n  get steps(): PipelineProcessor<unknown, unknown>[] {\n    let steps: PipelineProcessor<unknown, unknown>[] = [];\n\n    for (const type of this.getSortedProcessorTypes()) {\n      const subSteps = this._steps.get(type);\n\n      if (subSteps && subSteps.length) {\n        steps = steps.concat(subSteps);\n      }\n    }\n\n    // to remove any undefined elements\n    return steps.filter((s) => s);\n  }\n\n  /**\n   * Accepts ProcessType and returns an array of the registered processes\n   * with the give type\n   *\n   * @param type\n   */\n  getStepsByType(type: ProcessorType): PipelineProcessor<unknown, unknown>[] {\n    return this.steps.filter((process) => process.type === type);\n  }\n\n  /**\n   * Returns a list of ProcessorType according to their priority\n   */\n  private getSortedProcessorTypes(): ProcessorType[] {\n    return Object.keys(ProcessorType)\n      .filter((key) => !isNaN(Number(key)))\n      .map((key) => Number(key));\n  }\n\n  /**\n   * Runs all registered processors based on their correct priority\n   * and returns the final output after running all steps\n   *\n   * @param data\n   */\n  async process(data?: R): Promise<R> {\n    const lastProcessorIndexUpdated = this.lastProcessorIndexUpdated;\n    const steps = this.steps;\n\n    let prev = data;\n\n    try {\n      for (const processor of steps) {\n        const processorIndex = this.findProcessorIndexByID(processor.id);\n\n        if (processorIndex >= lastProcessorIndexUpdated) {\n          // we should execute process() here since the last\n          // updated processor was before \"processor\".\n          // This is to ensure that we always have correct and up to date\n          // data from processors and also to skip them when necessary\n          prev = (await processor.process(prev)) as R;\n          this.cache.set(processor.id, prev);\n        } else {\n          // cached results already exist\n          prev = this.cache.get(processor.id) as R;\n        }\n      }\n    } catch (e) {\n      log.error(e);\n      // trigger the onError callback\n      this.emit('error', prev);\n\n      // rethrow\n      throw e;\n    }\n\n    // means the pipeline is up to date\n    this.lastProcessorIndexUpdated = steps.length;\n\n    // triggers the afterProcess callbacks with the results\n    this.emit('afterProcess', prev);\n\n    return prev;\n  }\n\n  /**\n   * Returns the registered processor's index in _steps array\n   *\n   * @param processorID\n   */\n  private findProcessorIndexByID(processorID: ID): number {\n    return this.steps.findIndex((p) => p.id == processorID);\n  }\n\n  /**\n   * Sets the last updates processors index locally\n   * This is used to invalid or skip a processor in\n   * the process() method\n   */\n  private setLastProcessorIndex<T, P>(\n    processor: PipelineProcessor<T, P>,\n  ): void {\n    const processorIndex = this.findProcessorIndexByID(processor.id);\n\n    if (this.lastProcessorIndexUpdated > processorIndex) {\n      this.lastProcessorIndexUpdated = processorIndex;\n    }\n  }\n\n  private processorPropsUpdated(processor): void {\n    this.setLastProcessorIndex(processor);\n    this.emit('propsUpdated');\n    this.emit('updated', processor);\n  }\n\n  private afterRegistered(processor): void {\n    this.setLastProcessorIndex(processor);\n    this.emit('afterRegister');\n    this.emit('updated', processor);\n  }\n}\n\nexport default Pipeline;\n", "import Storage, { StorageResponse } from '../../storage/storage';\nimport {\n  PipelineProcessor,\n  PipelineProcessorProps,\n  ProcessorType,\n} from '../processor';\n\ninterface StorageExtractorProps extends PipelineProcessorProps {\n  storage: Storage<any>;\n}\n\nclass StorageExtractor extends PipelineProcessor<\n  StorageResponse,\n  StorageExtractorProps\n> {\n  get type(): ProcessorType {\n    return ProcessorType.Extractor;\n  }\n\n  async _process(opts: any): Promise<StorageResponse> {\n    return await this.props.storage.get(opts);\n  }\n}\n\nexport default StorageExtractor;\n", "import { PipelineProcessor, ProcessorType } from '../processor';\nimport Tabular from '../../tabular';\nimport { ArrayResponse } from './storageResponseToArray';\n\nclass ArrayToTabularTransformer extends PipelineProcessor<\n  Tabular,\n  Record<string, any>\n> {\n  get type(): ProcessorType {\n    return ProcessorType.Transformer;\n  }\n\n  _process(arrayResponse: ArrayResponse): Tabular {\n    const tabular = Tabular.fromArray(arrayResponse.data);\n\n    // for server-side storage\n    tabular.length = arrayResponse.total;\n\n    return tabular;\n  }\n}\n\nexport default ArrayToTabularTransformer;\n", "import {\n  PipelineProcessor,\n  PipelineProcessorProps,\n  ProcessorType,\n} from '../processor';\nimport { ServerStorageOptions } from '../../storage/server';\n\ninterface ServerInitiatorProps extends PipelineProcessorProps {\n  serverStorageOptions: ServerStorageOptions;\n}\n\nclass ServerInitiator extends PipelineProcessor<\n  ServerStorageOptions,\n  ServerInitiatorProps\n> {\n  get type(): ProcessorType {\n    return ProcessorType.Initiator;\n  }\n\n  _process(): ServerStorageOptions {\n    return Object.entries(this.props.serverStorageOptions)\n      .filter(([_, val]) => typeof val !== 'function')\n      .reduce(\n        (acc, [k, v]) => ({ ...acc, [k]: v }),\n        {},\n      ) as ServerStorageOptions;\n  }\n}\n\nexport default ServerInitiator;\n", "import {\n  PipelineProcessor,\n  PipelineProcessorProps,\n  ProcessorType,\n} from '../processor';\nimport { StorageResponse } from '../../storage/storage';\nimport { TCell, TData, TDataArray, TDataObject, TwoDArray } from '../../types';\nimport Header from '../../header';\nimport logger from '../../util/log';\n\nexport interface ArrayResponse {\n  data: TwoDArray<TCell>;\n  total: number;\n}\n\ninterface StorageResponseToArrayTransformerProps\n  extends PipelineProcessorProps {\n  header: Header;\n}\n\nclass StorageResponseToArrayTransformer extends PipelineProcessor<\n  ArrayResponse,\n  StorageResponseToArrayTransformerProps\n> {\n  get type(): ProcessorType {\n    return ProcessorType.Transformer;\n  }\n\n  private castData(data: TData): TwoDArray<TCell> {\n    if (!data || !data.length) {\n      return [];\n    }\n\n    if (!this.props.header || !this.props.header.columns) {\n      return data as TwoDArray<TCell>;\n    }\n\n    const columns = Header.leafColumns(this.props.header.columns);\n\n    // if it's a 2d array already\n    if (data[0] instanceof Array) {\n      return (data as TDataArray).map((row) => {\n        let pad = 0;\n\n        return columns.map((column, i) => {\n          // default `data` is provided for this column\n          if (column.data !== undefined) {\n            pad++;\n\n            if (typeof column.data === 'function') {\n              return column.data(row);\n            } else {\n              return column.data;\n            }\n          }\n\n          return row[i - pad];\n        });\n      });\n    }\n\n    // if it's an array of objects (but not array of arrays, i.e JSON payload)\n    if (typeof data[0] === 'object' && !(data[0] instanceof Array)) {\n      return (data as TDataObject).map((row) =>\n        columns.map((column, i) => {\n          if (column.data !== undefined) {\n            if (typeof column.data === 'function') {\n              return column.data(row);\n            } else {\n              return column.data;\n            }\n          } else if (column.id) {\n            return row[column.id];\n          } else {\n            logger.error(`Could not find the correct cell for column at position ${i}.\n                          Make sure either 'id' or 'selector' is defined for all columns.`);\n            return null;\n          }\n        }),\n      );\n    }\n\n    return [];\n  }\n\n  _process(storageResponse: StorageResponse): ArrayResponse {\n    return {\n      data: this.castData(storageResponse.data),\n      total: storageResponse.total,\n    };\n  }\n}\n\nexport default StorageResponseToArrayTransformer;\n", "import { Config } from '../config';\nimport Pipeline from './pipeline';\nimport Tabular from '../tabular';\nimport StorageExtractor from './extractor/storage';\nimport ArrayToTabularTransformer from './transformer/arrayToTabular';\nimport ServerStorage from '../storage/server';\nimport ServerInitiator from './initiator/server';\nimport StorageResponseToArrayTransformer from './transformer/storageResponseToArray';\n\nclass PipelineUtils {\n  static createFromConfig(config: Config): Pipeline<Tabular> {\n    const pipeline = new Pipeline<Tabular>();\n\n    if (config.storage instanceof ServerStorage) {\n      pipeline.register(\n        new ServerInitiator({\n          serverStorageOptions: config.server,\n        }),\n      );\n    }\n\n    pipeline.register(new StorageExtractor({ storage: config.storage }));\n    pipeline.register(\n      new StorageResponseToArrayTransformer({ header: config.header }),\n    );\n    pipeline.register(new ArrayToTabularTransformer());\n\n    return pipeline;\n  }\n}\n\nexport default PipelineUtils;\n", "export class Store<S = Record<string, unknown>> {\n  private state: S;\n  private listeners: ((current?: S, prev?: S) => void)[] = [];\n  private isDispatching = false;\n\n  constructor(initialState: S) {\n    this.state = initialState;\n  }\n\n  getState = () => this.state;\n  getListeners = () => this.listeners;\n\n  dispatch = (reducer: (state: S) => S) => {\n    if (typeof reducer !== 'function')\n      throw new Error('Reducer is not a function');\n    if (this.isDispatching)\n      throw new Error('Reducers may not dispatch actions');\n\n    this.isDispatching = true;\n\n    const prevState = this.state;\n    try {\n      this.state = reducer(this.state);\n    } finally {\n      this.isDispatching = false;\n    }\n\n    for (const listener of this.listeners) {\n      listener(this.state, prevState);\n    }\n\n    return this.state;\n  };\n\n  subscribe = (listener: (current?: S, prev?: S) => void): (() => void) => {\n    if (typeof listener !== 'function')\n      throw new Error('Listener is not a function');\n\n    this.listeners = [...this.listeners, listener];\n    return () =>\n      (this.listeners = this.listeners.filter((lis) => lis !== listener));\n  };\n}\n", "import { CSSDeclaration, OneDArray, Status, TColumn, TData } from './types';\nimport Storage from './storage/storage';\nimport Pipeline from './pipeline/pipeline';\nimport Tabular from './tabular';\nimport { Search, SearchConfig } from './view/plugin/search/search';\nimport { Pagination, PaginationConfig } from './view/plugin/pagination';\nimport Header from './header';\nimport { ServerStorageOptions } from './storage/server';\nimport { GenericSortConfig } from './view/plugin/sort/sort';\nimport { Language, Translator } from './i18n/language';\nimport { ComponentChild, createContext, createRef, RefObject } from 'preact';\nimport StorageUtils from './storage/storageUtils';\nimport PipelineUtils from './pipeline/pipelineUtils';\nimport { EventEmitter } from './util/eventEmitter';\nimport { GridEvents } from './events';\nimport { PluginManager, PluginPosition, Plugin } from './plugin';\nimport Grid from './grid';\nimport { Store } from './state/store';\n\nexport const ConfigContext = createContext(null);\n\nexport interface Config {\n  // a reference to the current Grid.js instance\n  instance: Grid;\n  store: Store;\n  eventEmitter: EventEmitter<GridEvents>;\n  plugin: PluginManager;\n  /** container element that is used to mount the Grid.js to */\n  // TODO: change this to an element reference\n  container?: Element;\n  /** pointer to the main table element */\n  tableRef?: RefObject<HTMLTableElement>;\n  data?: TData | (() => TData) | (() => Promise<TData>);\n  server?: ServerStorageOptions;\n  header?: Header;\n  /** to parse a HTML table and load the data */\n  from: HTMLElement;\n  storage: Storage<any>;\n  /** Pipeline process throttle timeout in milliseconds */\n  processingThrottleMs: number;\n  pipeline: Pipeline<Tabular>;\n  /** to automatically calculate the columns width */\n  autoWidth: boolean;\n  /** sets the width of the container and table */\n  width: string;\n  /** sets the height of the table */\n  height: string;\n  pagination: PaginationConfig | boolean;\n  sort: GenericSortConfig | boolean;\n  translator: Translator;\n  /** fixes the table header to the top of the table */\n  fixedHeader: boolean;\n  /** Resizable columns? */\n  resizable: boolean;\n  columns: OneDArray<TColumn | string | ComponentChild>;\n  search: SearchConfig | boolean;\n  language: Language;\n  plugins?: Plugin<any>[];\n  style?: Partial<{\n    table: CSSDeclaration;\n    td: CSSDeclaration;\n    th: CSSDeclaration;\n    container: CSSDeclaration;\n    header: CSSDeclaration;\n    footer: CSSDeclaration;\n  }>;\n  className?: Partial<{\n    table: string;\n    th: string;\n    thead: string;\n    tbody: string;\n    tr: string;\n    td: string;\n    container: string;\n    footer: string;\n    header: string;\n    search: string;\n    sort: string;\n    pagination: string;\n    paginationSummary: string;\n    paginationButton: string;\n    paginationButtonNext: string;\n    paginationButtonCurrent: string;\n    paginationButtonPrev: string;\n    loading: string;\n    notfound: string;\n    error: string;\n  }>;\n}\n\nexport class Config {\n  public constructor() {\n    Object.assign(this, Config.defaultConfig());\n  }\n  /**\n   * Assigns `updatedConfig` keys to the current config file\n   *\n   * @param partialConfig\n   */\n  assign(partialConfig: Partial<Config>): Config {\n    return Object.assign(this, partialConfig);\n  }\n\n  /**\n   * Updates the config from a partial Config\n   *\n   * @param partialConfig\n   */\n  update(partialConfig: Partial<Config>): Config {\n    if (!partialConfig) return this;\n\n    this.assign(\n      Config.fromPartialConfig({\n        ...this,\n        ...partialConfig,\n      }),\n    );\n\n    return this;\n  }\n\n  static defaultConfig(): Partial<Config> {\n    return {\n      store: new Store({\n        status: Status.Init,\n        header: undefined,\n        data: null,\n      }),\n      plugin: new PluginManager(),\n      tableRef: createRef(),\n      width: '100%',\n      height: 'auto',\n      processingThrottleMs: 100,\n      autoWidth: true,\n      style: {},\n      className: {},\n    };\n  }\n\n  static fromPartialConfig(partialConfig: Partial<Config>): Partial<Config> {\n    const config = new Config().assign(partialConfig);\n\n    // Sort\n    if (typeof partialConfig.sort === 'boolean' && partialConfig.sort) {\n      config.assign({\n        sort: {\n          multiColumn: true,\n        },\n      });\n    }\n\n    // Header\n    config.assign({\n      header: Header.createFromConfig(config),\n    });\n\n    config.assign({\n      storage: StorageUtils.createFromConfig(config),\n    });\n\n    config.assign({\n      pipeline: PipelineUtils.createFromConfig(config),\n    });\n\n    // Translator\n    config.assign({\n      translator: new Translator(config.language),\n    });\n\n    // clear existing plugins list to prevent duplicate errors\n    config.plugin = new PluginManager();\n\n    if (config.search) {\n      // Search\n      config.plugin.add({\n        id: 'search',\n        position: PluginPosition.Header,\n        component: Search,\n      });\n    }\n\n    if (config.pagination) {\n      // Pagination\n      config.plugin.add({\n        id: 'pagination',\n        position: PluginPosition.Footer,\n        component: Pagination,\n      });\n    }\n\n    // Additional plugins\n    if (config.plugins) {\n      config.plugins.forEach((p) => config.plugin.add(p));\n    }\n\n    return config;\n  }\n}\n", "import { h, ComponentChild, JSX } from 'preact';\n\nimport Cell from '../../cell';\nimport { classJoin, className } from '../../util/className';\nimport { CSSDeclaration, TColumn } from '../../types';\nimport Row from '../../row';\nimport { JSXInternal } from 'preact/src/jsx';\nimport { PluginRenderer } from '../../plugin';\nimport { useConfig } from '../../hooks/useConfig';\n\nexport function TD(\n  props: {\n    cell: Cell;\n    row?: Row;\n    column?: TColumn;\n    style?: CSSDeclaration;\n    messageCell?: boolean;\n  } & Omit<JSX.HTMLAttributes<HTMLTableCellElement>, 'style'>,\n) {\n  const config = useConfig();\n\n  const content = (): ComponentChild => {\n    if (props.column && typeof props.column.formatter === 'function') {\n      return props.column.formatter(props.cell.data, props.row, props.column);\n    }\n\n    if (props.column && props.column.plugin) {\n      return (\n        <PluginRenderer\n          pluginId={props.column.id}\n          props={{\n            column: props.column,\n            cell: props.cell,\n            row: props.row,\n          }}\n        />\n      );\n    }\n\n    return props.cell.data;\n  };\n\n  const handleClick = (\n    e: JSX.TargetedMouseEvent<HTMLTableCellElement>,\n  ): void => {\n    if (props.messageCell) return;\n\n    config.eventEmitter.emit(\n      'cellClick',\n      e,\n      props.cell,\n      props.column,\n      props.row,\n    );\n  };\n\n  const getCustomAttributes = (\n    column: TColumn | null,\n  ): JSXInternal.HTMLAttributes<HTMLTableCellElement> => {\n    if (!column) return {};\n\n    if (typeof column.attributes === 'function') {\n      return column.attributes(props.cell.data, props.row, props.column);\n    } else {\n      return column.attributes;\n    }\n  };\n\n  return (\n    <td\n      role={props.role}\n      colSpan={props.colSpan}\n      data-column-id={props.column && props.column.id}\n      className={classJoin(\n        className('td'),\n        props.className,\n        config.className.td,\n      )}\n      style={{\n        ...props.style,\n        ...config.style.td,\n      }}\n      onClick={handleClick}\n      {...getCustomAttributes(props.column)}\n    >\n      {content()}\n    </td>\n  );\n}\n", "import { h, JSX, ComponentChildren } from 'preact';\n\nimport Row from '../../row';\nimport Cell from '../../cell';\nimport { classJoin, className } from '../../util/className';\nimport { TColumn } from '../../types';\nimport { TD } from './td';\nimport Header from '../../header';\nimport { useConfig } from '../../hooks/useConfig';\nimport useSelector from '../../hooks/useSelector';\n\nexport function TR(props: {\n  row?: Row;\n  messageRow?: boolean;\n  children?: ComponentChildren;\n}) {\n  const config = useConfig();\n  const header = useSelector((state) => state.header);\n\n  const getColumn = (cellIndex: number): TColumn => {\n    if (header) {\n      const cols = Header.leafColumns(header.columns);\n\n      if (cols) {\n        return cols[cellIndex];\n      }\n    }\n\n    return null;\n  };\n\n  const handleClick = (\n    e: JSX.TargetedMouseEvent<HTMLTableRowElement>,\n  ): void => {\n    if (props.messageRow) return;\n    config.eventEmitter.emit('rowClick', e, props.row);\n  };\n\n  const getChildren = (): ComponentChildren => {\n    if (props.children) {\n      return props.children;\n    }\n\n    return props.row.cells.map((cell: Cell, i) => {\n      const column = getColumn(i);\n\n      if (column && column.hidden) return null;\n\n      return <TD key={cell.id} cell={cell} row={props.row} column={column} />;\n    });\n  };\n\n  return (\n    <tr\n      className={classJoin(className('tr'), config.className.tr)}\n      onClick={handleClick}\n    >\n      {getChildren()}\n    </tr>\n  );\n}\n", "import { h } from 'preact';\nimport Cell from '../../cell';\nimport { classJoin, className } from '../../util/className';\nimport { TR } from './tr';\nimport { TD } from './td';\n\nexport function MessageRow(props: {\n  message: string;\n  colSpan?: number;\n  className?: string;\n}) {\n  return (\n    <TR messageRow={true}>\n      <TD\n        role=\"alert\"\n        colSpan={props.colSpan}\n        messageCell={true}\n        cell={new Cell(props.message)}\n        className={classJoin(\n          className('message'),\n          props.className ? props.className : null,\n        )}\n      />\n    </TR>\n  );\n}\n", "import { h } from 'preact';\nimport Row from '../../row';\nimport { TR } from './tr';\nimport { classJoin, className } from '../../util/className';\nimport { Status } from '../../types';\nimport { MessageRow } from './messageRow';\nimport { useConfig } from '../../hooks/useConfig';\nimport { useTranslator } from '../../i18n/language';\nimport useSelector from '../../hooks/useSelector';\n\nexport function TBody() {\n  const config = useConfig();\n  const data = useSelector((state) => state.data);\n  const status = useSelector((state) => state.status);\n  const header = useSelector((state) => state.header);\n  const _ = useTranslator();\n\n  const headerLength = () => {\n    if (header) {\n      return header.visibleColumns.length;\n    }\n    return 0;\n  };\n\n  return (\n    <tbody className={classJoin(className('tbody'), config.className.tbody)}>\n      {data &&\n        data.rows.map((row: Row) => {\n          return <TR key={row.id} row={row} />;\n        })}\n\n      {status === Status.Loading && (!data || data.length === 0) && (\n        <MessageRow\n          message={_('loading')}\n          colSpan={headerLength()}\n          className={classJoin(className('loading'), config.className.loading)}\n        />\n      )}\n\n      {status === Status.Rendered && data && data.length === 0 && (\n        <MessageRow\n          message={_('noRecordsFound')}\n          colSpan={headerLength()}\n          className={classJoin(\n            className('notfound'),\n            config.className.notfound,\n          )}\n        />\n      )}\n\n      {status === Status.Error && (\n        <MessageRow\n          message={_('error')}\n          colSpan={headerLength()}\n          className={classJoin(className('error'), config.className.error)}\n        />\n      )}\n    </tbody>\n  );\n}\n", "import { <PERSON><PERSON><PERSON><PERSON>, TCell } from '../../types';\nimport Tabular from '../../tabular';\nimport {\n  PipelineProcessor,\n  PipelineProcessorProps,\n  ProcessorType,\n} from '../processor';\nimport Row from '../../row';\nimport log from '../../util/log';\n\ninterface NativeSortProps extends PipelineProcessorProps {\n  columns: {\n    index: number;\n    // 1 ascending, -1 descending\n    direction?: 1 | -1;\n    compare?: Comparator<TCell>;\n  }[];\n}\n\nclass NativeSort extends PipelineProcessor<Tabular, NativeSortProps> {\n  protected validateProps(): void {\n    for (const condition of this.props.columns) {\n      if (condition.direction === undefined) {\n        condition.direction = 1;\n      }\n\n      if (condition.direction !== 1 && condition.direction !== -1) {\n        log.error(`Invalid sort direction ${condition.direction}`);\n      }\n    }\n  }\n\n  get type(): ProcessorType {\n    return ProcessorType.Sort;\n  }\n\n  private compare(cellA: TCell, cellB: TCell): number {\n    if (cellA > cellB) {\n      return 1;\n    } else if (cellA < cellB) {\n      return -1;\n    }\n\n    return 0;\n  }\n\n  private compareWrapper(a: Row, b: Row): number {\n    let cmp = 0;\n\n    for (const column of this.props.columns) {\n      if (cmp === 0) {\n        const cellA = a.cells[column.index].data;\n        const cellB = b.cells[column.index].data;\n\n        if (typeof column.compare === 'function') {\n          cmp |= column.compare(cellA, cellB) * column.direction;\n        } else {\n          cmp |= this.compare(cellA, cellB) * column.direction;\n        }\n      } else {\n        break;\n      }\n    }\n\n    return cmp;\n  }\n\n  protected _process(data: Tabular): Tabular {\n    const sortedRows = [...data.rows];\n    sortedRows.sort(this.compareWrapper.bind(this));\n\n    const sorted = new Tabular(sortedRows);\n    // we have to set the tabular length manually\n    // because of the server-side storage\n    sorted.length = data.length;\n\n    return sorted;\n  }\n}\n\nexport default NativeSort;\n", "import { Comparator, TCell } from '../../../types';\n\nexport const SortColumn =\n  (\n    index: number,\n    direction: 1 | -1,\n    multi?: boolean,\n    compare?: Comparator<TCell>,\n  ) =>\n  (state) => {\n    let columns = state.sort?.columns\n      ? state.sort.columns.map((x) => {\n          return { ...x };\n        })\n      : [];\n    const count = columns.length;\n    const column = columns.find((x) => x.index === index);\n    const exists = column !== undefined;\n\n    let add = false;\n    let reset = false;\n    let remove = false;\n    let update = false;\n\n    if (!exists) {\n      // the column has not been sorted\n      if (count === 0) {\n        // the first column to be sorted\n        add = true;\n      } else if (count > 0 && !multi) {\n        // remove the previously sorted column\n        // and sort the current column\n        add = true;\n        reset = true;\n      } else if (count > 0 && multi) {\n        // multi-sorting\n        // sort this column as well\n        add = true;\n      }\n    } else {\n      // the column has been sorted before\n      if (!multi) {\n        // single column sorting\n        if (count === 1) {\n          update = true;\n        } else if (count > 1) {\n          // this situation happens when we have already entered\n          // multi-sorting mode but then user tries to sort a single column\n          reset = true;\n          add = true;\n        }\n      } else {\n        // multi sorting\n        if (column.direction === -1) {\n          // remove the current column from the\n          // sorted columns array\n          remove = true;\n        } else {\n          update = true;\n        }\n      }\n    }\n\n    if (reset) {\n      // resetting the sorted columns\n      columns = [];\n    }\n\n    if (add) {\n      columns.push({\n        index: index,\n        direction: direction,\n        compare: compare,\n      });\n    } else if (update) {\n      const index = columns.indexOf(column);\n      columns[index].direction = direction;\n    } else if (remove) {\n      const index = columns.indexOf(column);\n      columns.splice(index, 1);\n    }\n\n    return {\n      ...state,\n      sort: {\n        columns: columns,\n      },\n    };\n  };\n\nexport const SortToggle =\n  (index: number, multi: boolean, compare?: Comparator<TCell>) => (state) => {\n    const columns = state.sort ? [...state.sort.columns] : [];\n    const column = columns.find((x) => x.index === index);\n\n    if (!column) {\n      return {\n        ...state,\n        ...SortColumn(index, 1, multi, compare)(state),\n      };\n    }\n\n    return {\n      ...state,\n      ...SortColumn(\n        index,\n        column.direction === 1 ? -1 : 1,\n        multi,\n        compare,\n      )(state),\n    };\n  };\n", "import {\n  PipelineProcessor,\n  PipelineProcessorProps,\n  ProcessorType,\n} from '../processor';\nimport { ServerStorageOptions } from '../../storage/server';\nimport { TColumnSort } from '../../types';\n\ninterface ServerSortProps extends PipelineProcessorProps {\n  columns: TColumnSort[];\n  url?: (prevUrl: string, columns: TColumnSort[]) => string;\n  body?: (prevBody: BodyInit, columns: TColumnSort[]) => BodyInit;\n}\n\nclass ServerSort extends PipelineProcessor<\n  ServerStorageOptions,\n  ServerSortProps\n> {\n  get type(): ProcessorType {\n    return ProcessorType.ServerSort;\n  }\n\n  _process(options?: ServerStorageOptions): ServerStorageOptions {\n    const updates = {};\n\n    if (this.props.url) {\n      updates['url'] = this.props.url(options.url, this.props.columns);\n    }\n\n    if (this.props.body) {\n      updates['body'] = this.props.body(options.body, this.props.columns);\n    }\n\n    return {\n      ...options,\n      ...updates,\n    };\n  }\n}\n\nexport default ServerSort;\n", "import { h, JSX } from 'preact';\n\nimport { classJoin, className } from '../../../util/className';\nimport { PipelineProcessor, ProcessorType } from '../../../pipeline/processor';\nimport NativeSort from '../../../pipeline/sort/native';\nimport { Comparator, TCell, TColumnSort } from '../../../types';\nimport * as actions from './actions';\nimport ServerSort from '../../../pipeline/sort/server';\nimport { useEffect, useState } from 'preact/hooks';\nimport { useConfig } from '../../../hooks/useConfig';\nimport { useTranslator } from '../../../i18n/language';\nimport useSelector from '../../../hooks/useSelector';\nimport { useStore } from '../../../hooks/useStore';\n\n// column specific config\nexport interface SortConfig {\n  compare?: Comparator<TCell>;\n  // 1 ascending, -1 descending\n  direction?: 1 | -1;\n}\n\n// generic sort config:\n//\n// Config {\n//    sort: GenericSortConfig\n// }\n//\nexport interface GenericSortConfig {\n  multiColumn?: boolean;\n  server?: {\n    url?: (prevUrl: string, columns: TColumnSort[]) => string;\n    body?: (prevBody: BodyInit, columns: TColumnSort[]) => BodyInit;\n  };\n}\n\nexport function Sort(\n  props: {\n    // column index\n    index: number;\n  } & SortConfig,\n) {\n  const config = useConfig();\n  const { dispatch } = useStore();\n  const _ = useTranslator();\n  const [direction, setDirection] = useState(0);\n  const sortConfig = config.sort as GenericSortConfig;\n  const state = useSelector((state) => state.sort);\n  const processorType =\n    typeof sortConfig?.server === 'object'\n      ? ProcessorType.ServerSort\n      : ProcessorType.Sort;\n\n  const getSortProcessor = () => {\n    const processors = config.pipeline.getStepsByType(processorType);\n    if (processors.length) {\n      return processors[0];\n    }\n    return undefined;\n  };\n\n  const createSortProcessor = () => {\n    if (processorType === ProcessorType.ServerSort) {\n      return new ServerSort({\n        columns: state ? state.columns : [],\n        ...sortConfig.server,\n      });\n    }\n\n    return new NativeSort({\n      columns: state ? state.columns : [],\n    });\n  };\n\n  const getOrCreateSortProcessor = (): PipelineProcessor<any, any> => {\n    const existingSortProcessor = getSortProcessor();\n    if (existingSortProcessor) {\n      return existingSortProcessor;\n    }\n\n    return createSortProcessor();\n  };\n\n  useEffect(() => {\n    const processor = getOrCreateSortProcessor();\n    config.pipeline.tryRegister(processor);\n\n    return () => config.pipeline.unregister(processor);\n  }, [config]);\n\n  /**\n   * Sets the internal state of component\n   */\n  useEffect(() => {\n    if (!state) return;\n\n    const currentColumn = state.columns.find((x) => x.index === props.index);\n\n    if (!currentColumn) {\n      setDirection(0);\n    } else {\n      // if the direction is not set, initialize the selected\n      // column direction with the passed prop (default to ascending)\n      if (direction === 0) {\n        currentColumn.direction = props.direction ?? 1;\n      }\n      setDirection(currentColumn.direction);\n    }\n  }, [state]);\n\n  useEffect(() => {\n    const processor = getSortProcessor();\n\n    if (!processor) return;\n    if (!state) return;\n\n    processor.setProps({\n      columns: state.columns,\n    });\n  }, [state]);\n\n  const changeDirection = (e: JSX.TargetedMouseEvent<HTMLButtonElement>) => {\n    e.preventDefault();\n    e.stopPropagation();\n    // to sort two or more columns at the same time\n    dispatch(\n      actions.SortToggle(\n        props.index,\n        e.shiftKey === true && sortConfig.multiColumn,\n        props.compare,\n      ),\n    );\n  };\n\n  const getSortClassName = (direction: number) => {\n    if (direction === 1) {\n      return 'asc';\n    } else if (direction === -1) {\n      return 'desc';\n    }\n\n    return 'neutral';\n  };\n\n  return (\n    <button\n      // because the corresponding <th> has tabIndex=0\n      tabIndex={-1}\n      aria-label={_(`sort.sort${direction === 1 ? 'Desc' : 'Asc'}`)}\n      title={_(`sort.sort${direction === 1 ? 'Desc' : 'Asc'}`)}\n      className={classJoin(\n        className('sort'),\n        className('sort', getSortClassName(direction)),\n        config.className.sort,\n      )}\n      onClick={changeDirection}\n    />\n  );\n}\n", "/**\n * Throttle a given function\n * @param fn Function to be called\n * @param wait Throttle timeout in milliseconds\n * @returns Throttled function\n */\nexport const throttle = (fn: (...args) => void, wait = 100) => {\n  let timeoutId: ReturnType<typeof setTimeout>;\n  let lastTime = Date.now();\n\n  const execute = (...args) => {\n    lastTime = Date.now();\n    fn(...args);\n  };\n\n  return (...args) => {\n    const currentTime = Date.now();\n    const elapsed = currentTime - lastTime;\n\n    if (elapsed >= wait) {\n      // If enough time has passed since the last call, execute the function immediately\n      execute(...args);\n    } else {\n      // If not enough time has passed, schedule the function call after the remaining delay\n      if (timeoutId) {\n        clearTimeout(timeoutId);\n      }\n\n      timeoutId = setTimeout(() => {\n        execute(...args);\n        timeoutId = null;\n      }, wait - elapsed);\n    }\n  };\n};\n", "import { h, RefObject } from 'preact';\nimport { classJoin, className } from '../../../util/className';\nimport { TColumn } from '../../../types';\nimport { throttle } from '../../../util/throttle';\n\nexport function Resize(props: {\n  column: TColumn;\n  thRef: RefObject<HTMLTableCellElement>;\n}) {\n  let moveFn: (e) => void;\n\n  const getPageX = (e: MouseEvent | TouchEvent) => {\n    if (e instanceof MouseEvent) {\n      return Math.floor(e.pageX);\n    } else {\n      return Math.floor(e.changedTouches[0].pageX);\n    }\n  };\n\n  const start = (e: MouseEvent | TouchEvent) => {\n    e.stopPropagation();\n\n    const thElement = props.thRef.current;\n\n    const offsetStart = parseInt(thElement.style.width, 10) - getPageX(e);\n\n    moveFn = throttle((e) => move(e, offsetStart), 10);\n\n    document.addEventListener('mouseup', end);\n    document.addEventListener('touchend', end);\n    document.addEventListener('mousemove', moveFn);\n    document.addEventListener('touchmove', moveFn);\n  };\n\n  const move = (e: MouseEvent | TouchEvent, offsetStart: number) => {\n    e.stopPropagation();\n\n    const thElement = props.thRef.current;\n\n    if (offsetStart + getPageX(e) >= parseInt(thElement.style.minWidth, 10)) {\n      thElement.style.width = `${offsetStart + getPageX(e)}px`;\n    }\n  };\n\n  const end = (e: MouseEvent | TouchEvent) => {\n    e.stopPropagation();\n\n    document.removeEventListener('mouseup', end);\n    document.removeEventListener('mousemove', moveFn);\n    document.removeEventListener('touchmove', moveFn);\n    document.removeEventListener('touchend', end);\n  };\n\n  return (\n    <div\n      className={classJoin(className('th'), className('resizable'))}\n      onMouseDown={start}\n      onTouchStart={start}\n      onClick={(e) => e.stopPropagation()}\n    />\n  );\n}\n", "import { h, ComponentChil<PERSON>, JSX } from 'preact';\n\nimport { classJoin, className } from '../../util/className';\nimport { CSSDeclaration, TColumn } from '../../types';\nimport { GenericSortConfig, Sort } from '../plugin/sort/sort';\nimport { PluginRenderer } from '../../plugin';\nimport { Resize } from '../plugin/resize/resize';\nimport { useEffect, useRef, useState } from 'preact/hooks';\nimport { useConfig } from '../../hooks/useConfig';\nimport * as SortActions from '../plugin/sort/actions';\nimport { useStore } from '../../hooks/useStore';\n\nexport function TH(\n  props: {\n    index: number;\n    column: TColumn;\n    style?: CSSDeclaration;\n  } & Omit<JSX.HTMLAttributes<HTMLTableCellElement>, 'style'>,\n) {\n  const config = useConfig();\n  const thRef = useRef(null);\n  const [style, setStyle] = useState({});\n  const { dispatch } = useStore();\n\n  useEffect(() => {\n    // sets the `top` style if the current TH is fixed\n    if (config.fixedHeader && thRef.current) {\n      const offsetTop = thRef.current.offsetTop;\n\n      if (typeof offsetTop === 'number') {\n        setStyle({\n          top: offsetTop,\n        });\n      }\n    }\n  }, [thRef]);\n\n  const isSortable = (): boolean => props.column.sort != undefined;\n  const isResizable = (): boolean => props.column.resizable;\n  const onClick = (\n    e:\n      | JSX.TargetedMouseEvent<HTMLTableCellElement>\n      | JSX.TargetedKeyboardEvent<HTMLTableCellElement>,\n  ) => {\n    e.stopPropagation();\n\n    if (isSortable()) {\n      const sortConfig = config.sort as GenericSortConfig;\n\n      dispatch(\n        SortActions.SortToggle(\n          props.index,\n          e.shiftKey === true && sortConfig.multiColumn,\n          props.column.sort.compare,\n        ),\n      );\n    }\n  };\n\n  const keyDown = (e: JSX.TargetedKeyboardEvent<HTMLTableCellElement>) => {\n    // Enter key\n    if (isSortable() && e.which === 13) {\n      onClick(e);\n    }\n  };\n\n  const content = (): ComponentChild => {\n    if (props.column.name !== undefined) {\n      return props.column.name;\n    }\n\n    if (props.column.plugin !== undefined) {\n      return (\n        <PluginRenderer\n          pluginId={props.column.plugin.id}\n          props={{\n            column: props.column,\n          }}\n        />\n      );\n    }\n\n    return null;\n  };\n\n  const getCustomAttributes = () => {\n    const column = props.column;\n\n    if (!column) return {};\n\n    if (typeof column.attributes === 'function') {\n      return column.attributes(null, null, props.column);\n    } else {\n      return column.attributes;\n    }\n  };\n\n  return (\n    <th\n      ref={thRef}\n      data-column-id={props.column && props.column.id}\n      className={classJoin(\n        className('th'),\n        isSortable() ? className('th', 'sort') : null,\n        config.fixedHeader ? className('th', 'fixed') : null,\n        config.className.th,\n      )}\n      onClick={onClick}\n      style={{\n        ...config.style.th,\n        ...{\n          minWidth: props.column.minWidth,\n          width: props.column.width,\n        },\n        ...style,\n        ...props.style,\n      }}\n      onKeyDown={keyDown}\n      rowSpan={props.rowSpan > 1 ? props.rowSpan : undefined}\n      colSpan={props.colSpan > 1 ? props.colSpan : undefined}\n      {...getCustomAttributes()}\n      {...(isSortable() ? { tabIndex: 0 } : {})}\n    >\n      <div className={className('th', 'content')}>{content()}</div>\n      {isSortable() && <Sort index={props.index} {...props.column.sort} />}\n      {isResizable() &&\n        props.index < config.header.visibleColumns.length - 1 && (\n          <Resize column={props.column} thRef={thRef} />\n        )}\n    </th>\n  );\n}\n", "import { h } from 'preact';\nimport { TR } from './tr';\nimport { TH } from './th';\nimport { classJoin, className } from '../../util/className';\nimport Header from '../../header';\nimport { TColumn } from '../../types';\nimport { calculateRowColSpans } from '../../util/table';\nimport { useConfig } from '../../hooks/useConfig';\nimport useSelector from '../../hooks/useSelector';\n\nexport function THead() {\n  const config = useConfig();\n  const header = useSelector((state) => state.header);\n\n  const renderColumn = (\n    column: TColumn,\n    rowIndex: number,\n    columnIndex: number,\n    totalRows: number,\n  ) => {\n    const { rowSpan, colSpan } = calculateRowColSpans(\n      column,\n      rowIndex,\n      totalRows,\n    );\n\n    return (\n      <TH\n        column={column}\n        index={columnIndex}\n        colSpan={colSpan}\n        rowSpan={rowSpan}\n      />\n    );\n  };\n\n  const renderRow = (row: TColumn[], rowIndex: number, totalRows: number) => {\n    // because the only sortable columns are leaf columns (not parents)\n    const leafColumns = Header.leafColumns(header.columns);\n\n    return (\n      <TR>\n        {row.map((col) => {\n          if (col.hidden) return null;\n\n          return renderColumn(\n            col,\n            rowIndex,\n            leafColumns.indexOf(col),\n            totalRows,\n          );\n        })}\n      </TR>\n    );\n  };\n\n  const renderRows = () => {\n    const rows = Header.tabularFormat(header.columns);\n\n    return rows.map((row, rowIndex) => renderRow(row, rowIndex, rows.length));\n  };\n\n  if (header) {\n    return (\n      <thead\n        key={header.id}\n        className={classJoin(className('thead'), config.className.thead)}\n      >\n        {renderRows()}\n      </thead>\n    );\n  }\n\n  return null;\n}\n", "import { TColumn } from '../types';\nimport Header from '../header';\n\nexport function calculateRowColSpans(\n  column: TColumn,\n  rowIndex: number,\n  totalRows: number,\n): { rowSpan: number; colSpan: number } {\n  const depth = Header.maximumDepth(column);\n  const remainingRows = totalRows - rowIndex;\n  const rowSpan = Math.floor(remainingRows - depth - depth / remainingRows);\n  const colSpan = (column.columns && column.columns.length) || 1;\n\n  return {\n    rowSpan: rowSpan,\n    colSpan: colSpan,\n  };\n}\n", "import Header from 'src/header';\nimport Tabular from '../tabular';\nimport { Status } from '../types';\n\nexport const SetStatusToRendered = () => (state) => {\n  if (state.status === Status.Loaded) {\n    return {\n      ...state,\n      status: Status.Rendered,\n    };\n  }\n\n  return state;\n};\n\nexport const SetLoadingData = () => (state) => {\n  return {\n    ...state,\n    status: Status.Loading,\n  };\n};\n\nexport const SetData = (data: Tabular) => (state) => {\n  if (!data) return state;\n\n  return {\n    ...state,\n    data: data,\n    status: Status.Loaded,\n  };\n};\n\nexport const SetDataErrored = () => (state) => {\n  return {\n    ...state,\n    data: null,\n    status: Status.Error,\n  };\n};\n\nexport const SetHeader = (header: Header) => (state) => {\n  return {\n    ...state,\n    header: header,\n  };\n};\n\nexport const SetTableRef = (tableRef) => (state) => {\n  return {\n    ...state,\n    tableRef: tableRef,\n  };\n};\n", "import { h } from 'preact';\nimport { TBody } from './tbody';\nimport { THead } from './thead';\nimport { classJoin, className } from '../../util/className';\nimport { useConfig } from '../../hooks/useConfig';\nimport { useEffect, useRef } from 'preact/hooks';\nimport * as actions from '../actions';\nimport { useStore } from '../../hooks/useStore';\n\nexport function Table() {\n  const config = useConfig();\n  const tableRef = useRef(null);\n  const { dispatch } = useStore();\n\n  useEffect(() => {\n    if (tableRef) dispatch(actions.SetTableRef(tableRef));\n  }, [tableRef]);\n\n  return (\n    <table\n      ref={tableRef}\n      role=\"grid\"\n      className={classJoin(className('table'), config.className.table)}\n      style={{\n        ...config.style.table,\n        ...{\n          height: config.height,\n        },\n      }}\n    >\n      <THead />\n      <TBody />\n    </table>\n  );\n}\n", "import { classJoin, className } from '../util/className';\nimport { h } from 'preact';\nimport { PluginPosition, PluginRenderer } from '../plugin';\nimport { useEffect, useRef, useState } from 'preact/hooks';\nimport { useConfig } from '../hooks/useConfig';\n\nexport function HeaderContainer() {\n  const [isActive, setIsActive] = useState(true);\n  const headerRef = useRef(null);\n  const config = useConfig();\n\n  useEffect(() => {\n    if (headerRef.current.children.length === 0) {\n      setIsActive(false);\n    }\n  }, [headerRef]);\n\n  if (isActive) {\n    return (\n      <div\n        ref={headerRef}\n        className={classJoin(className('head'), config.className.header)}\n        style={{ ...config.style.header }}\n      >\n        <PluginRenderer position={PluginPosition.Header} />\n      </div>\n    );\n  }\n\n  return null;\n}\n", "import { h } from 'preact';\nimport { classJoin, className } from '../util/className';\nimport { PluginPosition, PluginRenderer } from '../plugin';\nimport { useEffect, useRef, useState } from 'preact/hooks';\nimport { useConfig } from '../hooks/useConfig';\n\nexport function FooterContainer() {\n  const footerRef = useRef(null);\n  const [isActive, setIsActive] = useState(true);\n  const config = useConfig();\n\n  useEffect(() => {\n    if (footerRef.current.children.length === 0) {\n      setIsActive(false);\n    }\n  }, [footerRef]);\n\n  if (isActive) {\n    return (\n      <div\n        ref={footerRef}\n        className={classJoin(className('footer'), config.className.footer)}\n        style={{ ...config.style.footer }}\n      >\n        <PluginRenderer position={PluginPosition.Footer} />\n      </div>\n    );\n  }\n\n  return null;\n}\n", "import { createRef, h } from 'preact';\nimport { classJoin, className } from '../util/className';\nimport { Status } from '../types';\nimport { Table } from './table/table';\nimport { HeaderContainer } from './headerContainer';\nimport { FooterContainer } from './footerContainer';\nimport log from '../util/log';\nimport { useEffect } from 'preact/hooks';\nimport * as actions from './actions';\nimport { useStore } from '../hooks/useStore';\nimport useSelector from '../hooks/useSelector';\nimport { useConfig } from '../hooks/useConfig';\nimport { throttle } from '../util/throttle';\n\nexport function Container() {\n  const config = useConfig();\n  const { dispatch } = useStore();\n  const status = useSelector((state) => state.status);\n  const data = useSelector((state) => state.data);\n  const tableRef = useSelector((state) => state.tableRef);\n  const tempRef = createRef();\n\n  const processPipeline = throttle(async () => {\n    dispatch(actions.SetLoadingData());\n\n    try {\n      const data = await config.pipeline.process();\n      dispatch(actions.SetData(data));\n\n      // TODO: do we need this setTimemout?\n      setTimeout(() => {\n        dispatch(actions.SetStatusToRendered());\n      }, 0);\n    } catch (e) {\n      log.error(e);\n      dispatch(actions.SetDataErrored());\n    }\n  }, config.processingThrottleMs);\n\n  useEffect(() => {\n    // set the initial header object\n    // we update the header width later when \"data\"\n    // is available in the state\n    dispatch(actions.SetHeader(config.header));\n\n    processPipeline();\n    config.pipeline.on('updated', processPipeline);\n\n    return () => config.pipeline.off('updated', processPipeline);\n  }, []);\n\n  useEffect(() => {\n    if (config.header && status === Status.Loaded && data?.length) {\n      // now that we have the data, let's adjust columns width\n      // NOTE: that we only calculate the columns width once\n      dispatch(\n        actions.SetHeader(config.header.adjustWidth(config, tableRef, tempRef)),\n      );\n    }\n  }, [data, config, tempRef]);\n\n  return (\n    <div\n      role=\"complementary\"\n      className={classJoin(\n        'gridjs',\n        className('container'),\n        status === Status.Loading ? className('loading') : null,\n        config.className.container,\n      )}\n      style={{\n        ...config.style.container,\n        ...{\n          width: config.width,\n        },\n      }}\n    >\n      {status === Status.Loading && (\n        <div className={className('loading-bar')} />\n      )}\n\n      <HeaderContainer />\n\n      <div className={className('wrapper')} style={{ height: config.height }}>\n        <Table />\n      </div>\n\n      <FooterContainer />\n\n      <div ref={tempRef} id=\"gridjs-temp\" className={className('temp')} />\n    </div>\n  );\n}\n", "import { Config } from './config';\nimport { h, render, VNode } from 'preact';\nimport { Container } from './view/container';\nimport log from './util/log';\nimport { EventEmitter } from './util/eventEmitter';\nimport { GridEvents } from './events';\nimport { PluginManager } from './plugin';\nimport { ConfigContext } from './config';\n\nclass Grid extends EventEmitter<GridEvents> {\n  public config: Config;\n  public plugin: PluginManager;\n\n  constructor(config?: Partial<Config>) {\n    super();\n    this.config = new Config()\n      .assign({ instance: this, eventEmitter: this })\n      .update(config);\n    this.plugin = this.config.plugin;\n  }\n\n  public updateConfig(config: Partial<Config>): this {\n    this.config.update(config);\n    return this;\n  }\n\n  createElement(): VNode {\n    return h(ConfigContext.Provider, {\n      value: this.config,\n      children: h(Container, {}),\n    });\n  }\n\n  /**\n   * Uses the existing container and tries to clear the cache\n   * and re-render the existing Grid.js instance again. This is\n   * useful when a new config is set/updated.\n   *\n   */\n  forceRender(): this {\n    if (!this.config || !this.config.container) {\n      log.error(\n        'Container is empty. Make sure you call render() before forceRender()',\n        true,\n      );\n    }\n\n    this.destroy();\n\n    // recreate the Grid instance\n    render(this.createElement(), this.config.container);\n\n    return this;\n  }\n\n  /**\n   * Deletes the Grid.js instance\n   */\n  destroy(): void {\n    this.config.pipeline.clearCache();\n    // TODO: not sure if it's a good idea to render a null element but I couldn't find a better way\n    render(null, this.config.container);\n  }\n\n  /**\n   * Mounts the Grid.js instance to the container\n   * and renders the instance\n   *\n   * @param container\n   */\n  render(container: Element): this {\n    if (!container) {\n      log.error('Container element cannot be null', true);\n    }\n\n    if (container.childNodes.length > 0) {\n      log.error(\n        `The container element ${container} is not empty. Make sure the container is empty and call render() again`,\n      );\n      return this;\n    }\n\n    this.config.container = container;\n    render(this.createElement(), container);\n\n    return this;\n  }\n}\n\nexport default Grid;\n"], "names": ["Status", "n", "l", "u", "i", "t", "o", "r", "f", "e", "c", "s", "a", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "h", "arguments", "length", "children", "call", "defaultProps", "v", "type", "props", "key", "ref", "__k", "__", "__b", "__e", "__d", "__c", "__h", "constructor", "__v", "vnode", "p", "d", "this", "context", "_", "indexOf", "k", "base", "b", "push", "g", "__r", "debounceRendering", "setTimeout", "sort", "some", "__P", "j", "__n", "ownerSVGElement", "z", "w", "y", "x", "C", "Array", "isArray", "m", "A", "N", "M", "append<PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "insertBefore", "$", "setProperty", "test", "H", "style", "cssText", "replace", "toLowerCase", "slice", "addEventListener", "T", "I", "removeEventListener", "removeAttribute", "setAttribute", "event", "contextType", "value", "__E", "prototype", "render", "O", "sub", "state", "_sb", "__s", "getDerivedStateFromProps", "componentWillMount", "componentDidMount", "componentWillReceiveProps", "shouldComponentUpdate", "for<PERSON>ach", "componentWillUpdate", "componentDidUpdate", "getChildContext", "getSnapshotBeforeUpdate", "L", "diffed", "localName", "nodeType", "document", "createTextNode", "createElementNS", "createElement", "is", "data", "childNodes", "dangerouslySetInnerHTML", "attributes", "name", "__html", "innerHTML", "checked", "current", "unmount", "componentWillUnmount", "P", "<PERSON><PERSON><PERSON><PERSON>", "Math", "random", "toString", "getDerivedStateFromError", "setState", "componentDidCatch", "forceUpdate", "Base", "id", "_id", "generateUUID", "_createClass", "HTMLElement", "parentElement", "content", "html", "ProcessorType", "_Base", "Cell", "_this", "update", "_inherits<PERSON><PERSON>e", "_proto", "cast", "outerHTML", "Row", "cells", "_cells", "cell", "index", "toArray", "map", "fromCells", "get", "set", "Tabular", "rows", "_rows", "_length", "row", "fromRows", "fromArray", "oneDtoTwoD", "len", "EventEmitter", "callbacks", "init", "listeners", "on", "listener", "off", "eventName", "filter", "emit", "_arguments", "apply", "deepEqual", "obj1", "obj2", "hasOwnProperty", "keys1", "Object", "keys", "keys2", "_i", "_keys", "PipelineProcessor", "_EventEmitter", "_props", "setProps", "process", "args", "validateProps", "_process", "concat", "updatedProps", "_extends", "GlobalSearchFilter", "keyword", "String", "trim", "columns", "ignoreHiddenColumns", "tabular", "selector", "rowIndex", "cellIndex", "hidden", "element", "Filter", "className", "prefix", "reduce", "prev", "cur", "ServerGlobalSearchFilter", "options", "updates", "url", "body", "<PERSON><PERSON><PERSON><PERSON>", "__H", "__V", "B", "__N", "every", "F", "shift", "requestAnimationFrame", "clearTimeout", "cancelAnimationFrame", "useContext", "ConfigContext", "enUS", "search", "placeholder", "sortAsc", "sortDesc", "pagination", "previous", "next", "navigate", "page", "pages", "showing", "of", "to", "results", "loading", "noRecordsFound", "error", "Translator", "language", "_language", "_defaultLanguage", "getString", "message", "lang", "splitted", "split", "val", "join", "translate", "messageFormat", "translated", "useTranslator", "config", "useConfig", "_config$translator", "translator", "payload", "store", "useSelector", "useStore", "_useState", "useState", "getState", "setCurrent", "useEffect", "subscribe", "updated", "Search", "_config$className", "undefined", "processor", "setProcessor", "dispatch", "server", "header", "actions", "pipeline", "register", "unregister", "func", "waitFor", "timeout", "debouncedOnInput", "useCallback", "target", "HTMLInputElement", "debounceTimeout", "resolve", "classJoin", "onInput", "defaultValue", "PaginationLimit", "isNaN", "Number", "limit", "Error", "Limit", "ServerPaginationLimit", "ServerLimit", "Pagination", "_config$pagination", "summary", "_config$pagination$su", "_config$pagination$ne", "nextButton", "_config$pagination$pr", "prevButton", "_config$pagination$bu", "buttonsCount", "_config$pagination$li", "_config$pagination$pa", "_config$pagination$re", "resetPageOnUpdate", "useRef", "currentPage", "setCurrentPage", "total", "setTotal", "_useState2", "onUpdate", "updatedProcessor", "ceil", "setPage", "Fragment", "role", "paginationSummary", "title", "min", "tabIndex", "disabled", "onClick", "paginationButton", "paginationButtonPrev", "maxCount", "pagePivot", "floor", "from", "paginationButtonCurrent", "renderPages", "paginationButtonNext", "width", "containerWidth", "parseInt", "px", "ShadowTable", "shadowTable", "tableRef", "cloneNode", "position", "zIndex", "visibility", "nodeElement", "camelCase", "str", "words", "word", "char<PERSON>t", "toUpperCase", "PluginPosition", "log", "<PERSON><PERSON>", "format", "throwException", "msg", "console", "warn", "info", "exports", "Plugin<PERSON>anager", "plugins", "find", "add", "plugin", "remove", "splice", "list", "order", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pluginId", "component", "Header", "_columns", "adjustWidth", "tempRef", "container", "autoWidth", "clientWidth", "widths", "getShadowTableWidths", "querySelector", "tableElement", "tableClassName", "tableStyle", "tableLayout", "padding", "margin", "border", "outline", "querySelectorAll", "getAttribute", "min<PERSON><PERSON><PERSON>", "obj", "_step", "_iterator", "_createForOfIteratorHelperLoose", "tabularFormat", "done", "column", "setSort", "sortConfig", "cols", "_step2", "setResizable", "resizable", "_step3", "_iterator3", "setID", "_step4", "_iterator4", "logger", "populatePlugins", "pluginManager", "_step5", "_iterator5", "fromColumns", "_step6", "_iterator6", "isValidElement", "typedColumn", "createFromConfig", "fromHTMLTable", "_step7", "_iterator7", "th", "result", "_step8", "_iterator8", "col", "nextRow", "leafColumns", "_step9", "_iterator9", "maximumDepth", "Storage", "MemoryStorage", "_Storage", "Promise", "reject", "ServerStorage", "handler", "response", "handle", "ok", "json", "status", "statusText", "opts", "fetch", "then", "bind", "res", "StorageUtils", "storage", "tableElementToArray", "display", "arr", "parsedRow", "_iterator2", "Node", "TEXT_NODE", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "documentElement", "textContent", "pact", "_settle", "observer", "_Pact", "onFulfilled", "onRejected", "callback", "_isSettledPact", "thenable", "Pipeline", "steps", "_steps", "cache", "Map", "lastProcessorIndexUpdated", "step", "clearCache", "priority", "findProcessorIndexByID", "processorPropsUpdated", "addProcessorByPriority", "afterRegistered", "tryRegister", "subSteps", "proc", "newSubStep", "second", "first", "getStepsByType", "getSortedProcessorTypes", "_temp3", "_result", "_this2", "_temp2", "array", "check", "_cycle", "processorIndex", "_processor$process", "_catch", "processorID", "findIndex", "setLastProcessorIndex", "StorageExtractor", "Extractor", "arrayResponse", "ArrayToTabularTransformer", "Transformer", "ServerInitiator", "_PipelineProcessor", "entries", "serverStorageOptions", "_ref", "acc", "_extends2", "Initiator", "StorageResponseToArrayTransformer", "castData", "pad", "storageResponse", "PipelineUtils", "initialState", "isDispatching", "getListeners", "reducer", "prevState", "lis", "Consumer", "Provider", "createContext", "Config", "assign", "defaultConfig", "partialConfig", "fromPartialConfig", "Store", "Init", "height", "processingThrottleMs", "multiColumn", "Footer", "TD", "colSpan", "td", "messageCell", "eventEmitter", "formatter", "TR", "tr", "messageRow", "getColumn", "MessageRow", "TBody", "<PERSON><PERSON><PERSON><PERSON>", "visibleColumns", "tbody", "Loading", "Rendered", "notfound", "NativeSort", "condition", "direction", "compare", "cellA", "cellB", "compareWrapper", "cmp", "sortedRows", "sorted", "Sort", "multi", "_state$sort", "reset", "count", "_index2", "SortToggle", "SortColumn", "ServerSort", "setDirection", "processorType", "getSortProcessor", "processors", "currentColumn", "_props$direction", "getSortClassName", "preventDefault", "stopPropagation", "shift<PERSON>ey", "throttle", "fn", "wait", "timeoutId", "lastTime", "Date", "now", "execute", "currentTime", "elapsed", "Resize", "getPageX", "MouseEvent", "pageX", "changedTouches", "start", "offsetStart", "thRef", "moveFn", "move", "end", "thElement", "onMouseDown", "onTouchStart", "TH", "setStyle", "fixedHeader", "offsetTop", "top", "isSortable", "SortActions", "onKeyDown", "which", "rowSpan", "THead", "thead", "renderRow", "totalRows", "columnIndex", "calculateRowColSpans", "depth", "remainingRows", "_calculateRowColSpans", "renderColumn", "<PERSON><PERSON><PERSON><PERSON>", "Table", "table", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isActive", "setIsActive", "headerRef", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "footerRef", "footer", "Container", "processPipeline", "Loaded", "_temp", "Grid", "instance", "_assertThisInitialized", "updateConfig", "forceRender", "destroy"], "mappings": "knEAiECA,GAND,SAAYA,GACVA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,OAAA,GAAA,SACAA,EAAAA,EAAA,SAAA,GAAA,WACAA,EAAAA,EAAA,MAAA,GAAA,OACD,CAND,CAAYA,IAAAA,EAMX,CAAA,ICjED,IAAIC,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,EAAE,CAAE,EAACC,EAAE,GAAGC,EAAE,oEAAoE,SAASC,EAAEV,EAAEC,GAAG,IAAI,IAAIC,KAAKD,EAAED,EAAEE,GAAGD,EAAEC,GAAG,OAAOF,CAAC,CAAC,SAASW,EAAEX,GAAG,IAAIC,EAAED,EAAEY,WAAWX,GAAGA,EAAEY,YAAYb,EAAE,CAAC,SAASc,EAAEb,EAAEC,EAAEC,GAAG,IAAIC,EAAEC,EAAEC,EAAEC,EAAE,CAAE,EAAC,IAAID,KAAKJ,EAAE,OAAOI,EAAEF,EAAEF,EAAEI,GAAG,OAAOA,EAAED,EAAEH,EAAEI,GAAGC,EAAED,GAAGJ,EAAEI,GAAG,GAAGS,UAAUC,OAAO,IAAIT,EAAEU,SAASF,UAAUC,OAAO,EAAEhB,EAAEkB,KAAKH,UAAU,GAAGZ,GAAG,mBAAmBF,GAAG,MAAMA,EAAEkB,aAAa,IAAIb,KAAKL,EAAEkB,kBAAa,IAASZ,EAAED,KAAKC,EAAED,GAAGL,EAAEkB,aAAab,IAAI,OAAOc,EAAEnB,EAAEM,EAAEH,EAAEC,EAAE,KAAK,CAAC,SAASe,EAAEpB,EAAEG,EAAEC,EAAEC,EAAEC,GAAG,IAAIC,EAAE,CAACc,KAAKrB,EAAEsB,MAAMnB,EAAEoB,IAAInB,EAAEoB,IAAInB,EAAEoB,IAAI,KAAKC,GAAG,KAAKC,IAAI,EAAEC,IAAI,KAAKC,SAAI,EAAOC,IAAI,KAAKC,IAAI,KAAKC,iBAAY,EAAOC,IAAI,MAAM3B,IAAIJ,EAAEI,GAAG,OAAO,MAAMA,GAAG,MAAML,EAAEiC,OAAOjC,EAAEiC,MAAM3B,GAAGA,CAAC,CAAmC,SAAS4B,EAAEnC,GAAG,OAAOA,EAAEiB,QAAQ,CAAC,SAASmB,EAAEpC,EAAEC,GAAGoC,KAAKf,MAAMtB,EAAEqC,KAAKC,QAAQrC,CAAC,CAAC,SAASsC,EAAEvC,EAAEC,GAAG,GAAG,MAAMA,EAAE,OAAOD,EAAE0B,GAAGa,EAAEvC,EAAE0B,GAAG1B,EAAE0B,GAAGD,IAAIe,QAAQxC,GAAG,GAAG,KAAK,IAAI,IAAIE,EAAED,EAAED,EAAEyB,IAAIT,OAAOf,IAAI,GAAG,OAAOC,EAAEF,EAAEyB,IAAIxB,KAAK,MAAMC,EAAE0B,IAAI,OAAO1B,EAAE0B,IAAI,MAAM,mBAAmB5B,EAAEqB,KAAKkB,EAAEvC,GAAG,IAAI,CAAC,SAASyC,EAAEzC,GAAG,IAAIC,EAAEC,EAAE,GAAG,OAAOF,EAAEA,EAAE0B,KAAK,MAAM1B,EAAE8B,IAAI,CAAC,IAAI9B,EAAE4B,IAAI5B,EAAE8B,IAAIY,KAAK,KAAKzC,EAAE,EAAEA,EAAED,EAAEyB,IAAIT,OAAOf,IAAI,GAAG,OAAOC,EAAEF,EAAEyB,IAAIxB,KAAK,MAAMC,EAAE0B,IAAI,CAAC5B,EAAE4B,IAAI5B,EAAE8B,IAAIY,KAAKxC,EAAE0B,IAAI,KAAK,CAAC,OAAOa,EAAEzC,EAAE,CAAC,CAAC,SAAS2C,EAAE3C,KAAKA,EAAE6B,MAAM7B,EAAE6B,KAAI,IAAKzB,EAAEwC,KAAK5C,KAAK6C,EAAEC,OAAOzC,IAAIJ,EAAE8C,sBAAsB1C,EAAEJ,EAAE8C,oBAAoBC,YAAYH,EAAE,CAAC,SAASA,IAAI,IAAI,IAAI7C,EAAE6C,EAAEC,IAAI1C,EAAEY,QAAQhB,EAAEI,EAAE6C,KAAK,SAASjD,EAAEC,GAAG,OAAOD,EAAEiC,IAAIN,IAAI1B,EAAEgC,IAAIN,GAAG,GAAGvB,EAAE,GAAGJ,EAAEkD,KAAK,SAASlD,GAAG,IAAIC,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,EAAEN,EAAE6B,MAAMxB,GAAGD,GAAGH,EAAED,GAAGiC,KAAKL,KAAKtB,EAAEL,EAAEkD,OAAOjD,EAAE,IAAIC,EAAEO,EAAE,CAAA,EAAGN,IAAI6B,IAAI7B,EAAE6B,IAAI,EAAEmB,EAAE9C,EAAEF,EAAED,EAAEF,EAAEoD,SAAI,IAAS/C,EAAEgD,gBAAgB,MAAMlD,EAAE2B,IAAI,CAAC1B,GAAG,KAAKH,EAAE,MAAMG,EAAEkC,EAAEnC,GAAGC,EAAED,EAAE2B,KAAKwB,EAAErD,EAAEE,GAAGA,EAAEwB,KAAKvB,GAAGoC,EAAErC,IAAI,EAAE,CAAC,SAASoD,EAAExD,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,EAAEG,EAAEC,EAAEC,GAAG,IAAIG,EAAE2C,EAAErB,EAAEK,EAAEE,EAAEE,EAAEW,EAAEE,EAAEvD,GAAGA,EAAEsB,KAAKjB,EAAEmD,EAAED,EAAE1C,OAAO,IAAId,EAAEuB,IAAI,GAAGX,EAAE,EAAEA,EAAEb,EAAEe,OAAOF,IAAI,GAAG,OAAO2B,EAAEvC,EAAEuB,IAAIX,GAAG,OAAO2B,EAAExC,EAAEa,KAAK,kBAAkB2B,EAAE,KAAK,iBAAiBA,GAAG,iBAAiBA,GAAG,iBAAiBA,EAAErB,EAAE,KAAKqB,EAAE,KAAK,KAAKA,GAAGmB,MAAMC,QAAQpB,GAAGrB,EAAEe,EAAE,CAAClB,SAASwB,GAAG,KAAK,KAAK,MAAMA,EAAEd,IAAI,EAAEP,EAAEqB,EAAEpB,KAAKoB,EAAEnB,MAAMmB,EAAElB,IAAIkB,EAAEjB,IAAIiB,EAAEjB,IAAI,KAAKiB,EAAER,KAAKQ,GAAG,CAAC,GAAGA,EAAEf,GAAGxB,EAAEuC,EAAEd,IAAIzB,EAAEyB,IAAI,EAAE,QAAQS,EAAEsB,EAAE5C,KAAKsB,GAAGK,EAAElB,KAAKa,EAAEb,KAAKkB,EAAEpB,OAAOe,EAAEf,KAAKqC,EAAE5C,QAAG,OAAY,IAAI2C,EAAE,EAAEA,EAAEE,EAAEF,IAAI,CAAC,IAAIrB,EAAEsB,EAAED,KAAKhB,EAAElB,KAAKa,EAAEb,KAAKkB,EAAEpB,OAAOe,EAAEf,KAAK,CAACqC,EAAED,QAAG,EAAO,KAAK,CAACrB,EAAE,IAAI,CAACgB,EAAEpD,EAAEyC,EAAEL,EAAEA,GAAG7B,EAAEH,EAAEC,EAAEC,EAAEG,EAAEC,EAAEC,GAAGgC,EAAEF,EAAEb,KAAK6B,EAAEhB,EAAEjB,MAAMY,EAAEZ,KAAKiC,IAAID,IAAIA,EAAE,IAAIpB,EAAEZ,KAAKgC,EAAEZ,KAAKR,EAAEZ,IAAI,KAAKiB,GAAGe,EAAEZ,KAAKa,EAAEhB,EAAEX,KAAKa,EAAEF,IAAI,MAAME,GAAG,MAAME,IAAIA,EAAEF,GAAG,mBAAmBF,EAAEpB,MAAMoB,EAAEhB,MAAMW,EAAEX,IAAIgB,EAAEZ,IAAInB,EAAEoD,EAAErB,EAAE/B,EAAEV,GAAGU,EAAEqD,EAAE/D,EAAEyC,EAAEL,EAAEsB,EAAEf,EAAEjC,GAAG,mBAAmBR,EAAEmB,OAAOnB,EAAE2B,IAAInB,IAAIA,GAAG0B,EAAER,KAAKlB,GAAGA,EAAEE,YAAYZ,IAAIU,EAAE6B,EAAEH,GAAG,CAAC,IAAIlC,EAAE0B,IAAIiB,EAAE/B,EAAE6C,EAAE7C,KAAK,MAAM4C,EAAE5C,IAAIkD,EAAEN,EAAE5C,GAAG4C,EAAE5C,IAAI,GAAG0C,EAAE,IAAI1C,EAAE,EAAEA,EAAE0C,EAAExC,OAAOF,IAAImD,EAAET,EAAE1C,GAAG0C,IAAI1C,GAAG0C,IAAI1C,GAAG,CAAC,SAASgD,EAAE9D,EAAEC,EAAEC,GAAG,IAAI,IAAIC,EAAEC,EAAEJ,EAAEyB,IAAIpB,EAAE,EAAED,GAAGC,EAAED,EAAEY,OAAOX,KAAKF,EAAEC,EAAEC,MAAMF,EAAEuB,GAAG1B,EAAEC,EAAE,mBAAmBE,EAAEkB,KAAKyC,EAAE3D,EAAEF,EAAEC,GAAG6D,EAAE7D,EAAEC,EAAEA,EAAEC,EAAED,EAAEyB,IAAI3B,IAAI,OAAOA,CAAC,CAAyH,SAAS8D,EAAE/D,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,GAAG,IAAIC,EAAEC,EAAEC,EAAE,QAAG,IAASP,EAAE4B,IAAIvB,EAAEL,EAAE4B,IAAI5B,EAAE4B,SAAI,OAAY,GAAG,MAAM3B,GAAGE,GAAGC,GAAG,MAAMD,EAAEQ,WAAWZ,EAAE,GAAG,MAAMK,GAAGA,EAAEO,aAAaZ,EAAEA,EAAEkE,YAAY9D,GAAGE,EAAE,SAAS,CAAC,IAAIC,EAAEF,EAAEG,EAAE,GAAGD,EAAEA,EAAE4D,cAAc3D,EAAEL,EAAEa,OAAOR,GAAG,EAAE,GAAGD,GAAGH,EAAE,MAAMJ,EAAEA,EAAEoE,aAAahE,EAAEC,GAAGC,EAAED,CAAC,CAAC,YAAO,IAASC,EAAEA,EAAEF,EAAE+D,WAAW,CAA4N,SAASE,EAAErE,EAAEC,EAAEC,GAAG,MAAMD,EAAE,GAAGD,EAAEsE,YAAYrE,EAAEC,GAAGF,EAAEC,GAAG,MAAMC,EAAE,GAAG,iBAAiBA,GAAGO,EAAE8D,KAAKtE,GAAGC,EAAEA,EAAE,IAAI,CAAC,SAASsE,EAAExE,EAAEC,EAAEC,EAAEC,EAAEC,GAAG,IAAIC,EAAEL,EAAE,GAAG,UAAUC,EAAE,GAAG,iBAAiBC,EAAEF,EAAEyE,MAAMC,QAAQxE,MAAM,CAAC,GAAG,iBAAiBC,IAAIH,EAAEyE,MAAMC,QAAQvE,EAAE,IAAIA,EAAE,IAAIF,KAAKE,EAAED,GAAGD,KAAKC,GAAGmE,EAAErE,EAAEyE,MAAMxE,EAAE,IAAI,GAAGC,EAAE,IAAID,KAAKC,EAAEC,GAAGD,EAAED,KAAKE,EAAEF,IAAIoE,EAAErE,EAAEyE,MAAMxE,EAAEC,EAAED,GAAG,MAAM,GAAG,MAAMA,EAAE,IAAI,MAAMA,EAAE,GAAGI,EAAEJ,KAAKA,EAAEA,EAAE0E,QAAQ,WAAW,KAAK1E,EAAEA,EAAE2E,gBAAgB5E,EAAEC,EAAE2E,cAAcC,MAAM,GAAG5E,EAAE4E,MAAM,GAAG7E,EAAEC,IAAID,EAAEC,EAAE,CAAE,GAAED,EAAEC,EAAEA,EAAEI,GAAGH,EAAEA,EAAEC,GAAGH,EAAE8E,iBAAiB7E,EAAEI,EAAE0E,EAAEC,EAAE3E,GAAGL,EAAEiF,oBAAoBhF,EAAEI,EAAE0E,EAAEC,EAAE3E,QAAQ,GAAG,4BAA4BJ,EAAE,CAAC,GAAGG,EAAEH,EAAEA,EAAE0E,QAAQ,cAAc,KAAKA,QAAQ,SAAS,UAAU,GAAG,SAAS1E,GAAG,SAASA,GAAG,SAASA,GAAG,aAAaA,GAAG,aAAaA,GAAGA,KAAKD,EAAE,IAAIA,EAAEC,GAAG,MAAMC,EAAE,GAAGA,EAAE,MAAMF,EAAE,MAAMA,IAAI,mBAAmBE,IAAI,MAAMA,IAAG,IAAKA,IAAI,GAAGD,EAAEuC,QAAQ,KAAKxC,EAAEkF,gBAAgBjF,GAAGD,EAAEmF,aAAalF,EAAEC,GAAG,CAAC,CAAC,SAAS8E,EAAEhF,GAAGqC,KAAKpC,EAAED,EAAEqB,MAAK,GAAIpB,EAAEmF,MAAMnF,EAAEmF,MAAMpF,GAAGA,EAAE,CAAC,SAAS+E,EAAE/E,GAAGqC,KAAKpC,EAAED,EAAEqB,MAAK,GAAIpB,EAAEmF,MAAMnF,EAAEmF,MAAMpF,GAAGA,EAAE,CAAC,SAASoD,EAAEpD,EAAEE,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,GAAG,IAAIE,EAAEG,EAAEM,EAAEqC,EAAElB,EAAEE,EAAEE,EAAEE,EAAEiB,EAAEJ,EAAEK,EAAEJ,EAAEU,EAAEG,EAAEQ,EAAED,EAAE7E,EAAEmB,KAAK,QAAG,IAASnB,EAAE8B,YAAY,OAAO,KAAK,MAAM7B,EAAE4B,MAAMtB,EAAEN,EAAE4B,IAAIvB,EAAEN,EAAE0B,IAAIzB,EAAEyB,IAAI1B,EAAE6B,IAAI,KAAKzB,EAAE,CAACE,KAAKG,EAAEV,EAAE0B,MAAMhB,EAAET,GAAG,IAAIF,EAAE,GAAG,mBAAmB+E,EAAE,CAAC,GAAGlC,EAAE3C,EAAEoB,MAAMwC,GAAGnD,EAAEoE,EAAEM,cAAcjF,EAAEO,EAAEmB,KAAK4B,EAAE/C,EAAEmD,EAAEA,EAAExC,MAAMgE,MAAM3E,EAAEe,GAAGtB,EAAED,EAAE2B,IAAIa,GAAG7B,EAAEZ,EAAE4B,IAAI3B,EAAE2B,KAAKJ,GAAGZ,EAAEyE,KAAK,cAAcR,GAAGA,EAAES,UAAUC,OAAOvF,EAAE4B,IAAIhB,EAAE,IAAIiE,EAAElC,EAAEa,IAAIxD,EAAE4B,IAAIhB,EAAE,IAAIsB,EAAES,EAAEa,GAAG5C,EAAEkB,YAAY+C,EAAEjE,EAAE2E,OAAOC,GAAG5B,GAAGA,EAAE6B,IAAI7E,GAAGA,EAAEQ,MAAMuB,EAAE/B,EAAE8E,QAAQ9E,EAAE8E,MAAM,IAAI9E,EAAEwB,QAAQoB,EAAE5C,EAAEuC,IAAIjD,EAAEgB,EAAEN,EAAEe,KAAI,EAAGf,EAAEiB,IAAI,GAAGjB,EAAE+E,IAAI,IAAI,MAAM/E,EAAEgF,MAAMhF,EAAEgF,IAAIhF,EAAE8E,OAAO,MAAMb,EAAEgB,2BAA2BjF,EAAEgF,KAAKhF,EAAE8E,QAAQ9E,EAAEgF,IAAIpF,EAAE,CAAE,EAACI,EAAEgF,MAAMpF,EAAEI,EAAEgF,IAAIf,EAAEgB,yBAAyBlD,EAAE/B,EAAEgF,OAAOrC,EAAE3C,EAAEQ,MAAMiB,EAAEzB,EAAE8E,MAAMxE,EAAE,MAAM2D,EAAEgB,0BAA0B,MAAMjF,EAAEkF,oBAAoBlF,EAAEkF,qBAAqB,MAAMlF,EAAEmF,mBAAmBnF,EAAEiB,IAAIa,KAAK9B,EAAEmF,uBAAuB,CAAC,GAAG,MAAMlB,EAAEgB,0BAA0BlD,IAAIY,GAAG,MAAM3C,EAAEoF,2BAA2BpF,EAAEoF,0BAA0BrD,EAAEa,IAAI5C,EAAEc,KAAK,MAAMd,EAAEqF,wBAAuB,IAAKrF,EAAEqF,sBAAsBtD,EAAE/B,EAAEgF,IAAIpC,IAAIxD,EAAE+B,MAAM9B,EAAE8B,IAAI,CAAC,IAAInB,EAAEQ,MAAMuB,EAAE/B,EAAE8E,MAAM9E,EAAEgF,IAAI5F,EAAE+B,MAAM9B,EAAE8B,MAAMnB,EAAEe,KAAI,GAAIf,EAAEmB,IAAI/B,EAAEA,EAAE0B,IAAIzB,EAAEyB,IAAI1B,EAAEuB,IAAItB,EAAEsB,IAAIvB,EAAEuB,IAAI2E,QAAQ,SAASpG,GAAGA,IAAIA,EAAE0B,GAAGxB,EAAE,GAAG6D,EAAE,EAAEA,EAAEjD,EAAE+E,IAAI7E,OAAO+C,IAAIjD,EAAEiB,IAAIa,KAAK9B,EAAE+E,IAAI9B,IAAIjD,EAAE+E,IAAI,GAAG/E,EAAEiB,IAAIf,QAAQT,EAAEqC,KAAK9B,GAAG,MAAMd,CAAC,CAAC,MAAMc,EAAEuF,qBAAqBvF,EAAEuF,oBAAoBxD,EAAE/B,EAAEgF,IAAIpC,GAAG,MAAM5C,EAAEwF,oBAAoBxF,EAAEiB,IAAIa,KAAK,WAAW9B,EAAEwF,mBAAmB7C,EAAElB,EAAEE,EAAE,EAAE,CAAC,GAAG3B,EAAEwB,QAAQoB,EAAE5C,EAAEQ,MAAMuB,EAAE/B,EAAEmB,IAAI/B,EAAEY,EAAEqC,IAAInD,EAAE2D,EAAE1D,EAAE6C,IAAIuB,EAAE,EAAE,cAAcU,GAAGA,EAAES,UAAUC,OAAO,CAAC,IAAI3E,EAAE8E,MAAM9E,EAAEgF,IAAIhF,EAAEe,KAAI,EAAG8B,GAAGA,EAAEzD,GAAGS,EAAEG,EAAE2E,OAAO3E,EAAEQ,MAAMR,EAAE8E,MAAM9E,EAAEwB,SAASkC,EAAE,EAAEA,EAAE1D,EAAE+E,IAAI7E,OAAOwD,IAAI1D,EAAEiB,IAAIa,KAAK9B,EAAE+E,IAAIrB,IAAI1D,EAAE+E,IAAI,EAAE,MAAM,GAAG/E,EAAEe,KAAI,EAAG8B,GAAGA,EAAEzD,GAAGS,EAAEG,EAAE2E,OAAO3E,EAAEQ,MAAMR,EAAE8E,MAAM9E,EAAEwB,SAASxB,EAAE8E,MAAM9E,EAAEgF,UAAUhF,EAAEe,OAAOwC,EAAE,IAAIvD,EAAE8E,MAAM9E,EAAEgF,IAAI,MAAMhF,EAAEyF,kBAAkBnG,EAAEM,EAAEA,EAAE,CAAA,EAAGN,GAAGU,EAAEyF,oBAAoBnF,GAAG,MAAMN,EAAE0F,0BAA0B/D,EAAE3B,EAAE0F,wBAAwB/C,EAAElB,IAAIyC,EAAE,MAAMrE,GAAGA,EAAEU,OAAOc,GAAG,MAAMxB,EAAEY,IAAIZ,EAAEW,MAAML,SAASN,EAAE6C,EAAExD,EAAE4D,MAAMC,QAAQmB,GAAGA,EAAE,CAACA,GAAG9E,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,GAAGK,EAAE4B,KAAKxC,EAAE0B,IAAI1B,EAAE6B,IAAI,KAAKjB,EAAEiB,IAAIf,QAAQT,EAAEqC,KAAK9B,GAAG6B,IAAI7B,EAAEyE,IAAIzE,EAAEY,GAAG,MAAMZ,EAAEc,KAAI,CAAE,MAAM,MAAMtB,GAAGJ,EAAE+B,MAAM9B,EAAE8B,KAAK/B,EAAEuB,IAAItB,EAAEsB,IAAIvB,EAAE0B,IAAIzB,EAAEyB,KAAK1B,EAAE0B,IAAI6E,EAAEtG,EAAEyB,IAAI1B,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,EAAEE,IAAIE,EAAEV,EAAEyG,SAAS/F,EAAET,EAA0F,CAAvF,MAAMF,GAAGE,EAAE+B,IAAI,MAAMxB,GAAG,MAAMH,KAAKJ,EAAE0B,IAAIpB,EAAEN,EAAE6B,MAAMtB,EAAEH,EAAEA,EAAEkC,QAAQhC,IAAI,MAAMP,EAAE2B,IAAI5B,EAAEE,EAAEC,EAAE,CAAC,CAAC,SAASoD,EAAEvD,EAAEE,GAAGD,EAAE6B,KAAK7B,EAAE6B,IAAI5B,EAAEF,GAAGA,EAAEkD,KAAK,SAAShD,GAAG,IAAIF,EAAEE,EAAE6B,IAAI7B,EAAE6B,IAAI,GAAG/B,EAAEkD,KAAK,SAASlD,GAAGA,EAAEkB,KAAKhB,EAAE,EAA0B,CAAvB,MAAMF,GAAGC,EAAE2B,IAAI5B,EAAEE,EAAE+B,IAAI,CAAC,EAAE,CAAC,SAASwE,EAAExG,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,EAAEE,EAAEC,GAAG,IAAIC,EAAEI,EAAEM,EAAEqC,EAAEtD,EAAEmB,MAAMa,EAAEjC,EAAEoB,MAAMc,EAAElC,EAAEmB,KAAKoB,EAAE,EAAE,GAAG,QAAQL,IAAI/B,GAAE,GAAI,MAAMC,EAAE,KAAKmC,EAAEnC,EAAEU,OAAOyB,IAAI,IAAI/B,EAAEJ,EAAEmC,KAAK,iBAAiB/B,KAAK0B,IAAIA,EAAE1B,EAAEiG,YAAYvE,EAAE,IAAI1B,EAAEkG,UAAU,CAAC3G,EAAES,EAAEJ,EAAEmC,GAAG,KAAK,KAAK,CAAC,GAAG,MAAMxC,EAAE,CAAC,GAAG,OAAOmC,EAAE,OAAOyE,SAASC,eAAe3E,GAAGlC,EAAEI,EAAEwG,SAASE,gBAAgB,6BAA6B3E,GAAGyE,SAASG,cAAc5E,EAAED,EAAE8E,IAAI9E,GAAG7B,EAAE,KAAKG,GAAE,CAAE,CAAC,GAAG,OAAO2B,EAAEqB,IAAItB,GAAG1B,GAAGR,EAAEiH,OAAO/E,IAAIlC,EAAEiH,KAAK/E,OAAO,CAAC,GAAG7B,EAAEA,GAAGN,EAAEkB,KAAKjB,EAAEkH,YAAYrG,GAAG2C,EAAEtD,EAAEmB,OAAOf,GAAG6G,wBAAwBhG,EAAEe,EAAEiF,yBAAyB3G,EAAE,CAAC,GAAG,MAAMH,EAAE,IAAImD,EAAE,CAAA,EAAGhB,EAAE,EAAEA,EAAExC,EAAEoH,WAAWrG,OAAOyB,IAAIgB,EAAExD,EAAEoH,WAAW5E,GAAG6E,MAAMrH,EAAEoH,WAAW5E,GAAG6C,OAAOlE,GAAGN,KAAKM,IAAIN,GAAGM,EAAEmG,QAAQzG,EAAEyG,QAAQnG,EAAEmG,SAAStH,EAAEuH,aAAavH,EAAEuH,UAAUpG,GAAGA,EAAEmG,QAAQ,IAAI,CAAC,GAAtqI,SAAWvH,EAAEC,EAAEC,EAAEC,EAAEC,GAAG,IAAIC,EAAE,IAAIA,KAAKH,EAAE,aAAaG,GAAG,QAAQA,GAAGA,KAAKJ,GAAGuE,EAAExE,EAAEK,EAAE,KAAKH,EAAEG,GAAGF,GAAG,IAAIE,KAAKJ,EAAEG,GAAG,mBAAmBH,EAAEI,IAAI,aAAaA,GAAG,QAAQA,GAAG,UAAUA,GAAG,YAAYA,GAAGH,EAAEG,KAAKJ,EAAEI,IAAImE,EAAExE,EAAEK,EAAEJ,EAAEI,GAAGH,EAAEG,GAAGF,EAAE,CAA+8HwD,CAAE1D,EAAEkC,EAAEsB,EAAEpD,EAAEI,GAAGW,EAAElB,EAAEuB,IAAI,QAAQ,GAAGgB,EAAEvC,EAAEoB,MAAML,SAASuC,EAAEvD,EAAE2D,MAAMC,QAAQpB,GAAGA,EAAE,CAACA,GAAGvC,EAAEC,EAAEC,EAAEC,GAAG,kBAAkB+B,EAAE9B,EAAEE,EAAEF,EAAEA,EAAE,GAAGH,EAAEsB,KAAKc,EAAEpC,EAAE,GAAGM,GAAG,MAAMH,EAAE,IAAImC,EAAEnC,EAAEU,OAAOyB,KAAK,MAAMnC,EAAEmC,IAAI9B,EAAEL,EAAEmC,IAAIhC,IAAI,UAAU0B,QAAG,KAAUM,EAAEN,EAAEmD,SAAS7C,IAAIxC,EAAEqF,OAAO,aAAalD,IAAIK,GAAG,WAAWL,GAAGK,IAAIgB,EAAE6B,QAAQd,EAAEvE,EAAE,QAAQwC,EAAEgB,EAAE6B,OAAM,GAAI,YAAYnD,QAAG,KAAUM,EAAEN,EAAEsF,UAAUhF,IAAIxC,EAAEwH,SAASjD,EAAEvE,EAAE,UAAUwC,EAAEgB,EAAEgE,SAAQ,GAAI,CAAC,OAAOxH,CAAC,CAAC,SAASgE,EAAEjE,EAAEE,EAAEC,GAAG,IAAI,mBAAmBH,EAAEA,EAAEE,GAAGF,EAAE0H,QAAQxH,CAAqB,CAAnB,MAAMF,GAAGC,EAAE2B,IAAI5B,EAAEG,EAAE,CAAC,CAAC,SAAS6D,EAAEhE,EAAEE,EAAEC,GAAG,IAAIC,EAAEC,EAAE,GAAGJ,EAAE0H,SAAS1H,EAAE0H,QAAQ3H,IAAII,EAAEJ,EAAEwB,OAAOpB,EAAEsH,SAAStH,EAAEsH,UAAU1H,EAAE4B,KAAKqC,EAAE7D,EAAE,KAAKF,IAAI,OAAOE,EAAEJ,EAAE8B,KAAK,CAAC,GAAG1B,EAAEwH,qBAAqB,IAAIxH,EAAEwH,sBAA0C,CAAnB,MAAM5H,GAAGC,EAAE2B,IAAI5B,EAAEE,EAAE,CAACE,EAAEsC,KAAKtC,EAAE+C,IAAI,KAAKnD,EAAE8B,SAAI,CAAM,CAAC,GAAG1B,EAAEJ,EAAEyB,IAAI,IAAIpB,EAAE,EAAEA,EAAED,EAAEY,OAAOX,IAAID,EAAEC,IAAI2D,EAAE5D,EAAEC,GAAGH,EAAEC,GAAG,mBAAmBH,EAAEqB,MAAMlB,GAAG,MAAMH,EAAE4B,KAAKjB,EAAEX,EAAE4B,KAAK5B,EAAE0B,GAAG1B,EAAE4B,IAAI5B,EAAE6B,SAAI,CAAM,CAAC,SAAS6D,EAAE1F,EAAEC,EAAEC,GAAG,OAAOmC,KAAKL,YAAYhC,EAAEE,EAAE,CAAC,SAAS2H,EAAE3H,EAAEC,EAAEC,GAAG,IAAIC,EAAEC,EAAEE,EAAEP,EAAEyB,IAAIzB,EAAEyB,GAAGxB,EAAEC,GAAGG,GAAGD,EAAE,mBAAmBD,GAAG,KAAKA,GAAGA,EAAEqB,KAAKtB,EAAEsB,IAAIjB,EAAE,GAAG4C,EAAEjD,EAAED,IAAIG,GAAGD,GAAGD,GAAGsB,IAAIX,EAAEqB,EAAE,KAAK,CAACjC,IAAII,GAAGC,EAAEA,OAAE,IAASJ,EAAEmD,iBAAiBjD,GAAGD,EAAE,CAACA,GAAGE,EAAE,KAAKH,EAAE2H,WAAW9H,EAAEkB,KAAKf,EAAEgH,YAAY,KAAK3G,GAAGH,GAAGD,EAAEA,EAAEE,EAAEA,EAAEsB,IAAIzB,EAAE2H,WAAWzH,GAAGkD,EAAE/C,EAAEN,EAAE,cCG9zQ,MAAO,uCAAuCyE,QAAQ,QAAS,SAAUlE,GACvE,IAAMH,EAAqB,GAAhByH,KAAKC,SAAiB,EAEjC,OADW,KAALvH,EAAWH,EAAS,EAAJA,EAAW,GACxB2H,SAAS,GACpB,EACF,CDRkhSjI,EAAEQ,EAAEqE,MAAM5E,EAAE,CAAC2B,IAAI,SAAS5B,EAAEC,EAAEC,EAAEC,GAAG,IAAI,IAAIC,EAAEC,EAAEC,EAAEL,EAAEA,EAAEyB,IAAI,IAAItB,EAAEH,EAAE6B,OAAO1B,EAAEsB,GAAG,IAAI,IAAIrB,EAAED,EAAE4B,cAAc,MAAM3B,EAAE6H,2BAA2B9H,EAAE+H,SAAS9H,EAAE6H,yBAAyBlI,IAAIM,EAAEF,EAAEyB,KAAK,MAAMzB,EAAEgI,oBAAoBhI,EAAEgI,kBAAkBpI,EAAEG,GAAG,CAAE,GAAEG,EAAEF,EAAEyB,KAAKvB,EAAE,OAAOF,EAAEmF,IAAInF,CAAc,CAAZ,MAAMH,GAAGD,EAAEC,CAAC,CAAC,MAAMD,CAAC,GAAGE,EAAE,EAAEC,EAAE,SAASH,GAAG,OAAO,MAAMA,QAAG,IAASA,EAAEgC,WAAW,EAAEI,EAAEoD,UAAU2C,SAAS,SAASnI,EAAEC,GAAG,IAAIC,EAAEA,EAAE,MAAMmC,KAAKyD,KAAKzD,KAAKyD,MAAMzD,KAAKuD,MAAMvD,KAAKyD,IAAIzD,KAAKyD,IAAIpF,EAAE,CAAE,EAAC2B,KAAKuD,OAAO,mBAAmB5F,IAAIA,EAAEA,EAAEU,EAAE,GAAGR,GAAGmC,KAAKf,QAAQtB,GAAGU,EAAER,EAAEF,GAAG,MAAMA,GAAGqC,KAAKJ,MAAMhC,GAAGoC,KAAKwD,IAAIjD,KAAK3C,GAAG0C,EAAEN,MAAM,EAAED,EAAEoD,UAAU6C,YAAY,SAASrI,GAAGqC,KAAKJ,MAAMI,KAAKT,KAAI,EAAG5B,GAAGqC,KAAKN,IAAIa,KAAK5C,GAAG2C,EAAEN,MAAM,EAAED,EAAEoD,UAAUC,OAAOtD,EAAE/B,EAAE,GAAGyC,EAAEC,IAAI,EAAExC,EAAE,EEAnqT,IAEnCgI,eAAA,WAGR,SAAYC,EAAAA,GAAOlG,KAFFmG,SAAG,EAGlBnG,KAAKmG,IAAMD,GAAME,GACnB,CAIC,OAJAC,mBAED,WACE,OAAOrG,KAAKmG,GACd,KAACF,CAAA,CATO,GCKJ,SAAqBK,EAACrH,GAC1B,SAASA,EAAMsH,eAAiB,OAAQ,CACtCxB,wBAAyB,CAAEG,OAAQjG,EAAMuH,UAE7C,CCHgBC,SAAAA,EAAKD,EAAiBD,GACpC,OAAO9H,EAAE6H,EAAa,CAAEE,QAASA,EAASD,cAAeA,GAC3D,KCMCG,iBCPC,SAAAC,GAAA,SAAAC,EAAY/B,SAGQ,OAFlBgC,EAAOF,EAAA9H,KAAAmB,OAAAA,MAHF6E,YAKLgC,EAAKC,OAAOjC,GAAMgC,CACpB,CAJAE,EAAAH,EAAAD,GAIC,IAAAK,EAAAJ,EAAAzD,iBAAA6D,EAEOC,KAAA,SAAKpC,GACX,OAAIA,aAA2ByB,YACtBG,EAAK5B,EAAKqC,YAIrB,EAACF,EAOMF,OAAA,SAAOjC,GAEZ,OADA7E,KAAK6E,KAAO7E,KAAKiH,KAAKpC,GAExB7E,IAAA,IAtBA,CAJiBiG,GCDTkB,eAAA,SAAAR,GAGR,SAAYS,EAAAA,GAAc,IAAAP,EAGC,OAFzBA,EAAOF,EAAA9H,KAAAmB,OAAAA,MAHDqH,YAKN,EAAAR,EAAKO,MAAQA,GAAS,GACxBP,CAAA,CAPQE,EAAAI,EAAAR,GAOP,IAEMW,EAAAA,EAAAA,UA6BN,OA7BMA,EAAAA,KAAA,SAAKC,GACV,OAAWvH,KAACqH,OAAOE,EACrB,EAACP,EAUMQ,QAAA,WACL,OAAOxH,KAAKoH,MAAMK,IAAI,SAACH,GAAI,SAAUzC,IAAI,EAC3C,EASO6C,EAAAA,UAAP,SAAiBN,GACf,OAAWD,IAAAA,EAAIC,EAAMK,IAAI,SAACH,GAAI,OAASV,IAAAA,EAAKU,EAAKzC,KAAK,GACxD,EArBAwB,EAAAc,EAAA,CAAA,CAAAjI,IAAA,QAAAyI,IAAA,WACE,OAAW3H,KAACqH,MACd,EAEAO,IAAA,SAAiBR,GACfpH,KAAKqH,OAASD,CAChB,GAAC,CAAAlI,IAAA,SAAAyI,IAiBD,WACE,OAAO3H,KAAKoH,MAAMzI,MACpB,KAACwI,CAAA,CAtCO,CAAQlB,GCEJ4B,eAAA,SAAAlB,GAIZ,SAAYmB,EAAAA,GACV,IAAAjB,EAQC,OARDA,EAAAF,EAAA9H,KAAAmB,aAJM+H,eACAC,aAAO,EAMXnB,EAAKiB,KADHA,aAAgBvG,MACNuG,EACHA,aAAgBX,EACb,CAACW,GAED,GAEhBjB,CAAA,CAdoBZ,OAARc,EAAAc,EAAAlB,GAiCLa,EAAAA,UAAAA,QAAA,WACL,OAAOxH,KAAK8H,KAAKL,IAAI,SAACQ,UAAWA,EAACT,SAAS,EAC7C,EAACK,EASMK,SAAP,SAAgBJ,GACd,OAAWD,IAAAA,EAAQC,EAAKL,IAAI,SAACQ,UAAWd,EAACO,UAAUO,EAAIb,MAAM,GAC/D,EAACS,EASMM,UAAP,SACEtD,GAIA,OAAWgD,IAAAA,GAFXhD,EC9DE,SAAwBA,GAC5B,OAAIA,EAAK,IAAQA,EAAK,aAActD,MAKtCsD,EAJW,CAACA,EAIZ,CDwDWuD,CAAWvD,IAGX4C,IAAI,SAACQ,GAAG,OAASd,IAAAA,EAAIc,EAAIR,IAAI,SAACH,UAAaV,IAAAA,EAAKU,EAAK,GAAE,GAEhE,EA/CAjB,EAAAwB,EAAA,CAAA,CAAA3I,IAAA,OAAAyI,IAAA,WACE,OAAO3H,KAAK+H,KACd,EAACH,IAED,SAASE,GACP9H,KAAK+H,MAAQD,CACf,GAEA,CAAA5I,IAAA,SAAAyI,IAAA,WACE,YAAYK,SAAWhI,KAAK8H,KAAKnJ,MACnC,EAACiJ,IAGD,SAAWS,GACTrI,KAAKgI,QAAUK,CACjB,KA/BoBpC,CAAAA,CAAR,CAAQA,GE8BTqC,4CACHC,eAAS,CAAA,CAAA,IAAAvB,EAAAsB,EAAAnF,UA8DhB,OA9DgB6D,EAKTwB,KAAA,SAAKzF,GACN/C,KAAKuI,YACRvI,KAAKuI,UAAY,CAClB,GAEGxF,IAAU/C,KAAKuI,UAAUxF,KAC3B/C,KAAKuI,UAAUxF,GAAS,GAE5B,EAACiE,EAEDyB,UAAA,WACE,OAAWzI,KAACuI,SACd,EAACvB,EAED0B,GAAA,SACE3F,EACA4F,GAIA,OAFA3I,KAAKwI,KAAKzF,GACV/C,KAAKuI,UAAUxF,GAAiBxC,KAAKoI,GAEvC3I,IAAA,EAEA4I,EAAAA,IAAA,SACE7F,EACA4F,GAEA,IAAME,EAAY9F,EAIlB,OAFA/C,KAAKwI,OAEAxI,KAAKuI,UAAUM,IAAmD,IAArC7I,KAAKuI,UAAUM,GAAWlK,QAK5DqB,KAAKuI,UAAUM,GAAa7I,KAAKuI,UAAUM,GAAWC,OACpD,SAAC7F,GAAK,OAAUA,GAAI0F,CAAQ,GAGvB3I,MAPEA,IAQX,EAACgH,EAED+B,KAAA,SACEhG,GACyC,IAAAiG,EAAAtK,UAE1BmK,EAAG9F,EAIlB,OAFA/C,KAAKwI,KAAKK,GAEN7I,KAAKuI,UAAUM,GAAWlK,OAAS,IACrCqB,KAAKuI,UAAUM,GAAW9E,QAAQ,SAACd,GAAK,OAAUA,EAAAgG,WAAA,EAAA,GAAAzG,MAAA3D,KAAAmK,EAAA,GAAS,IAE5D,EAGH,EAACV,CAAA,IC7FaY,SAAAA,EAAgBC,EAASC,GAEvC,UAAeD,UAAYC,EACzB,OAAO,EAGT,GAAa,OAATD,GAA0B,OAATC,EACnB,OAAO,EAGT,GAAoB,iBAATD,EAGT,OAAOA,IAASC,EAGlB,GAAI7H,MAAMC,QAAQ2H,IAAS5H,MAAMC,QAAQ4H,GAAO,CAC9C,GAAID,EAAKxK,SAAWyK,EAAKzK,OACvB,OACD,EACD,IAAK,IAAKb,EAAG,EAAGA,EAAIqL,EAAKxK,OAAQb,IAC/B,IAAKoL,EAAUC,EAAKrL,GAAIsL,EAAKtL,IAC3B,OAAO,EAGX,OAAO,CACR,CAED,GAEEqL,EAAKE,eAAe,gBAEpBD,EAAKC,eAAe,gBAEpBF,EAAKE,eAAe,UAEpBD,EAAKC,eAAe,UAEpBF,EAAKE,eAAe,QAEpBD,EAAKC,eAAe,QAEpBF,EAAKE,eAAe,QAEpBD,EAAKC,eAAe,QAEpBF,EAAKE,eAAe,SAEpBD,EAAKC,eAAe,QAEpB,OAAOH,EAAUC,EAAY,MAAGC,EAAY,OAG9C,IAAWE,EAAGC,OAAOC,KAAKL,GACfM,EAAGF,OAAOC,KAAKJ,GAC1B,GAAIE,EAAM3K,SAAW8K,EAAM9K,OACzB,OAAO,EAET,IAAA,IAAA+K,EAAA,EAAAC,EAAkBL,EAAOI,EAAAC,EAAAhL,OAAA+K,IAAA,CAApB,IAAMxK,EAETyK,EAAAD,GAAA,IAAKN,EAAKC,eAAenK,KAASgK,EAAUC,EAAKjK,GAAMkK,EAAKlK,IAC1D,OAAO,CAEV,CACD,OACF,CAAA,ENjEA,SAAYwH,GACVA,EAAAA,EAAA,UAAA,GAAA,YACAA,EAAAA,EAAA,aAAA,GAAA,eACAA,EAAAA,EAAA,WAAA,GAAA,aACAA,EAAAA,EAAA,YAAA,GAAA,cACAA,EAAAA,EAAA,UAAA,GAAA,YACAA,EAAAA,EAAA,YAAA,GAAA,cACAA,EAAAA,EAAA,OAAA,GAAA,SACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,MAAA,GAAA,OACD,CAVD,CAAYA,IAAAA,EAUX,CAAA,IAWD,IAGEkD,gBAAA,SAAAC,GAQA,WAAY5K,GACV,IAAA4H,EAKgC,OALhCA,EAAAgD,EAAAhL,KAAAmB,OAAQA,MARMkG,QAAE,EAAAW,EACViD,YASN,EAAAjD,EAAKiD,OAAS,CAAO,EACrBjD,EAAKX,GAAKE,IAENnH,GAAO4H,EAAKkD,SAAS9K,IAC3B,CAfA8H,EAAA6C,EAAAC,GAeC,IAAA7C,EAAA4C,EAAAzG,iBAAA6D,EAQDgD,QAAA,WAAWC,IAAAA,EACT,GAAAzH,MAAA3D,KAAAH,WAAIsB,KAAKkK,mCACPlK,KAAKkK,cAALjB,MAAAjJ,KAAsBiK,GAGxBjK,KAAK+I,KAALE,MAAAjJ,KAAU,CAAA,wBAAoBiK,IAC9B,MAAejK,KAAKmK,eAALnK,KAAiBiK,GAEhC,OADAjK,KAAK+I,WAAL/I,KAAI,CAAM,gBAAcoK,OAAKH,KAE/B,EAACjD,EAED+C,SAAA,SAAS9K,GACP,IAAkBoL,EAAAC,EAAA,CAAA,EACbtK,KAAK8J,OACL7K,GAQL,OALKiK,EAAUmB,EAAcrK,KAAK8J,UAChC9J,KAAK8J,OAASO,EACdrK,KAAK+I,KAAK,eAAgB/I,WAI9B,EAACqG,EAAAuD,EAAA,CAAA,CAAA1K,IAAA,QAAAyI,IAED,WACE,OAAW3H,KAAC8J,MACd,OAlDA,CAAQxB,GOdJiC,gBAQJJ,SAAAA,GAAAA,SAAAA,IAAAA,OAAAA,EAAAA,MAAAA,KAAAA,YAAAA,IAAAA,QAAAA,EAAAA,EAAAA,GAAAA,EAAAA,UAAAA,SAAA,SAAStF,GACP,OAAI7E,KAAKf,MAAMuL,SCnBjBA,EDqBMC,OAAOzK,KAAKf,MAAMuL,SAASE,OCpBjCC,EDqBM3K,KAAKf,MAAM0L,QCpBjBC,EDqBM5K,KAAKf,MAAM2L,oBCpBjBC,EDqBMhG,ECpBNiG,EDqBM9K,KAAKf,MAAM6L,SClBjBN,EAAUA,EAAQlI,QAAQ,2BAA4B,QAE3CuF,IAAAA,EACTgD,EAAQ/C,KAAKgB,OAAO,SAACb,EAAK8C,GAAQ,OAC7B9C,EAACb,MAAMvG,KAAK,SAACyG,EAAM0D,GACpB,IAAK1D,EACH,OAAO,EAGT,GAAIsD,GAEAD,GACAA,EAAQK,IACsB,iBAAhBL,EAACK,IAEKL,EAAQK,GACZC,OACd,OAAO,EAKb,IAAQpG,EAAG,GAEX,GAAwB,mBAALiG,EACjBjG,EAAOiG,EAASxD,EAAKzC,KAAMkG,EAAUC,QAC5B,GAAqB,mBAATnG,KAAmB,CAExC,IAAMqG,EAAU5D,EAAKzC,KACjBqG,GAAWA,EAAQjM,OAASiM,EAAQjM,MAAMuH,UAE5C3B,EAAOqG,EAAQjM,MAAMuH,QAExB,MAEC3B,EAAO4F,OAAOnD,EAAKzC,MAGrB,OAAO,WAAW2F,EAAS,MAAMtI,KAAK2C,EACxC,EAAE,KDhBNA,EC9BA2F,IAAAA,EACAG,EACAC,EACAC,EACAC,CD0BA,uBAhBA,WACE,OAAOpE,EAAcyE,MACvB,OAEAhB,CAR+BP,aEdRwB,KACvB,MAAe,SAEf,MAAA,GAAUC,EAAS,GAAA7I,MAAA3D,KAAAH,WAAK4M,OACtB,SAACC,EAAcC,GAAW,SAAgBA,IAAAA,CAAG,EAC7C,GAEJ,CAEgB,cAGd,MAAO,GAAAhJ,MAAA3D,KAAAH,WACJ+I,IAAI,SAACpG,GAAC,SAAUA,EAAEuE,WAAa,EAAE,GACjCkD,OAAO,SAACzH,UAAOA,CAAA,GACfiK,OAAO,SAACF,EAAWG,UAAYH,GAAa,IAAE,IAAIG,CAAI,EAAI,IAC1Db,MACL,CCfsB,ICJe3M,GAAEE,GAAEJ,GAAEC,GDarC2N,gBAQJtB,SAAAA,GAAAA,SAAAA,IAAAA,OAAAA,EAAAA,MAAAA,KAAAA,YAAAA,IAAAA,CARqCP,OAQrCO,EAAAA,EAAAA,GAAAA,EAAAA,UAAAA,SAAA,SAASuB,GACP,IAAK1L,KAAKf,MAAMuL,QAAS,OAAckB,EAEvC,IAAaC,EAAG,CAAE,EAUlB,OARI3L,KAAKf,MAAM2M,MACbD,EAAa,IAAI3L,KAAKf,MAAM2M,IAAIF,EAAQE,IAAK5L,KAAKf,MAAMuL,UAGtDxK,KAAKf,MAAM4M,OACbF,EAAc,KAAI3L,KAAKf,MAAM4M,KAAKH,EAAQG,KAAM7L,KAAKf,MAAMuL,UAG7DF,EAAA,GACKoB,EACAC,EAEP,EArBAtF,EAAAoF,EAAA,CAAA,CAAAvM,IAAA,OAAAyI,IAAA,WACE,OAAoBjB,EAACoF,YACvB,KANqClC,CAAAA,CAQrCO,CARqCP,ICbM5L,GAAE,EAAEE,GAAE,GAAGE,GAAE,GAAGD,GAAER,EAAE2B,IAAIhB,GAAEX,EAAE8C,IAAI1B,GAAEpB,EAAE0G,OAAOzG,GAAED,EAAE8B,IAAIgC,GAAE9D,EAAE2H,QAAQ,SAASvF,GAAEhC,EAAEF,GAAGF,EAAE+B,KAAK/B,EAAE+B,IAAIzB,GAAEF,EAAEC,IAAGH,GAAGG,GAAE,EAAE,IAAIF,EAAEG,GAAE8N,MAAM9N,GAAE8N,IAAI,CAAC1M,GAAG,GAAGK,IAAI,KAAK,OAAO3B,GAAGD,EAAEuB,GAAGV,QAAQb,EAAEuB,GAAGkB,KAAK,CAACyL,IAAI5N,KAAIN,EAAEuB,GAAGtB,EAAE,CAAC,SAAS+B,GAAEnC,GAAG,OAAOK,GAAE,EAAS,SAAWL,EAAEE,EAAEC,GAAG,IAAIE,EAAE+B,GAAEhC,KAAI,GAAG,GAAGC,EAAED,EAAEJ,GAAGK,EAAEyB,MAAMzB,EAAEqB,GAAG,CAAQ4M,QAAE,EAAOpO,GAAG,SAASF,GAAG,IAAII,EAAEC,EAAEkO,IAAIlO,EAAEkO,IAAI,GAAGlO,EAAEqB,GAAG,GAAGpB,EAAED,EAAED,EAAEA,EAAEJ,GAAGI,IAAIE,IAAID,EAAEkO,IAAI,CAACjO,EAAED,EAAEqB,GAAG,IAAIrB,EAAEyB,IAAIqG,SAAS,CAAA,GAAI,GAAG9H,EAAEyB,IAAIxB,IAAGA,GAAEJ,GAAG,CAACI,GAAEJ,GAAE,EAAG,IAAIK,EAAED,GAAE6F,sBAAsB7F,GAAE6F,sBAAsB,SAASnG,EAAEI,EAAEE,GAAG,IAAID,EAAEyB,IAAIsM,IAAI,OAAM,EAAG,IAAIlO,EAAEG,EAAEyB,IAAIsM,IAAI1M,GAAGyJ,OAAO,SAASnL,GAAG,OAAOA,EAAE8B,GAAG,GAAG,GAAG5B,EAAEsO,MAAM,SAASxO,GAAG,OAAOA,EAAEuO,GAAG,GAAG,OAAOhO,GAAGA,EAAEW,KAAKmB,KAAKrC,EAAEI,EAAEE,GAAG,IAAIH,GAAE,EAAG,OAAOD,EAAEkG,QAAQ,SAASpG,GAAG,GAAGA,EAAEuO,IAAI,CAAC,IAAInO,EAAEJ,EAAE0B,GAAG,GAAG1B,EAAE0B,GAAG1B,EAAEuO,IAAIvO,EAAEuO,SAAI,EAAOnO,IAAIJ,EAAE0B,GAAG,KAAKvB,GAAE,EAAG,CAAC,MAAMA,GAAGE,EAAEyB,IAAIR,QAAQtB,MAAMO,GAAGA,EAAEW,KAAKmB,KAAKrC,EAAEI,EAAEE,GAAG,CAAC,CAAC,OAAOD,EAAEkO,KAAKlO,EAAEqB,EAAE,CAAnlB+B,CAAE6K,GAAEtO,EAAE,CAA8kB,SAASc,GAAEZ,EAAEC,GAAG,IAAIE,EAAE+B,GAAEhC,KAAI,IAAIJ,EAAE8F,KAAKvC,GAAElD,EAAE+N,IAAIjO,KAAKE,EAAEqB,GAAGxB,EAAEG,EAAEF,EAAEA,EAAEG,GAAE8N,IAAIrM,IAAIa,KAAKvC,GAAG,CAAiF,SAASkC,GAAEvC,GAAG,OAAOK,GAAE,EAAEoO,GAAE,WAAW,MAAM,CAAC/G,QAAQ1H,EAAE,EAAE,GAAG,CAAsL,SAASyO,GAAEzO,EAAEM,GAAG,IAAIJ,EAAEkC,GAAEhC,KAAI,GAAG,OAAOmD,GAAErD,EAAEkO,IAAI9N,IAAIJ,EAAEmO,IAAIrO,IAAIE,EAAEC,EAAEG,EAAEJ,EAAE6B,IAAI/B,EAAEE,EAAEmO,KAAKnO,EAAEwB,EAAE,CAAqiB,SAASiB,KAAI,IAAI,IAAIvC,EAAEA,EAAEG,GAAEmO,SAAS,GAAGtO,EAAE+C,KAAK/C,EAAEgO,IAAI,IAAIhO,EAAEgO,IAAIrM,IAAIqE,QAAQ3D,IAAGrC,EAAEgO,IAAIrM,IAAIqE,QAAQ5C,IAAGpD,EAAEgO,IAAIrM,IAAI,EAAuC,CAApC,MAAMzB,GAAGF,EAAEgO,IAAIrM,IAAI,GAAG/B,EAAE4B,IAAItB,EAAEF,EAAE6B,IAAI,CAAC,CAACjC,EAAE2B,IAAI,SAAS3B,GAAGM,GAAE,KAAKE,IAAGA,GAAER,EAAE,EAAEA,EAAE8C,IAAI,SAAS9C,GAAGW,IAAGA,GAAEX,GAAGI,GAAE,EAAE,IAAID,GAAGG,GAAEN,EAAE8B,KAAKsM,IAAIjO,IAAID,KAAII,IAAGH,EAAE4B,IAAI,GAAGzB,GAAEyB,IAAI,GAAG5B,EAAEuB,GAAG0E,QAAQ,SAASpG,GAAGA,EAAEuO,MAAMvO,EAAE0B,GAAG1B,EAAEuO,KAAKvO,EAAEqO,IAAI5N,GAAET,EAAEuO,IAAIvO,EAAEG,OAAE,CAAM,KAAKA,EAAE4B,IAAIqE,QAAQ3D,IAAGtC,EAAE4B,IAAIqE,QAAQ5C,IAAGrD,EAAE4B,IAAI,KAAK7B,GAAEI,EAAC,EAAEN,EAAE0G,OAAO,SAAStG,GAAGgB,IAAGA,GAAEhB,GAAG,IAAIC,EAAED,EAAE0B,IAAIzB,GAAGA,EAAE+N,MAAM/N,EAAE+N,IAAIrM,IAAIf,SAAS,IAAIT,GAAEqC,KAAKvC,IAAIF,KAAIH,EAAE2O,yBAAyBxO,GAAEH,EAAE2O,wBAAwBvL,IAAGT,KAAItC,EAAE+N,IAAI1M,GAAG0E,QAAQ,SAASpG,GAAGA,EAAEG,IAAIH,EAAEoO,IAAIpO,EAAEG,GAAGH,EAAEqO,MAAM5N,KAAIT,EAAE0B,GAAG1B,EAAEqO,KAAKrO,EAAEG,OAAE,EAAOH,EAAEqO,IAAI5N,EAAC,IAAIP,GAAEI,GAAE,IAAI,EAAEN,EAAE8B,IAAI,SAAS1B,EAAEE,GAAGA,EAAE4C,KAAK,SAAS9C,GAAG,IAAIA,EAAE2B,IAAIqE,QAAQ3D,IAAGrC,EAAE2B,IAAI3B,EAAE2B,IAAIoJ,OAAO,SAASnL,GAAG,OAAOA,EAAE0B,IAAI8B,GAAExD,EAAE,EAAsE,CAAnE,MAAME,GAAGI,EAAE4C,KAAK,SAASlD,GAAGA,EAAE+B,MAAM/B,EAAE+B,IAAI,GAAG,GAAGzB,EAAE,GAAGN,EAAE4B,IAAI1B,EAAEE,EAAE6B,IAAI,CAAC,GAAGhC,IAAGA,GAAEG,EAAEE,EAAE,EAAEN,EAAE2H,QAAQ,SAASvH,GAAG0D,IAAGA,GAAE1D,GAAG,IAAIE,EAAEJ,EAAEE,EAAE0B,IAAI5B,GAAGA,EAAEkO,MAAMlO,EAAEkO,IAAI1M,GAAG0E,QAAQ,SAASpG,GAAG,IAAIyC,GAAEzC,EAAe,CAAZ,MAAMA,GAAGM,EAAEN,CAAC,CAAC,GAAGE,EAAEkO,SAAI,EAAO9N,GAAGN,EAAE4B,IAAItB,EAAEJ,EAAE+B,KAAK,EAAE,IAAIY,GAAE,mBAAmB8L,sBAAsB,SAASvL,GAAEpD,GAAG,IAAII,EAAEE,EAAE,WAAWsO,aAAa1O,GAAG2C,IAAGgM,qBAAqBzO,GAAG4C,WAAWhD,EAAE,EAAEE,EAAE8C,WAAW1C,EAAE,KAAKuC,KAAIzC,EAAEuO,sBAAsBrO,GAAG,CAAC,SAASmC,GAAEzC,GAAG,IAAII,EAAEE,GAAEJ,EAAEF,EAAE8B,IAAI,mBAAmB5B,IAAIF,EAAE8B,SAAI,EAAO5B,KAAKI,GAAEF,CAAC,CAAC,SAASoD,GAAExD,GAAG,IAAII,EAAEE,GAAEN,EAAE8B,IAAI9B,EAAE0B,KAAKpB,GAAEF,CAAC,CAAC,SAASmD,GAAEvD,EAAEI,GAAG,OAAOJ,GAAGA,EAAEgB,SAASZ,EAAEY,QAAQZ,EAAE8C,KAAK,SAAS9C,EAAEE,GAAG,OAAOF,IAAIJ,EAAEM,EAAE,EAAE,CAAC,SAASgO,GAAEtO,EAAEI,GAAG,MAAM,mBAAmBA,EAAEA,EAAEJ,GAAGI,CAAC,eCI5uG,ODJu3C,SAAWJ,GAAG,IAAIE,EAAEI,GAAEgC,QAAQtC,EAAE8B,KAAK3B,EAAEiC,GAAEhC,KAAI,GAAG,OAAOD,EAAEM,EAAET,EAAEE,GAAG,MAAMC,EAAEuB,KAAKvB,EAAEuB,IAAG,EAAGxB,EAAEyF,IAAIrF,KAAIJ,EAAEoB,MAAMgE,OAAOtF,EAAE0B,EAAE,CCIx9CoN,CAACC,GACpB,CCLe,IAAAC,GAAA,CACbC,OAAQ,CACNC,YAAa,qBAEfjM,KAAM,CACJkM,QAAS,wBACTC,SAAU,0BAEZC,WAAY,CACVC,SAAU,WACVC,KAAM,OACNC,SAAU,SAACC,EAAMC,GAAkBD,MAAAA,QAAAA,EAAWC,OAAAA,CAAK,EACnDD,KAAM,SAACA,GAAiBA,MAAAA,QAAAA,CAAI,EAC5BE,QAAS,UACTC,GAAI,KACJC,GAAI,KACJC,QAAS,WAEXC,QAAS,aACTC,eAAgB,4BAChBC,MAAO,6CCdIC,2BAIX,SAAYC,EAAAA,GAAmB9N,KAHd+N,eAAS,EAAA/N,KACTgO,sBAGf,EAAAhO,KAAK+N,UAAYD,EACjB9N,KAAKgO,iBAAmBrB,EAC1B,CAAC,kBA6CA,SApCDsB,UAAA,SAAUC,EAAiBC,GACzB,IAAKA,IAASD,EAAS,OAAW,KAElC,IAAME,EAAWF,EAAQG,MAAM,KACzBnP,EAAMkP,EAAS,GAErB,GAAID,EAAKjP,GAAM,CACb,IAASoP,EAAGH,EAAKjP,GAEjB,MAAmB,iBAALoP,EACL,WAAA,OAAiBA,CAAA,EACA,mBAARA,EAEjBA,EACYtO,KAACiO,UAAUG,EAAS5L,MAAM,GAAG+L,KAAK,KAAMD,EAEtD,CAED,OAAO,IACT,EAACtH,EAEDwH,UAAA,SAAUN,GACR,IACiBO,EADDC,EAAG1O,KAAKiO,UAAUC,EAASlO,KAAK+N,WAShD,OALEU,EADEC,GAGc1O,KAAKiO,UAAUC,EAASlO,KAAKgO,qBAK9C/E,WAAA,EAAA,GAAAzG,MAAA3D,KAAAH,UAAA,IAEMwP,CACT,EAACL,CAAA,IAGac,SAAAA,KACd,IAAMC,EAASC,KAEf,OAAiBX,SAAAA,GACf,IAAAY,EAAA,OAAOF,EAAAA,EAAOG,YAAWP,UAASvF,MAAA6F,EAAA,CAACZ,GAAO9D,OAAA,GAAA5H,MAAA3D,KAAAH,UAAA,IAC5C,CACF,CCnEO,OAAsB,SAACsQ,GAAY,OAAA,SAACzL,GACzC,OACKA,EAAAA,CAAAA,EAAAA,EACHqJ,CAAAA,OAAQ,CACNpC,QAASwE,IAGf,CAAC,gBCHC,OADeH,KACDI,KAChB,CCFwBC,SAAAA,GAAepE,GACrC,IAAMmE,EAAQE,KACdC,EAA8BC,GAASvE,EAASmE,EAAMK,aAA/CjK,OAASkK,EAAUH,EAAA,GAc1B,OAZAI,GAAU,WASR,OARoBP,EAAMQ,UAAU,WAClC,IAAaC,EAAG5E,EAASmE,EAAMK,YAE3BjK,IAAYqK,GACdH,EAAWG,EAEf,EAGF,EAAG,IAGLrK,CAAA,CCIgBsK,SAAAA,KACd,IAAAC,EAAAR,EAAkCC,QAEhCQ,GAFKC,EAAWC,EAAAA,GAAAA,EAGlBX,EAAA,KAAeP,OACDD,EAAOhC,SACX+B,KACFqB,EAAab,KAAba,WACMd,GAAY,SAAC3L,UAAeA,EAACqJ,MAAM,GAEjD4C,GAAU,WACHM,GAELA,EAAU/F,SAAS,CACjBS,cAASjH,SAAAA,EAAOiH,SAEpB,EAAG,CAACjH,EAAOuM,IAEXN,GAAU,WAENO,EADE9Q,EAAMgR,OAEN,IAA4BxE,GAAC,CAC3BjB,QAASvL,EAAMuL,QACfoB,IAAK3M,EAAMgR,OAAOrE,IAClBC,KAAM5M,EAAMgR,OAAOpE,OAKrB,IAAItB,GAAmB,CACrBC,QAASvL,EAAMuL,QACfG,QAASiE,EAAOsB,QAAUtB,EAAOsB,OAAOvF,QACxCC,oBACE3L,EAAM2L,0BACwBiF,IAA9B5Q,EAAM2L,oBACRE,SAAU7L,EAAM6L,YAMlB7L,EAAMuL,SAASwF,EAASG,GAAsBlR,EAAMuL,SAC1D,EAAG,CAACvL,IAEJuQ,GAAU,WACR,GAAKM,EAIL,OAFAlB,EAAOwB,SAASC,SAAyBP,GAE5BlB,WAAAA,OAAAA,EAAOwB,SAASE,WAA2BR,EAAU,CACpE,EAAG,CAAClB,EAAQkB,IAEZ,IC3EAS,EACAC,EAEWC,EDwEWC,EP5E4yC,SAAW/S,EAAEI,GAAG,OAAOC,GAAE,EAAEoO,GAAE,WAAW,OAAOzO,CAAC,EAAEI,EAAE,CO4E71C4S,EC3EzBJ,ED6EI,SAACxN,GACKA,EAAM6N,kBAAkBC,kBAC1Bb,EAASG,GAAsBpN,EAAM6N,OAAO3N,OAEhD,EChFJuN,EDiFIV,aAAqBrE,GACjBxM,EAAM6R,iBAAmB,IACzB,EC9EN,WAAA,IAAA9H,EAAAtK,UAAA,OAAA,YAAY,SAACqS,GACPN,GACFlE,aAAakE,GAGfA,EAAU9P,WAAW,WAAA,SAAc4P,iCAAc,EAAEC,EACrD,EAAE,GD0EF,CAACvR,EAAO6Q,IAGV,OACOrR,EAAA,MAAA,CAAA2M,UAAWA,GAAU4F,GAAU,SAAU,OAAFpB,EAAEhB,EAAOxD,gBAAP,EAAAwE,EAAkBhD,UAC9DnO,EAAA,QAAA,CACEO,KAAK,SACL6N,YAAa3M,EAAE,sBACf,aAAYA,EAAE,sBACd+Q,QAASP,EACTtF,UAAW4F,GAAU5F,GAAU,SAAUA,GAAU,SAAU,UAC7D8F,cAAc3N,MAAAA,OAAAA,EAAAA,EAAOiH,UAAW,KAIxC,CEjGsB,IAOhB2G,gBACMjH,SAAAA,GAAAA,SAAAA,IAAAA,OAAAA,EAAAA,MAAAA,KAAAA,YAAAA,IAAAA,CAAAA,EAAAA,EAAAA,GAAAA,IAAAA,EAAAA,EAAAA,UADkBN,OAClBM,EAAAA,cAAA,WACR,GAAIkH,MAAMC,OAAOrR,KAAKf,MAAMqS,SAAWF,MAAMC,OAAOrR,KAAKf,MAAMmO,OAC7D,MAAMmE,MAAM,4BAEhB,EAACvK,EAMSmD,SAAA,SAAStF,GACjB,IAAMuI,EAAOpN,KAAKf,MAAMmO,KAIxB,OAAO,IAAWvF,EAAChD,EAAKiD,KAAKtF,MAHf4K,EAAOpN,KAAKf,MAAMqS,OACnBlE,EAAO,GAAKpN,KAAKf,MAAMqS,OAGtC,EAVAjL,EAAA8K,EAAA,CAAA,CAAAjS,IAAA,OAAAyI,IAAA,WACE,OAAoBjB,EAAC8K,KACvB,KAT4B5H,CAAAA,CAClBM,CADkBN,ICExB6H,gBAQJtH,SAAAA,GAAAA,SAAAA,IAAAA,OAAAA,EAAAA,MAAAA,KAAAA,YAAAA,IAAAA,CARkCP,OAQlCO,EAAAA,EAAAA,GAAAA,EAAAA,UAAAA,SAAA,SAASuB,GACP,IAAMC,EAAU,CAAA,EAkBhB,OAhBI3L,KAAKf,MAAM2M,MACbD,EAAa,IAAI3L,KAAKf,MAAM2M,IAC1BF,EAAQE,IACR5L,KAAKf,MAAMmO,KACXpN,KAAKf,MAAMqS,QAIXtR,KAAKf,MAAM4M,OACbF,EAAc,KAAI3L,KAAKf,MAAM4M,KAC3BH,EAAQG,KACR7L,KAAKf,MAAMmO,KACXpN,KAAKf,MAAMqS,QAIfhH,EAAA,GACKoB,EACAC,EAEP,EA3BAtF,EAAAoL,EAAA,CAAA,CAAAvS,IAAA,OAAAyI,IAAA,WACE,OAAoBjB,EAACgL,WACvB,KANkC9H,CAAAA,CAQlCO,CARkCP,ICQpB+H,SAAAA,KACd,IAAY/C,EAAGC,KACf+C,EASIhD,EAAO5B,WARTiD,EAAAA,EAAAA,OACA4B,EAAAA,EAAAA,QAAAA,cAAcC,EAAAC,EAAAH,EACdI,WAAAA,OAAU,IAAAD,GAAOA,EAAAE,EAAAL,EACjBM,WAAAA,OAAU,IAAAD,GAAOA,EAAAE,EAAAP,EACjBQ,aAAAA,OAAe,IAAAD,EAAA,EACfb,EAAAA,EAAAA,EAAAA,MAAAA,OAAQ,IAAAe,EAAA,SACRjF,KAAAA,OAAI,IAAAkF,EAAG,EAACA,EAAAC,EAAAX,EACRY,kBAAAA,OAAoB,IAAAD,GAGtBA,EAAezC,EAAG2C,GAAgD,MAClErD,EAAsCC,GAASjC,GAAxCsF,EAAWtD,EAAA,GAAEuD,EAAcvD,EAAA,GACRC,EAAAA,GAAS,GAA5BuD,EAAOC,EAAAA,GAAAA,EACdC,EAAA,GAAO5S,EAAGyO,KAEVa,GAAU,WAuCR,OAtCIS,GACFH,EAAUzK,QAAU,IAAIoM,GAAsB,CAC5CH,MAAOA,EACPlE,KAAMsF,EACN9G,IAAKqE,EAAOrE,IACZC,KAAMoE,EAAOpE,OAGf+C,EAAOwB,SAASC,SAASP,EAAUzK,WAEnCyK,EAAUzK,QAAU,IAAmB8L,GAAC,CACtCG,MAAOA,EACPlE,KAAMsF,IAGR9D,EAAOwB,SAASC,SAASP,EAAUzK,UAGjCyK,EAAUzK,mBAAwCoM,GACpD7C,EAAOwB,SAAS1H,GAAG,eAAgB,SAACmC,GAAYgI,OAAAA,EAAShI,EAAQlM,OAAO,GAC/DmR,EAAUzK,mBAAkC8L,IAIrDrB,EAAUzK,QAAQqD,GAAG,gBAAiB,SAACmC,GACrCgI,OAAAA,EAAShI,EAAQlM,OAAO,GAI5BiQ,EAAOwB,SAAS1H,GAAG,UAAWqK,GAI9BnE,EAAOwB,SAAS1H,GAAG,QAAS,WAC1BmK,EAAS,GACTF,EAAe,EACjB,GAEO,WACL/D,EAAOwB,SAASE,WAA2BR,EAAUzK,SACrDuJ,EAAOwB,SAASxH,IAAI,UAAWmK,EACjC,CACF,EAAG,IAEH,IAAcA,EAAG,SAACC,GAGZR,GAAqBQ,IAAqBlD,EAAUzK,UACtDsN,EAAe,GAEsB,IAAjC7C,EAAUzK,QAAQpG,MAAMmO,MAC1B0C,EAAUzK,QAAQ0E,SAAS,CACzBqD,KAAM,IAId,EAEWC,EAAG,WAAM3H,OAAAA,KAAKuN,KAAKL,EAAQtB,EAAM,EAE/B4B,EAAG,SAAC9F,GACf,GAAIA,GAAQC,KAAWD,EAAO,GAAKA,IAASsF,EAC1C,OACD,KAEDC,EAAevF,GAEf0C,EAAUzK,QAAQ0E,SAAS,CACzBqD,KAAMA,GAEV,EAmHA,OAEI3O,EAAA,MAAA,CAAA2M,UAAW4F,GACT5F,GAAU,cACVwD,EAAOxD,UAAU4B,aA1BlBvO,EAAA0U,EACEtB,KAAAA,GAAWe,EAAQ,GAClBnU,EACE,MAAA,CAAA2U,KAAK,SACL,YAAU,SACVhI,UAAW4F,GACT5F,GAAU,WACVwD,EAAOxD,UAAUiI,mBAEnBC,MAAOpT,EAAE,sBAAuBwS,EAAc,EAAGrF,MAEhDnN,EAAE,sBAAuB,IAAAzB,EAAA,IAAA,KAAIyB,EAAC,IAAIwS,EAAcpB,EAAQ,KAAU,IAClEpR,EAAE,iBAAkB,IACrBzB,EAAA,IAAA,KAAIyB,EAAKwF,GAAAA,KAAK6N,KAAKb,EAAc,GAAKpB,EAAOsB,KAAe,IAC3D1S,EAAE,iBAAkB,eAAIA,EAAC,GAAI0S,IAAc,IAC3C1S,EAAE,wBAgBTzB,EAAK,MAAA,CAAA2M,UAAWA,GAAU,UACvB8G,GACCzT,EAAA,SAAA,CACE+U,SAAU,EACVJ,KAAK,SACLK,SAA0B,IAAhBf,EACVgB,QAAS,WAAA,OAAaR,EAACR,EAAc,EAAE,EACvCY,MAAOpT,EAAE,uBACT,aAAYA,EAAE,uBACdkL,UAAW4F,GACTpC,EAAOxD,UAAUuI,iBACjB/E,EAAOxD,UAAUwI,uBAGlB1T,EAAE,wBAxIO,WAClB,GAAIkS,GAAgB,EAClB,OACD,KAGD,IAAMyB,EAAmBnO,KAAK6N,IAAIlG,IAAS+E,GAE9B0B,EAAGpO,KAAK6N,IAAIb,EAAahN,KAAKqO,MAAMF,EAAW,IAK5D,OAJInB,EAAchN,KAAKqO,MAAMF,EAAW,IAAMxG,MAC5CyG,EAAYD,GAAYxG,IAAUqF,IAIlCjU,EAAC0U,EAAQ,KACN9F,IAAUwG,GAAYnB,EAAcoB,EAAY,GAC/CrV,EAAC0U,EACC,KAAA1U,EAAA,SAAA,CACE+U,SAAU,EACVJ,KAAK,SACLM,QAAS,WAAA,OAAaR,EAAC,EAAE,EACzBI,MAAOpT,EAAE,wBACT,aAAYA,EAAE,wBACdkL,UAAWwD,EAAOxD,UAAUuI,kBAE3BzT,EAAE,MAELzB,EACE,SAAA,CAAA+U,UAAW,EACXpI,UAAW4F,GACT5F,GAAU,UACVwD,EAAOxD,UAAUuI,mBAGnB,QAKLpS,MAAMyS,KAAKzS,MAAMsS,GAAUrK,QACzB/B,IAAI,SAAC3J,GAAC,OAAgB4U,GAAI5U,EAAIgW,EAAU,GACxCrM,IAAI,SAAC3J,UAEFW,EAAA,SAAA,CAAA+U,SAAU,EACVJ,KAAK,SACLM,QAAS,WAAMR,OAAAA,EAAQpV,EAAE,EACzBsN,UAAW4F,GACT0B,IAAgB5U,EACZkT,GACE5F,GAAU,eACVwD,EAAOxD,UAAU6I,yBAEnB,KACJrF,EAAOxD,UAAUuI,kBAEnBL,MAAOpT,EAAE,kBAAmBpC,EAAI,GAChC,aAAYoC,EAAE,kBAAmBpC,EAAI,IAEpCoC,EAAC,IAAIpC,EAAI,IACH,GAGZuP,IAAUwG,GAAYxG,IAAUqF,EAAcoB,EAAY,GACzDrV,EAAC0U,EACC,KAAA1U,EAAA,SAAA,CACE+U,UAAW,EACXpI,UAAW4F,GACT5F,GAAU,UACVwD,EAAOxD,UAAUuI,mBAGnB,OAEFlV,YACE+U,SAAU,EACVJ,KAAK,SACLM,QAAS,WAAMR,OAAAA,EAAQ7F,IAAU,EAAE,EACnCiG,MAAOpT,EAAE,kBAAmBmN,KAC5B,aAAYnN,EAAE,kBAAmBmN,KACjCjC,UAAWwD,EAAOxD,UAAUuI,kBAE3BzT,EAAC,GAAImN,OAMlB,CAqDO6G,GAEAlC,GACCvT,EACE,SAAA,CAAA+U,SAAU,EACVJ,KAAK,SACLK,SAAUpG,MAAYqF,EAAc,GAAiB,IAAZrF,IACzCqG,QAAS,kBAAaR,EAACR,EAAc,EAAE,EACvCY,MAAOpT,EAAE,mBACT,aAAYA,EAAE,mBACdkL,UAAW4F,GACTpC,EAAOxD,UAAUuI,iBACjB/E,EAAOxD,UAAU+I,uBAGlBjU,EAAE,qBAMf,CClRgBkU,SAAAA,GAAMA,EAAwBC,GAC5C,MAAoB,iBAAJD,EACVA,EAAMjU,QAAQ,MAAQ,EAChBkU,EAAiB,IAAOC,SAASF,EAAO,IAEzCE,SAASF,EAAO,IAK7BA,CAAA,CAEgBG,SAAAA,GAAGH,GACjB,OAAKA,OACUL,MAAMK,GACvB,KAFqB,EAErB,CCRgBI,SAAAA,GAAYvV,GAC1B,IAAiBwV,EAAGxV,EAAMyV,SAASC,WAAU,GAO7C,OALAF,EAAYrS,MAAMwS,SAAW,WAC7BH,EAAYrS,MAAMgS,MAAQ,OAC1BK,EAAYrS,MAAMyS,OAAS,cAC3BJ,EAAYrS,MAAM0S,WAAa,SAI3BrW,EAAA,MAAA,CAAAU,IAAK,SAAC4V,GACJA,GAAeA,EAAYlT,YAAY4S,EACzC,GAGN,UCtByBO,GAACC,GACxB,IAAKA,EAAK,MAAO,GAEjB,IAAMC,EAAQD,EAAI5G,MAAM,KAGxB,OAAqB,IAAjB6G,EAAMvW,QAAgB,iBAAiBuD,KAAK+S,OAK7CxN,IAAI,SAAU0N,EAAM5N,GAEnB,OAAa,GAATA,IACUhF,gBAIF6S,OAAO,GAAGC,cAAgBF,EAAK3S,MAAM,GAAGD,aACtD,GACCgM,KAAK,GACV,KCjBY+G,GCyBZC,GAAe,iBAxBH,WAAA,SAAAC,IAAA,CAAA,IAAAxO,EAAAwO,EAAArS,UAqBT,OArBS6D,EACFyO,OAAA,SAAOvH,EAAiBlP,GAC9B,MAAA,cAAqBA,EAAKqW,cAAa,MAAMnH,CAC/C,EAEAN,EAAAA,MAAA,SAAMM,EAAiBwH,QAAAA,IAAAA,IAAAA,GAAiB,GACtC,MAAY1V,KAAKyV,OAAOvH,EAAS,SAEjC,GAAIwH,EACF,MAAMnE,MAAMoE,GAEZC,QAAQhI,MAAM+H,EAElB,EAEAE,EAAAA,KAAA,SAAK3H,GACH0H,QAAQC,KAAK7V,KAAKyV,OAAOvH,EAAS,QACpC,EAEA4H,EAAAA,KAAA,SAAK5H,GACH0H,QAAQE,KAAK9V,KAAKyV,OAAOvH,EAAS,QACpC,EAACsH,CAAA,CArBS,IDDZO,EAAAT,oBAAA,GAAYA,GAAAA,EAAcA,iBAAdA,EAAcA,eAIzB,KAHCA,GAAA,OAAA,GAAA,SACAA,GAAAA,GAAA,OAAA,GAAA,SACAA,GAAAA,GAAA,KAAA,GAAA,OAUF,IAA0BU,gBAAA,WAGxB,aAFiBC,KAAAA,eAGfjW,KAAKiW,QAAU,EACjB,CAAC,IAAAjP,EAAAgP,EAAA7S,iBAAA6D,EAEDW,IAAA,SAAiCzB,GAC/B,OAAOlG,KAAKiW,QAAQC,KAAK,SAACpW,UAAOA,EAACoG,KAAOA,CAAE,EAC7C,EAACc,EAEDmP,IAAA,SAAsCC,GACpC,OAAKA,EAAOlQ,GAKRlG,KAAK2H,IAAIyO,EAAOlQ,KAClBqP,GAAI3H,MAAK,wBAAyBwI,EAAOlQ,IAClClG,OAGTA,KAAKiW,QAAQ1V,KAAK6V,GAEpBpW,OAXIuV,GAAI3H,MAAM,6BACH5N,KAUX,IAEAqW,OAAA,SAAOnQ,GACL,IAAYkQ,EAAGpW,KAAK2H,IAAIzB,GAMxB,OAJIkQ,GACFpW,KAAKiW,QAAQK,OAAOtW,KAAKiW,QAAQ9V,QAAQiW,GAAS,GAItDpW,IAAA,IAEAuW,KAAA,SAAkC3B,GAChC,IAAIqB,EAQJ,OALEA,EADc,MAAZrB,GAAgC/E,MAAZ+E,EACZ5U,KAAKiW,QAAQnN,OAAO,SAAChJ,GAAMA,OAAAA,EAAE8U,WAAaA,CAAQ,GAElD5U,KAAKiW,QAGVA,EAAQrV,KAAK,SAACtC,EAAGgC,GAAOhC,OAAAA,EAAEkY,OAASlW,EAAEkW,MAAQlY,EAAEkY,MAAQlW,EAAEkW,MAAQ,CAAC,EAC3E,IA9CwB,GAiDpB,SAAwBC,GAACxX,cAOvB2P,EAASC,KAEf,GAAI5P,EAAMyX,SAAU,CAElB,IAAYN,EAAGxH,EAAOwH,OAAOzO,IAAI1I,EAAMyX,UAEvC,OAAKN,EAEE3X,EACL0U,EACA,CAAA,EACA1U,EAAE2X,EAAOO,UACPP,EAAAA,CAAAA,OAAQA,GACLnX,EAAMA,SAPW,IAUzB,CAAM,YAAuB4Q,IAAnB5Q,EAAM2V,SAEPnW,EACN0U,EACA,CAAE,EACFvE,EAAOwH,OAAOG,KAAKtX,EAAM2V,UAAUnN,IAAI,SAAC3H,GACtC,SAASA,EAAE6W,UAASrM,EAAA,CAAI8L,OAAQtW,GAAM+G,EAAK5H,MAAMA,OACnD,IAKN,IAAA,CE5FyD,IAGnD2X,4BAGJ,SAAAA,IAAA,IAAA/P,EAGqB,OAFnBA,EAAOF,EAAA9H,KAAAmB,OAAAA,MAHD6W,cAKN,EAAAhQ,EAAKgQ,SAAW,GAAGhQ,CACrB,QAAC,IAAAG,EAAA4P,EAAAzT,UAYA,OAZA6D,EAsBD8P,YAAA,SACElI,EACA8F,EACAqC,GAEA,MAA2BnI,EAAOoI,UACnBC,EAAGrI,EAAOqI,UAEzB,IAAKD,EAGH,OACDhX,KAGD,IAAMqU,EAAiB2C,EAAUE,YAE7BC,EAAS,GAETzC,EAASrP,SAAW4R,IAMtB7T,EACE3E,EAAE+V,GAAa,CACbE,SAAUA,EAASrP,UAErB0R,EAAQ1R,SAGV8R,EJjDUC,SAAqBL,GAGnC,MAAuCA,EAAQM,cAC7C,SAGF,IAAKC,EACH,MAAO,CACR,EAED,IAAMC,EAAiBD,EAAalM,UACpBoM,EAAGF,EAAalV,MAAMC,QACtCiV,EAAalM,UAAemM,EAAc,IAAInM,GAAU,eAExDkM,EAAalV,MAAMqV,YAAc,OACjCH,EAAalV,MAAMgS,MAAQ,OAC3BkD,EAAalV,MAAMsV,QAAU,IAC7BJ,EAAalV,MAAMuV,OAAS,IAC5BL,EAAalV,MAAMwV,OAAS,OAC5BN,EAAalV,MAAMyV,QAAU,OAE7B,MAAUtW,MAAMyS,KACdsD,EAAa/Y,WAAWuZ,iBAA8B,aACtDxM,OAAO,SAACC,EAAMlG,GACdA,IAAAA,EAEA,OAFAA,EAAQjD,MAAMgS,MAAW/O,EAAQ6R,YAAe,eAG7C7R,EAAQ0S,aAAa,mBAAoB,CACxCC,SAAU3S,EAAQ6R,gBAEjB3L,EAEP,EAAG,CAAA,GAcH,OAZA+L,EAAalM,UAAYmM,EACzBD,EAAalV,MAAMC,QAAUmV,EAC7BF,EAAalV,MAAMqV,YAAc,OAE3BlW,MAAMyS,KACVsD,EAAa/Y,WAAWuZ,iBAA8B,aACtDxM,OAAO,SAACC,EAAMlG,GAGd,OAFAkG,EAAKlG,EAAQ0S,aAAa,mBAA0B,MAAI1S,EAAQ6R,YAEzD3L,CACT,EAAG0M,EAGL,CICeb,CAAqBL,EAAQ1R,UAGxC,IAAA,IAAgE6S,EAAhEC,EAAAC,EAA6BxB,EAAOyB,cAAcrY,KAAK2K,SxBjE3CW,OAAO,SAACC,EAAMlK,GAAC,OAASkK,EAACnB,OAAO/I,EAAE,EAAE,OwBiEgB6W,EAAAC,KAAAG,MAAE,CAAvDC,IAAAA,EAETL,EAAAjV,MAAIsV,EAAO5N,SAAW4N,EAAO5N,QAAQhM,OAAS,KAIzC4Z,EAAOnE,OAAS6C,EAIfsB,EAAOrS,UAETqS,EAAOnE,MAAQG,GAAG4C,EAAOoB,EAAOrS,IAAW,OAC3CqS,EAAOP,SAAWzD,GAAG4C,EAAOoB,EAAOrS,IAAc,WAKnDqS,EAAOnE,MAAQG,GAAGH,GAAMmE,EAAOnE,MAAOC,IAEzC,CAOD,OALIK,EAASrP,SAAW4R,GAEtB7T,EAAO,KAAM2T,EAAQ1R,SAIzBrF,IAAA,EAEQwY,EAAAA,QAAA,SACNC,EACA9N,GAIA,IAFA,MAEqB+N,EAAAA,EAFR/N,GAAW3K,KAAK2K,SAAW,mBAEb,CAAhB4N,IAAAA,EAETI,EAAA1V,MAAIsV,EAAO5N,SAAW4N,EAAO5N,QAAQhM,OAAS,EAC5C4Z,EAAO3X,UAAOiP,OACWA,IAAhB0I,EAAO3X,MAAsB6X,EACtCF,EAAO3X,KAAO,CAAA,EACJ2X,EAAO3X,KAGe,iBAAV2X,EAAC3X,OACvB2X,EAAO3X,KAAI0J,EAAA,CAAA,EACNiO,EAAO3X,OAHZ2X,EAAO3X,UAAOiP,EAOZ0I,EAAO5N,SACT3K,KAAKwY,QAAQC,EAAYF,EAAO5N,QAEnC,CACH,EAAC3D,EAEO4R,aAAA,SAAaC,EAAoBlO,GAGvC,IAFA,IAEyBmO,EAAzBC,EAAAX,EAFazN,GAAW3K,KAAK2K,SAAW,MAEfmO,EAAAC,KAAAT,MAAE,KAAVC,EAAAO,EAAA7V,WACU4M,IAArB0I,EAAOM,YACTN,EAAOM,UAAYA,GAGjBN,EAAO5N,SACT3K,KAAK4Y,aAAaC,EAAWN,EAAO5N,QAEvC,CACH,IAEQqO,MAAA,SAAMrO,GAGZ,IAFA,IAEyBsO,MAFZtO,GAAW3K,KAAK2K,SAAW,MAEfsO,EAAAC,KAAAZ,MAAE,CAAhBC,IAAAA,EACTU,EAAAhW,MAAKsV,EAAOrS,IAA6B,iBAAhBqS,EAAOtT,OAE9BsT,EAAOrS,GAAK8O,GAAUuD,EAAOtT,OAG1BsT,EAAOrS,IACViT,GAAOvL,MAGR,oGAGG2K,EAAO5N,SACT3K,KAAKgZ,MAAMT,EAAO5N,QAErB,CACH,EAAC3D,EAEOoS,gBAAA,SACNC,EACA1O,GAGA,IAAA,IAA4B2O,EAA5BC,EAAAnB,EAAqBzN,KAAO2O,EAAAC,KAAAjB,MAAE,KAAbC,EAAAe,EAAArW,WACO4M,IAAlB0I,EAAOnC,QACTiD,EAAclD,IACZjQ,EAAAA,CAAAA,GAAIqS,EAAOrS,IACRqS,EAAOnC,OAAM,CAChBxB,SAAUU,EAAAA,eAAe1O,OAG9B,CACH,EAACgQ,EAEM4C,YAAP,SACE7O,GAIA,IAFA,IAE8B8O,EAFlBvJ,EAAG,IAAI0G,EAEEjM,EAAAA,EAAAA,KAAS8O,EAAAC,KAAApB,MAAA,CAAA,IAAbC,EAAAkB,EAAAxW,MACf,GAAsB,oBAAY0W,EAAepB,GAC/CrI,EAAOvF,QAAQpK,KAAK,CAClB0E,KAAMsT,SAEH,GAAsB,iBAAXA,EAAqB,CACrC,IAAiBqB,EAAGrB,EAEhBqB,EAAYjP,UACdiP,EAAYjP,QAAUiM,EAAO4C,YAAYI,EAAYjP,SAASA,SAK9B,iBAAZiP,EAACxD,aACIvG,IAArB+J,EAAY/U,OACd+U,EAAY/U,KAAO,MAKvBqL,EAAOvF,QAAQpK,KAAKgY,EACrB,CACF,CAED,OAAOrI,CACT,EAAC0G,EAEMiD,iBAAP,SAAwBjL,GACtB,IAAYsB,EAAG,IAAI0G,EAmBnB,OAhBIhI,EAAOoF,KACT9D,EAAOvF,QAAUiM,EAAOkD,cAAclL,EAAOoF,MAAMrJ,QAC1CiE,EAAOjE,QAChBuF,EAAOvF,QAAUiM,EAAO4C,YAAY5K,EAAOjE,SAASA,SAEpDiE,EAAO/J,MACmB,iBAAnB+J,EAAO/J,KAAK,IACjB+J,EAAO/J,KAAK,aAAmBtD,QAIjC2O,EAAOvF,QAAUpB,OAAOC,KAAKoF,EAAO/J,KAAK,IAAI4C,IAAI,SAACxC,GAChD,MAAO,CAAEA,KAAMA,EACjB,IAGEiL,EAAOvF,QAAQhM,QACjBuR,EAAO8I,QACP9I,EAAOsI,QAAQ5J,EAAOhO,MACtBsP,EAAO0I,aAAahK,EAAOiK,WAC3B3I,EAAOkJ,gBAAgBxK,EAAOwH,OAAQlG,EAAOvF,SACtCuF,GAIX,IAAA,EAEO4J,EAAAA,cAAP,SAAqB5O,GAKnB,IAJA,IAI2B6O,EAJrB7J,EAAS,MAIf8J,EAAA5B,EAHclN,EAAQmM,cAAc,SAClBS,iBAAiB,SAERiC,EAAAC,KAAA1B,MAAE,CAAlB2B,IAAAA,UACT/J,EAAOvF,QAAQpK,KAAK,CAClB0E,KAAMgV,EAAG9U,UACTiP,MAAO6F,EAAG7F,OAEb,CAED,OACFlE,CAAA,EAqBOmI,EAAAA,cAAP,SAAqB1N,GACnB,IAAIuP,EAA6B,GAC3BxB,EAAO/N,GAAW,KACV,GAEd,GAAI+N,GAAQA,EAAK/Z,OAAQ,CACvBub,EAAO3Z,KAAKmY,GAEZ,IAAkBA,IAAMyB,EAANzB,EAAAA,EAAAA,KAAMyB,EAAAC,KAAA9B,MAAA,CAAA,IAAV+B,EAAAF,EAAAlX,MACRoX,EAAI1P,SAAW0P,EAAI1P,QAAQhM,SAC7B2b,EAAUA,EAAQlQ,OAAOiQ,EAAI1P,SAEhC,CAEG2P,EAAQ3b,SACVub,EAASA,EAAO9P,OAAOpK,KAAKqY,cAAciC,IAE7C,CAED,OAAOJ,CACT,EAOOK,EAAAA,YAAP,SAAmB5P,GACjB,MAAiC,GACvB+N,EAAG/N,GAAW,GAExB,GAAI+N,GAAQA,EAAK/Z,OACf,IAAkB+Z,IAAM8B,EAAN9B,EAAAA,EAAAA,KAAM8B,EAAAC,KAAAnC,MAAA,CAAA,IAAV+B,EAAAG,EAAAvX,MACPoX,EAAI1P,SAAkC,IAAvB0P,EAAI1P,QAAQhM,QAC9Bub,EAAO3Z,KAAK8Z,GAGVA,EAAI1P,UACNuP,EAASA,EAAO9P,OAAOpK,KAAKua,YAAYF,EAAI1P,UAE/C,CAGH,QACF,EAACiM,EAMM8D,aAAP,SAAoBnC,GAClB,OAAWvY,KAACqY,cAAc,CAACE,IAAS5Z,OAAS,CAC/C,EA1TA0H,EAAAuQ,EAAA,CAAA,CAAA1X,IAAA,UAAAyI,IAAA,WACE,YAAYkP,QACd,EAACjP,IAED,SAAY+C,GACV3K,KAAK6W,SAAWlM,CAClB,GAAC,CAAAzL,IAAA,iBAAAyI,IAED,WACE,OAAW3H,KAAC6W,SAAS/N,OAAO,SAAC1K,UAAOA,EAAE6M,MAAM,EAC9C,KAAC2L,CAAA,EAnBkB3Q,GCPC0U,GAAA,WAAA,ECFhBC,4BAGJ,SAAY/V,EAAAA,GACV,IAAAgC,EACe,OADfA,EAAAgU,EAAAhc,KAAAmB,aAHM6E,YAINgC,EAAKe,IAAI/C,GACXgC,CAAA,QAAC,2BAEYc,IAAG,WAAA,IACS,OAAAmT,QAAA/J,QAAJ/Q,KAAK6E,sBAAlBA,GAEN,MAAO,CACLA,KAAMA,EACN+N,MAAO/N,EAAKlG,OACZ,EACH,CAAA,MAAAR,GAAA,OAAA2c,QAAAC,OAAA5c,EAAA,CAAA,EAAA6I,EAEMY,IAAA,SAAI/C,GAOT,OANIA,mBACF7E,KAAK6E,KAAO,WAAaA,OAAAA,CAAI,EACpBA,wBACT7E,KAAK6E,KAAOA,GAIhB7E,IAAA,KAzB0B2a,ICgBRK,gBAAA,SAAAH,GAGlB,WAAYnP,SAEa,OADvB7E,EAAOgU,EAAAhc,KAAAmB,OAAAA,MAHQ0L,aAAO,EAItB7E,EAAK6E,QAAUA,EAAQ7E,CACzB,CANkBE,EAAAiU,EAAAH,GAMjB,IAEOI,EAAAA,EAAAA,iBAAAA,EAAAA,QAAA,SAAQC,GACd,MAAmC,mBAApBlb,KAAC0L,QAAQyP,OACfnb,KAAK0L,QAAQyP,OAAOD,GAGzBA,EAASE,KACKC,QAEhB9F,GAAI3H,MACuBsN,yBAAAA,EAASI,OAAM,MAAMJ,EAASK,YACvD,GAEK,KAEX,EAACvU,EAEMW,IAAA,SAAI+D,GAGT,IAAU8P,EAAAlR,EAAA,CAAA,EACLtK,KAAK0L,QACLA,GAOL,MAAyB,qBAAT7G,KACP2W,EAAK3W,KAAK2W,GAGPC,MAACD,EAAK5P,IAAK4P,GACpBE,KAAK1b,KAAKib,QAAQU,KAAK3b,OACvB0b,KAAK,SAACE,GACL,MAAO,CACL/W,KAAM2W,EAAKE,KAAKE,GAChBhJ,MAA6B,qBAAVA,MAAuB4I,EAAK5I,MAAMgJ,QAAO/L,EAEhE,EACJ,IAhDkB,CAAQ8K,ICZVkB,gBAAA,WAAA,SAAAA,IAAA,CAiElB,OAjEkBA,EAMFhC,iBAAP,SAAwBjL,GAC7B,IAAWkN,EAAG,KAoBd,OAlBIlN,EAAO/J,OACTiX,EAAU,IAAiBlB,GAAChM,EAAO/J,OAGjC+J,EAAOoF,OACT8H,EAAU,IAAIlB,GAAc5a,KAAK+b,oBAAoBnN,EAAOoF,OAE5DpF,EAAOoF,KAAK5R,MAAM4Z,QAAU,QAG1BpN,EAAOqB,SACT6L,EAAU,IAAiBd,GAACpM,EAAOqB,SAGhC6L,GACHvG,GAAI3H,MAAM,wCAAwC,GAG7CkO,CACT,EAACD,EASME,oBAAP,SAA2B7Q,GAKzB,IAJA,IAI+BgN,EjC9CZ1R,EiC0CVyV,EAAG,GAIZ9D,EAAAC,EAHclN,EAAQmM,cAAc,SACjBS,iBAAiB,SAELI,EAAAC,KAAAG,MAAA,CAI7B,IAJ6B,IAIHK,EAFXuD,EAAG,GAEC9U,EAAAA,EAJP8Q,EAAAjV,MACqB6U,iBAAiB,SAGxBa,EAAAwD,KAAA7D,MAAA,CAAA,IAAXhR,EAAAqR,EAAA1V,MAGgB,IAA3BqE,EAAKxC,WAAWnG,QAChB2I,EAAKxC,WAAW,GAAGP,WAAa6X,KAAKC,UAErCH,EAAU3b,MjCxDGiG,EiCwDSc,EAAKnC,WjCvDrB,IAAamX,WAAGC,gBAAgB/V,EAAS,aAC1CgW,gBAAgBC,ciCwDrBP,EAAU3b,KAAKkG,EAAKa,EAAKnC,WAE5B,CAED8W,EAAI1b,KAAK2b,EACV,CAED,OACFD,CAAA,EAGFJ,CAAA,CAjEkB,2GC2Gf,YAAAa,EAAAnZ,EAAAN,GAOD,IAAAyZ,EAAAre,EAAU,CACR,GAAA4E,gBAAc,OAAS5E,EAWtB,YADC4E,EAAAjF,EAAA2e,GAAUhB,UAAWe,EAAAnZ,IATf,EAARA,MAA6DN,EAAA5E,GAI7D4E,EAAIA,EAAQlE,EAmBZ,GAAAkE,GAAYA,EAAAyY,mBAERA,QAAWC,KAAA,KAAAe,EAAAnZ,GAAAoZ,GAAAhB,KAAA,KAAAe,EAAA,UAIdA,EAAA3d,EAAAkE,EAED,QAAiBjF,EACf4e,GACDA,EAAAF,EACC,CAEE,CAAA,IA7JQG,gBAAc,WAC9B,aAAS,CA0GJ,OAvECA,EAAA1Z,eAAmD,SAAA2Z,EAAAC,GAEtC,IAAA7C,EAGT,IAA4D2C,EAE9BtZ,EAAIvD,KAAsB3B,KAI/BkF,EAAA,CAEjC,IAAAyZ,EAAyD,EAAAzZ,EAAAuZ,EAAAC,EACvD,GAAAC,EAAQ,CAER,IACEL,GAAAzC,EAAM,EAAA8C,EAAehd,KAAGjB,GAO5B,CANG,MAAAZ,GACFwe,GAAAzC,EAAA,EAAA/b,EAKD,CACE,OACA+b,CAAA,CACF,OAQAla,YAKIA,KAAAhC,EAAA,SAAW6I,GACZ,IAED,MAAaA,EAAK9H,EACV,EAAN8H,EAAAxI,EACDse,GAAAzC,EAAA,EAAA4C,EAAAA,EAAA7Z,GAAAA,GAEG8Z,KACI7C,EAAA,EAAM6C,EAAgB9Z,IAI9B0Z,GAAAzC,EAAa,EAAAjX,GAGb,MAAI9E,GAEJwe,GAAAzC,OAQF,EAIMA,IAEF,CA3GwB,GA8JjB,SAAA+C,GAAAC,GAEL,OAAAA,iBAAwC,EAAlBA,EAAQ7e,sBA/GpC,SAAAwL,GAAA,SAAAsT,EAAYC,GAA6C,IAAAvW,EAKtD,OAJDA,gBAAQ7G,MAZOqd,OAGb,QAEIC,EAAAA,MAA8B,IAA0BC,IAAA1W,EAIxD2W,2BAA6B,EAK/BJ,GACFA,EAAMrZ,QAAQ,SAAC0Z,GAAI,SAAUpN,SAASoN,EAAK,GAC5C5W,CACH,CANAE,EAAAoW,EAAAtT,GAMC,IAAA7C,EAAAmW,EAAAha,UAiIA,OAjIA6D,EAKD0W,WAAA,WACE1d,KAAKsd,MAAQ,IAAyBC,IACtCvd,KAAKwd,2BAA6B,CACpC,EAACxW,EAQDqJ,SAAA,SACEP,EACA6N,GAEA,QAFA,IAAAA,IAAAA,EAAmB,OAEd7N,EACH,YAAY,4BAGd,GAAuB,OAAnBA,EAAU9Q,KACZ,MAAWuS,MAAC,iCAGd,GAAIvR,KAAK4d,uBAAuB9N,EAAU5J,KAAO,EAC/C,MAAWqL,MAAA,gBAAiBzB,EAAU5J,GACvC,uBAQD,OALA4J,EAAUpH,GAAG,eAAgB1I,KAAK6d,sBAAsBlC,KAAK3b,OAE7DA,KAAK8d,uBAAuBhO,EAAW6N,GACvC3d,KAAK+d,gBAAgBjO,GAEdA,CACT,EAAC9I,EAODgX,YAAA,SACElO,EACA6N,QAAAA,IAAAA,IAAAA,EAAmB,MAEnB,IACE,OAAW3d,KAACqQ,SAASP,EAAW6N,GAChC,MAAOzd,IAKX,EAAC8G,EAODsJ,WAAA,SAAiBR,GACf,GAAKA,IAC8C,IAA/C9P,KAAK4d,uBAAuB9N,EAAU5J,IAA1C,CAEA,IAAM+X,EAAWje,KAAKqd,OAAO1V,IAAImI,EAAU9Q,MAEvCif,GAAYA,EAAStf,SACvBqB,KAAKqd,OAAOzV,IACVkI,EAAU9Q,KACVif,EAASnV,OAAO,SAACoV,GAAI,OAASA,GAAIpO,CAAS,IAE7C9P,KAAK+I,KAAK,UAAW+G,GAPvB,CASF,EAAC9I,EAQO8W,uBAAA,SACNhO,EACA6N,GAEA,IAAYM,EAAGje,KAAKqd,OAAO1V,IAAImI,EAAU9Q,MAEzC,IAAKif,EAAU,CACb,IAAME,EAAa,GACnBne,KAAKqd,OAAOzV,IAAIkI,EAAU9Q,KAAMmf,GAChCF,EAAWE,CACZ,CAED,GAAiB,OAAbR,GAAqBA,EAAW,EAClCM,EAAS1d,KAAKuP,QAEd,GAAKmO,EAASN,GAGP,CAEL,MAAcM,EAASzb,MAAM,EAAGmb,EAAW,GAC/BS,EAAGH,EAASzb,MAAMmb,EAAW,GAEzC3d,KAAKqd,OAAOzV,IAAIkI,EAAU9Q,KAAMqf,EAAMjU,OAAO0F,GAAW1F,OAAOgU,GAChE,MAPCH,EAASN,GAAY7N,CAS3B,EAAC9I,EA0BDsX,eAAA,SAAetf,GACb,OAAWgB,KAACod,MAAMtU,OAAO,SAACkB,GAAO,SAAahL,OAASA,CAAI,EAC7D,EAKQuf,EAAAA,wBAAA,WACN,cAAc/U,KAAK9C,GAChBoC,OAAO,SAAC5J,GAAQ,OAACkS,MAAMC,OAAOnS,GAAK,GACnCuI,IAAI,SAACvI,UAAcmS,OAACnS,EAAI,EAC7B,EAQM8K,EAAAA,QAAQnF,SAAAA,GAAQ,IAAA,IAAA2Z,EAAA,SAAAC,GAqCpB,OALAC,EAAKlB,0BAA4BJ,EAAMze,OAGvC+f,EAAK3V,KAAK,eAAgBwC,IAEd,EApCsBmT,EAAA1e,KAA5Bwd,EAA4BkB,EAAKlB,0BAC5BJ,EAAGsB,EAAKtB,MAEX7R,EAAG1G,EAAK8Z,4hBAkCjB,SAAAC,EAAA/S,EAAAgT,GAOO,IAAAnC,EAAA3B,KAAA,SACN,kBAQM,OAAAjd,EAAA8gB,EAAAjgB,QAKN,MAFMkN,EAAA/N,KAE4Boc,EAAAwB,KAAA,CAChC,IAAAuB,GAAK/C,GAKP,YADMA,EAAAwB,KAAAoD,EAAsB/D,IAASA,EAAA4B,GAAAhB,KAAA,KAAAe,EAAA,IAAAG,GAAA,KAHpC3C,EAAAA,EAAAnb,CAMD,CAGM2d,EACNC,GAAID,EAAC,EAAAxC,GAELwC,EAAAxC,EAEH,MAAA/b,GAEDwe,GAAAD,MAAwB,QAAA,EAAAve,0CAtEMif,EAAK,SAAlBtN,GACT,IAAoBiP,EAAGL,EAAKd,uBAAuB9N,EAAU5J,IAEzD6Y,EAAAA,WAAAA,GAAAA,GAAkBvB,EAAyB,OAAA1C,QAAA/J,QAK/BjB,EAAU9F,QAAQuB,IAAKmQ,KAAA,SAAAsD,GACrCN,EAAKpB,MAAM1V,IAAIkI,EAAU5J,GADzBqF,EAA2CyT,EACR,GAGnCzT,EAAOmT,EAAKpB,MAAM3V,IAAImI,EAAU5J,IAT9B6Y,2CAWL,4DAjBaE,CAAA,EAkBP9gB,SAAAA,GAMP,MALAoX,GAAI3H,MAAMzP,GAEVugB,EAAK3V,KAAK,QAASwC,GAIpBpN,CAAA,kDASF,CAAA,MAAAA,GAAA,OAAA2c,QAAAC,OAAA5c,EAAA,CAAA,EAAA6I,EAOO4W,uBAAA,SAAuBsB,GAC7B,YAAY9B,MAAM+B,UAAU,SAACrf,GAAC,SAAOoG,IAAMgZ,CAAW,EACxD,EAOQE,EAAAA,sBAAA,SACNtP,GAEA,IAAMiP,EAAiB/e,KAAK4d,uBAAuB9N,EAAU5J,IAEzDlG,KAAKwd,0BAA4BuB,IACnC/e,KAAKwd,0BAA4BuB,EAErC,EAAC/X,EAEO6W,sBAAA,SAAsB/N,GAC5B9P,KAAKof,sBAAsBtP,GAC3B9P,KAAK+I,KAAK,gBACV/I,KAAK+I,KAAK,UAAW+G,EACvB,IAEQiO,gBAAA,SAAgBjO,GACtB9P,KAAKof,sBAAsBtP,GAC3B9P,KAAK+I,KAAK,iBACV/I,KAAK+I,KAAK,UAAW+G,EACvB,EAlHAzJ,EAAA8W,EAAA,CAAA,CAAAje,IAAA,QAAAyI,IAAA,WAGE,IAFA,IAEiDuQ,EAF7CkF,EAA+C,OAEhCpd,KAAKue,6BAAyBrG,EAAAC,KAAAG,MAAE,CAAxCtZ,MACQgB,KAAKqd,OAAO1V,IAA7BuQ,EAAAjV,OAEIgb,GAAYA,EAAStf,SACvBye,EAAQA,EAAMhT,OAAO6T,GAExB,CAGD,OAAYb,EAACtU,OAAO,SAACzK,GAAMA,OAAAA,CAAC,EAC9B,KAAC8e,CAAA,CAvID,CAbwB7U,GC3BpB+W,gBAQElV,SAAAA,GAAAA,SAAAA,IAAAA,OAAAA,EAAAA,MAAAA,KAAAA,YAAAA,IAAAA,QAAAA,EAAAA,EAAAA,GAAAA,EAAAA,UAAAA,kBAASqR,GAAS,IACL,OAAAV,QAAA/J,QAAJ/Q,KAAKf,MAAM6c,QAAQnU,IAAI6T,IACrC,yDAND,WACE,OAAO9U,EAAc4Y,SACvB,OAEMnV,CARuBP,oBCC7BO,SAAAA,GAAAA,SAAAA,IAAAA,OAAAA,EAAAA,MAAAA,KAAAA,YAAAA,IAAAA,CARsCP,OAQtCO,EAAAA,EAAAA,GAAAA,EAAAA,UAAAA,SAAA,SAASoV,GACP,IAAa1U,EAAGhD,EAAQM,UAAUoX,EAAc1a,MAKhD,OAFAgG,EAAQlM,OAAS4gB,EAAc3M,MAExB/H,CACT,EAXAxE,EAAAmZ,EAAA,CAAA,CAAAtgB,IAAA,OAAAyI,IAAA,WACE,OAAoBjB,EAAC+Y,WACvB,KANsC7V,CAAAA,CAQtCO,CARsCP,ICOlB8V,gBAAA,SAAAC,GAAA,SAAAD,IAAA,OAAAC,EAAA1W,MAAAjJ,KAAAtB,YAAAsB,IAAA,CAMnB,OANmB+G,EAAA2Y,EAAAC,GAAAD,EAAAvc,UAQpBgH,SAAA,WACE,OAAaZ,OAACqW,QAAQ5f,KAAKf,MAAM4gB,sBAC9B/W,OAAO,YAAQ,MAAqB,mBAArBgX,EAAA,EAA+B,GAC9CxU,OACC,SAACyU,EAAM3f,GAAAA,IAAAA,EAAgB2f,OAAAA,EAAAA,GAAAA,IAAM3f,EAAAA,CAAAA,GAAnBrB,EAAAA,IAAaghB,EAAAA,GAAWC,GAAA,EAClC,CAAE,EAER,EAAC3Z,EAAAqZ,EAAA,CAAA,CAAAxgB,IAAA,OAAAyI,IAXD,WACE,OAAoBjB,EAACuZ,SACvB,KAACP,CAAA,CANmB,CAAQ9V,ICSUsW,gBAAA,SAAAP,GAAA,SAAAO,IAAA,OAAAP,EAAA1W,MAAAjJ,KAAAtB,YAAAsB,IAAA,CAAA+G,EAAAmZ,EAAAP,GAAA,IAAA3Y,EAAAkZ,EAAA/c,UAMrC,OANqC6D,EAQ9BmZ,SAAA,SAAStb,GACf,IAAKA,IAASA,EAAKlG,OACjB,MAAO,GAGT,IAAKqB,KAAKf,MAAMiR,SAAWlQ,KAAKf,MAAMiR,OAAOvF,QAC3C,OACD9F,EAED,IAAM8F,EAAUiM,GAAO2D,YAAYva,KAAKf,MAAMiR,OAAOvF,SAGrD,OAAI9F,EAAK,aAAmBtD,QACEkG,IAAI,SAACQ,GAC/B,IAAImY,EAAM,EAEV,SAAe3Y,IAAI,SAAC8Q,EAAQza,GAE1B,YAAoB+R,IAAhB0I,EAAO1T,MACTub,IAE2B,mBAAhB7H,EAAO1T,KACT0T,EAAO1T,KAAKoD,GAENsQ,EAAC1T,MAIXoD,EAAInK,EAAIsiB,EACjB,EACF,GAIqB,iBAARvb,EAAC,IAAqBA,EAAK,aAActD,MAoBjD,GAnBGsD,EAAqB4C,IAAI,SAACQ,GAChC0C,OAAAA,EAAQlD,IAAI,SAAC8Q,EAAQza,GACnB,YAAoB+R,IAAhB0I,EAAO1T,KACkB,qBAATA,KACT0T,EAAO1T,KAAKoD,GAEZsQ,EAAO1T,KAEP0T,EAAOrS,GACN+B,EAACsQ,EAAOrS,KAElBiT,GAAOvL,MAAgE9P,0DAAAA,EACQ,gGAEhF,KACH,EAAE,EAKR,IAEAqM,SAAA,SAASkW,GACP,MAAO,CACLxb,KAAM7E,KAAKmgB,SAASE,EAAgBxb,MACpC+N,MAAOyN,EAAgBzN,MAE3B,EAACvM,EAAA6Z,EAAA,CAAA,CAAAhhB,IAAA,OAAAyI,IAlED,WACE,OAAoBjB,EAAC+Y,WACvB,KAACS,CAAA,CANqC,CAAQtW,ICX1C0W,gBACGzG,WAAAA,SAAAA,KAqBT,OArBSA,EAAAA,iBAAP,SAAwBjL,GACtB,IAAcwB,EAAG,IAAI+M,GAgBrB,OAdIvO,EAAOkN,mBAAmBd,IAC5B5K,EAASC,SACP,IAAIqP,GAAgB,CAClBG,qBAAsBjR,EAAOqB,UAKnCG,EAASC,SAAS,IAAoBgP,GAAC,CAAEvD,QAASlN,EAAOkN,WACzD1L,EAASC,SACP,IAAqC6P,GAAC,CAAEhQ,OAAQtB,EAAOsB,UAEzDE,EAASC,SAAS,IAA+BmP,IAGnDpP,CAAA,EAGFkQ,CAAA,CArBSzG,MCLP,SAAY0G,GAAe,IAAA1Z,EAAA7G,KAAAA,KAJnBuD,WACAkF,EAAAA,KAAAA,UAAiD,QACjD+X,eAAgB,EAMxBlR,KAAAA,SAAW,WAAM,OAAAzI,EAAKtD,KAAK,OAC3Bkd,aAAe,kBAAU5Z,EAAC4B,SAAS,EAEnCuH,KAAAA,SAAW,SAAC0Q,GACV,GAAuB,mBAAZA,EACT,MAAM,UAAU,6BAClB,GAAI7Z,EAAK2Z,cACP,UAAejP,MAAC,qCAElB1K,EAAK2Z,eAAgB,EAErB,IAAMG,EAAY9Z,EAAKtD,MACvB,IACEsD,EAAKtD,MAAQmd,EAAQ7Z,EAAKtD,MAG3B,CAFA,QACCsD,EAAK2Z,eAAgB,CACtB,CAED,QAAqCtI,MAAdrR,EAAK4B,aAASyP,EAAAC,KAAAG,OACnC3P,EADiBuP,EAAAjV,OACR4D,EAAKtD,MAAOod,GAGvB,OAAO9Z,EAAKtD,KACd,OAEAkM,UAAY,SAAC9G,GACX,GAAwB,qBACtB,MAAM,IAAS4I,MAAC,8BAGlB,OADA1K,EAAK4B,oBAAgB5B,EAAK4B,UAAS,CAAEE,IAElC,WAAA,OAAA9B,EAAK4B,UAAY5B,EAAK4B,UAAUK,OAAO,SAAC8X,UAAWA,IAAKjY,CAAQ,EAAC,CACtE,EAnCE3I,KAAKuD,MAAQgd,CACf,ECYwB7T,G7CnBqhR,SAAW/O,EAAEC,GAAG,IAAIC,EAAE,CAAC4B,IAAI7B,EAAE,OAAOK,IAAIoB,G6CmB5iR,K7CnBijRwhB,SAAS,SAASljB,EAAEC,GAAG,OAAOD,EAAEiB,SAAShB,EAAE,EAAEkjB,SAAS,SAASnjB,GAAG,IAAIE,EAAEC,EAAE,OAAOkC,KAAKkE,kBAAkBrG,EAAE,IAAIC,EAAE,CAAA,GAAIF,GAAGoC,KAAKA,KAAKkE,gBAAgB,WAAW,OAAOpG,CAAC,EAAEkC,KAAK8D,sBAAsB,SAASnG,GAAGqC,KAAKf,MAAMgE,QAAQtF,EAAEsF,OAAOpF,EAAEgD,KAAKP,EAAE,EAAEN,KAAKsD,IAAI,SAAS3F,GAAGE,EAAE0C,KAAK5C,GAAG,IAAIC,EAAED,EAAE4H,qBAAqB5H,EAAE4H,qBAAqB,WAAW1H,EAAEyY,OAAOzY,EAAEsC,QAAQxC,GAAG,GAAGC,GAAGA,EAAEiB,KAAKlB,EAAE,CAAC,GAAGA,EAAEiB,QAAQ,GAAG,OAAOf,EAAEijB,SAASzhB,GAAGxB,EAAEgjB,SAAS7d,YAAYnF,CAAC,C6CmBp/RkjB,mBAwE3B,WAAA,SAAAC,IACEzX,OAAO0X,OAAOjhB,KAAMghB,EAAOE,gBAC7B,CAAC,kBAuGA,SAjGDD,OAAA,SAAOE,GACL,OAAO5X,OAAO0X,OAAOjhB,KAAMmhB,EAC7B,EAOAra,EAAAA,OAAA,SAAOqa,GACL,OAAKA,GAELnhB,KAAKihB,OACHD,EAAOI,kBACF9W,EAAA,CAAA,EAAAtK,KACAmhB,KAIAnhB,MATwBA,IAUjC,EAEOkhB,EAAAA,cAAP,WACE,MAAO,CACLjS,MAAO,IAAIoS,GAAM,CACf/F,OAAQ5d,EAAO4jB,KACfpR,YAAQL,EACRhL,KAAM,OAERuR,OAAQ,IAAIJ,GACZtB,S7CjIksB,CAACrP,QAAQ,M6CkI3sB+O,MAAO,OACPmN,OAAQ,OACRC,qBAAsB,IACtBvK,WAAW,EACX7U,MAAO,GACPgJ,UAAW,GAEf,EAAC4V,EAEMI,kBAAP,SAAyBD,GACvB,OAAe,OAAaF,OAAOE,GAuDnC,MApDkC,kBAAVA,EAACvgB,MAAsBugB,EAAcvgB,MAC3DgO,EAAOqS,OAAO,CACZrgB,KAAM,CACJ6gB,aAAa,KAMnB7S,EAAOqS,OAAO,CACZ/Q,OAAQ0G,GAAOiD,iBAAiBjL,KAGlCA,EAAOqS,OAAO,CACZnF,QAASD,GAAahC,iBAAiBjL,KAGzCA,EAAOqS,OAAO,CACZ7Q,SAAUkQ,GAAczG,iBAAiBjL,KAI3CA,EAAOqS,OAAO,CACZlS,WAAY,IAAclB,GAACe,EAAOd,YAIpCc,EAAOwH,OAAS,OAEZxH,EAAOhC,QAETgC,EAAOwH,OAAOD,IAAI,CAChBjQ,GAAI,SACJ0O,SAAUU,iBAAesB,OACzBD,UAAWhH,KAIXf,EAAO5B,YAET4B,EAAOwH,OAAOD,IAAI,CAChBjQ,GAAI,aACJ0O,SAAUU,EAAAA,eAAeoM,OACzB/K,UAAWhF,KAKX/C,EAAOqH,SACTrH,EAAOqH,QAAQlS,QAAQ,SAACjE,GAAM8O,OAAAA,EAAOwH,OAAOD,IAAIrW,EAAE,GAG7C8O,CACT,EAACoS,CAAA,CAzGD,YCjFgBW,GAChB1iB,GAQA,IAsCEsZ,EAtCI3J,EAASC,KAiDf,OAEIpQ,EAAA,KAAA6L,EAAA,CAAA8I,KAAMnU,EAAMmU,KACZwO,QAAS3iB,EAAM2iB,QACf,iBAAgB3iB,EAAMsZ,QAAUtZ,EAAMsZ,OAAOrS,GAC7CkF,UAAW4F,GACT5F,GAAU,MACVnM,EAAMmM,UACNwD,EAAOxD,UAAUyW,IAEnBzf,WACKnD,EAAMmD,MACNwM,EAAOxM,MAAMyf,IAElBnO,QAxCgB,SAClBvV,GAEIc,EAAM6iB,aAEVlT,EAAOmT,aAAahZ,KAClB,YACA5K,EACAc,EAAMqI,KACNrI,EAAMsZ,OACNtZ,EAAMgJ,IAEV,IAGEsQ,EA0B0BtZ,EAAMsZ,QAtBC,mBAAtBA,EAAOvT,WACTuT,EAAOvT,WAAW/F,EAAMqI,KAAKzC,KAAM5F,EAAMgJ,IAAKhJ,EAAMsZ,UAE7CvT,WALI,CAAA,GArChB/F,EAAMsZ,QAA4C,mBAAtBtZ,EAACsZ,OAAOyJ,UAC/B/iB,EAAMsZ,OAAOyJ,UAAU/iB,EAAMqI,KAAKzC,KAAM5F,EAAMgJ,IAAKhJ,EAAMsZ,QAG9DtZ,EAAMsZ,QAAUtZ,EAAMsZ,OAAOnC,OAE7B3X,EAACgY,GAAc,CACbC,SAAUzX,EAAMsZ,OAAOrS,GACvBjH,MAAO,CACLsZ,OAAQtZ,EAAMsZ,OACdjR,KAAMrI,EAAMqI,KACZW,IAAKhJ,EAAMgJ,OAMZhJ,EAAMqI,KAAKzC,KAiDtB,CC7EgBod,SAAAA,GAAGhjB,GAKjB,IAAY2P,EAAGC,KACTqB,EAAShB,GAAY,SAAC3L,GAAK,OAAUA,EAAC2M,MAAM,GAmClD,OAEIzR,EAAA,KAAA,CAAA2M,UAAW4F,GAAU5F,GAAU,MAAOwD,EAAOxD,UAAU8W,IACvDxO,QAxBgB,SAClBvV,GAEIc,EAAMkjB,YACVvT,EAAOmT,aAAahZ,KAAK,WAAY5K,EAAGc,EAAMgJ,IAChD,GAGMhJ,EAAML,SACIK,EAACL,SAGHK,EAACgJ,IAAIb,MAAMK,IAAI,SAACH,EAAYxJ,GACtC,IAAYya,EAzBE,SAACvN,GACjB,GAAIkF,EAAQ,CACV,IAAUwI,EAAG9B,GAAO2D,YAAYrK,EAAOvF,SAEvC,GAAI+N,EACF,OAAOA,EAAK1N,EAEf,CAED,OACF,IAAA,CAemBoX,CAAUtkB,GAEzB,OAAIya,GAAUA,EAAOtN,OAAmB,KAEjCxM,EAACkjB,GAAG,CAAAziB,IAAKoI,EAAKpB,GAAIoB,KAAMA,EAAMW,IAAKhJ,EAAMgJ,IAAKsQ,OAAQA,GAC/D,GAWJ,CCtDgB8J,SAAAA,GAAWpjB,GAKzB,OACER,EAACwjB,GAAG,CAAAE,YAAY,GACd1jB,EAACkjB,GAAE,CACDvO,KAAK,QACLwO,QAAS3iB,EAAM2iB,QACfE,aAAa,EACbxa,KAAM,MAASrI,EAAMiP,SACrB9C,UAAW4F,GACT5F,GAAU,WACVnM,EAAMmM,UAAYnM,EAAMmM,UAAY,QAK9C,CCfgBkX,SAAAA,KACd,IAAY1T,EAAGC,KACThK,EAAOqK,GAAY,SAAC3L,GAAK,SAAWsB,IAAI,GAClCyW,EAAGpM,GAAY,SAAC3L,GAAK,OAAUA,EAAC+X,MAAM,KACnCpM,GAAY,SAAC3L,GAAK,OAAUA,EAAC2M,MAAM,GAC5ChQ,EAAIyO,KAEQ4T,EAAG,WACnB,OAAIrS,EACKA,EAAOsS,eAAe7jB,QAGjC,EAEA,OACEF,EAAA,QAAA,CAAO2M,UAAW4F,GAAU5F,GAAU,SAAUwD,EAAOxD,UAAUqX,QAC9D5d,GACCA,EAAKiD,KAAKL,IAAI,SAACQ,GACb,OAAQxJ,EAAAwjB,GAAG,CAAA/iB,IAAK+I,EAAI/B,GAAI+B,IAAKA,GAC/B,GAEDqT,IAAW5d,EAAOglB,WAAa7d,GAAwB,IAAhBA,EAAKlG,SAC3CF,EAAC4jB,GAAU,CACTnU,QAAShO,EAAE,WACX0hB,QAASW,IACTnX,UAAW4F,GAAU5F,GAAU,WAAYwD,EAAOxD,UAAUsC,WAI/D4N,IAAW5d,EAAOilB,UAAY9d,GAAwB,IAAhBA,EAAKlG,QAC1CF,EAAC4jB,GACC,CAAAnU,QAAShO,EAAE,kBACX0hB,QAASW,IACTnX,UAAW4F,GACT5F,GAAU,YACVwD,EAAOxD,UAAUwX,YAKtBtH,IAAW5d,EAAO6T,OACjB9S,EAAC4jB,GACC,CAAAnU,QAAShO,EAAE,SACX0hB,QAASW,IACTnX,UAAW4F,GAAU5F,GAAU,SAAUwD,EAAOxD,UAAUwC,SAKpE,CCnDiC,IAWhBiV,gBAAA,SAAAlD,GAAA,SAAAkD,IAAA,OAAAlD,EAAA1W,MAAAjJ,KAAAtB,YAAAsB,IAAA,CAAA+G,EAAA8b,EAAAlD,GAAA,IAAA3Y,EAAA6b,EAAA1f,UAed,OAfc6D,EACLkD,cAAA,WACR,IAAwB,MAAAiO,EAAAC,EAAApY,KAAKf,MAAM0L,wBAAS,CAAjCmY,IAAAA,EACT5K,EAAAjV,WAA4B4M,IAAxBiT,EAAUC,YACZD,EAAUC,UAAY,GAGI,IAAxBD,EAAUC,YAA4C,IAAzBD,EAAUC,WACzCxN,GAAI3H,MAAgCkV,0BAAAA,EAAUC,UAEjD,CACH,EAMQC,EAAAA,QAAA,SAAQC,EAAcC,GAC5B,OAAID,EAAQC,EACH,EACED,EAAQC,GACT,EAGH,CACT,EAAClc,EAEOmc,eAAA,SAAe7kB,EAAQgC,GAG7B,IAFA,MAAI8iB,EAAM,EAEWjH,EAAA/D,EAAApY,KAAKf,MAAM0L,wBAAS,CAA9B4N,IAAAA,EACTI,EAAA1V,MAAA,GAAY,IAARmgB,EAUF,MATA,IAAMH,EAAQ3kB,EAAE8I,MAAMmR,EAAOhR,OAAO1C,KAC9Bqe,EAAQ5iB,EAAE8G,MAAMmR,EAAOhR,OAAO1C,KAGlCue,GAD4B,mBAAnB7K,EAAOyK,QACTzK,EAAOyK,QAAQC,EAAOC,GAAS3K,EAAOwK,UAEtC/iB,KAAKgjB,QAAQC,EAAOC,GAAS3K,EAAOwK,SAKhD,CAED,OAAOK,CACT,EAACpc,EAESmD,SAAA,SAAStF,GACjB,IAAgBwe,EAAA,GAAAjZ,OAAOvF,EAAKiD,MAC5Bub,EAAWziB,KAAKZ,KAAKmjB,eAAexH,KAAK3b,OAEzC,IAAYsjB,EAAG,IAAIzb,EAAQwb,GAK3B,OAFAC,EAAO3kB,OAASkG,EAAKlG,OAGvB2kB,CAAA,EA7CAjd,EAAAwc,EAAA,CAAA,CAAA3jB,IAAA,OAAAyI,IAAA,WACE,OAAOjB,EAAc6c,IACvB,KAACV,CAAA,CAfc,CAAQjZ,OChBvB,SACErC,EACAwb,EACAS,EACAR,UAEDzf,SAAAA,GACC,IAAAkgB,WAAcA,EAAAlgB,EAAM3C,OAAN6iB,EAAY9Y,QACtBpH,EAAM3C,KAAK+J,QAAQlD,IAAI,SAACpG,GACtB,OAAYA,EAAAA,CAAAA,EAAAA,EACd,GACA,KACUsJ,EAAQhM,OACV4Z,EAAG5N,EAAQuL,KAAK,SAAC7U,UAAOA,EAACkG,QAAUA,CAAK,MAG1C,EACNmc,GAAQ,EACFrN,GAAG,EACTvP,GAAS,EA8Cb,QAnD0B+I,IAAX0I,EAwBRiL,GAYuB,IAAtBjL,EAAOwK,UAGT1M,GAAS,EAETvP,GAAS,EAfG,IAAV6c,EACF7c,GAAS,EACA6c,EAAQ,IAGjBD,GAAQ,EACRvN,GAAM,GAvBI,IAAVwN,EAEFxN,GAAM,EACGwN,EAAQ,IAAMH,GAGvBrN,GAAM,EACNuN,GAAQ,GACCC,EAAQ,GAAKH,IAGtBrN,GAAM,GA0BNuN,IAEF/Y,EAAU,IAGRwL,EACFxL,EAAQpK,KAAK,CACXgH,MAAOA,EACPwb,UAAWA,EACXC,QAASA,YAEFlc,EAAQ,CACjB,IAAMS,EAAQoD,EAAQxK,QAAQoY,GAC9B5N,EAAQpD,GAAOwb,UAAYA,CAC5B,MAAM,GAAI1M,EAAQ,CACjB,IAAWuN,EAAGjZ,EAAQxK,QAAQoY,GAC9B5N,EAAQ2L,OAAO/O,EAAO,EACvB,CAED,YACKhE,EAAK,CACR3C,KAAM,CACJ+J,QAASA,IAGf,CAAC,EAEUkZ,GACX,SAACtc,EAAeic,EAAgBR,UAAiCzf,SAAAA,GAC/D,IACMgV,GADUhV,EAAM3C,KAAW2C,GAAAA,OAAAA,EAAM3C,KAAK+J,SAAW,IAChCuL,KAAK,SAAC7U,GAAMA,OAAAA,EAAEkG,QAAUA,CAAK,GAEpD,OAOA+C,EAAA,CAAA,EACK/G,EARAgV,EASAuL,GACDvc,EACqB,IAArBgR,EAAOwK,WAAmB,EAAI,EAC9BS,EACAR,EAJCc,CAKDvgB,GAXGugB,GAAWvc,EAAO,EAAGic,EAAOR,EAA5Bc,CAAqCvgB,GAa9C,CAAC,kBCzFD4G,SAAAA,GAAAA,SAAAA,IAAAA,OAAAA,EAAAA,MAAAA,KAAAA,YAAAA,IAAAA,CARuBP,OAQvBO,EAAAA,EAAAA,GAAAA,EAAAA,UAAAA,SAAA,SAASuB,GACP,MAAgB,CAAE,EAUlB,OARI1L,KAAKf,MAAM2M,MACbD,EAAa,IAAI3L,KAAKf,MAAM2M,IAAIF,EAAQE,IAAK5L,KAAKf,MAAM0L,UAGtD3K,KAAKf,MAAM4M,OACbF,EAAc,KAAI3L,KAAKf,MAAM4M,KAAKH,EAAQG,KAAM7L,KAAKf,MAAM0L,eAIxDe,EACAC,EAEP,EAACtF,EAAA0d,EAAA,CAAA,CAAA7kB,IAAA,OAAAyI,IAnBD,WACE,SAAqBoc,UACvB,KANuBna,CAAAA,CAQvBO,CARuBP,ICqBT2Z,SAAAA,GACdtkB,GAKA,IAAY2P,EAAGC,KACPmB,EAAab,KAAba,SACF9P,EAAIyO,KACwBU,EAAAA,GAAS,GAApC0T,EAAS3T,EAAA,GAAE4U,EAAY5U,EAAA,GACdqJ,EAAG7J,EAAOhO,KACpB2C,EAAQ2L,GAAY,SAAC3L,GAAK,OAAUA,EAAC3C,IAAI,GACzCqjB,EAC0B,iBAAb,MAAVxL,OAAU,EAAVA,EAAYxI,QACfvJ,EAAcqd,WACdrd,EAAc6c,KAEEW,EAAG,WACvB,IAAMC,EAAavV,EAAOwB,SAASkO,eAAe2F,GAClD,GAAIE,EAAWxlB,OACb,OAAOwlB,EAAW,EAGtB,EAqFA,OA7DA3U,GAAU,WACR,IAAeM,EATeoU,MAb1BD,IAAkBvd,EAAcqd,WACvBA,IAAAA,GACTpZ,EAAAA,CAAAA,QAASpH,EAAQA,EAAMoH,QAAU,IAC9B8N,EAAWxI,SAIP4S,IAAAA,GAAW,CACpBlY,QAASpH,EAAQA,EAAMoH,QAAU,MAiBnC,OAFAiE,EAAOwB,SAAS4N,YAAYlO,GAEflB,WAAAA,OAAAA,EAAOwB,SAASE,WAAWR,EAAU,CACpD,EAAG,CAAClB,IAKJY,GAAU,WACR,GAAKjM,EAAL,CAEA,MAAM6gB,EAAgB7gB,EAAMoH,QAAQuL,KAAK,SAAC7U,GAAMA,OAAAA,EAAEkG,QAAUtI,EAAMsI,KAAK,GAElE6c,GAKe,IAAdrB,IACFqB,EAAcrB,UAA2B,OAAlBsB,EAAGplB,EAAM8jB,WAASsB,EAAI,GAE/CL,EAAaI,EAAcrB,YAP3BiB,EAAa,EALH,CAcd,EAAG,CAACzgB,IAEJiM,GAAU,WACR,IAAeM,EAAGoU,IAEbpU,GACAvM,GAELuM,EAAU/F,SAAS,CACjBY,QAASpH,EAAMoH,SAEnB,EAAG,CAACpH,IA4BA9E,EAAA,SAAA,CAAA+U,UAAW,EACX,aAAYtT,eAA4B,IAAd6iB,EAAkB,OAAS,QACrDzP,MAAOpT,EAAc6iB,aAAc,IAAdA,EAAkB,OAAS,QAChD3X,UAAW4F,GACT5F,GAAU,QACVA,GAAU,OAlBS,SAAC2X,GACxB,OAAkB,IAAdA,EACK,OACiB,IAAfA,EACF,OAGF,SACT,CAUwBuB,CAAiBvB,IACnCnU,EAAOxD,UAAUxK,MAEnB8S,QAlCoB,SAACvV,GACvBA,EAAEomB,iBACFpmB,EAAEqmB,kBAEFxU,EACEG,GACElR,EAAMsI,OACS,IAAfpJ,EAAEsmB,UAAqBhM,EAAWgJ,YAClCxiB,EAAM+jB,SAGZ,GA0BF,CCvJO,IAAc0B,GAAG,SAACC,EAAuBC,GAC9C,IAAIC,WAD0CD,IAAAA,EAAO,KAErD,IAAIE,EAAWC,KAAKC,MAEPC,EAAG,WACdH,EAAWC,KAAKC,MAChBL,EACF1b,WAAA,EAAA,GAAAzG,MAAA3D,KAAAH,WAAA,EAEA,OAAmB,WAAA,IAAJuL,EAAA,GAAAzH,MAAA3D,KAAAH,WACPwmB,EAAcH,KAAKC,MACZG,EAAGD,EAAcJ,EAE1BK,GAAWP,EAEbK,EAAOhc,WAAA,EAAIgB,IAGP4a,GACFtY,aAAasY,GAGfA,EAAYlkB,WAAW,WACrBskB,EAAWhb,WAAAA,EAAAA,GACX4a,EAAY,IACd,EAAGD,EAAOO,GAEd,CACF,EC7BgBC,SAAAA,GAAOnmB,GAIrB,MAEcomB,EAAG,SAAClnB,GAChB,OAAIA,aAAamnB,WACJ5f,KAACqO,MAAM5V,EAAEonB,YAERxR,MAAM5V,EAAEqnB,eAAe,GAAGD,MAE1C,EAEME,EAAQ,SAACtnB,GACbA,EAAEqmB,kBAEF,IAEiBkB,EAAGpR,SAFFrV,EAAM0mB,MAAMtgB,QAESjD,MAAMgS,MAAO,IAAMiR,EAASlnB,GAEnEynB,EAASlB,GAAS,SAACvmB,UAAU0nB,EAAC1nB,EAAGunB,EAAY,EAAE,IAE/ClhB,SAAS/B,iBAAiB,UAAWqjB,GACrCthB,SAAS/B,iBAAiB,WAAYqjB,GACtCthB,SAAS/B,iBAAiB,YAAamjB,GACvCphB,SAAS/B,iBAAiB,YAAamjB,EACzC,IAEa,SAACznB,EAA4BunB,GACxCvnB,EAAEqmB,kBAEF,IAAeuB,EAAG9mB,EAAM0mB,MAAMtgB,QAE1BqgB,EAAcL,EAASlnB,IAAMmW,SAASyR,EAAU3jB,MAAM4V,SAAU,MAClE+N,EAAU3jB,MAAMgS,MAAWsR,EAAcL,EAASlnB,GACnD,KACH,EAES2nB,EAAG,SAANA,EAAO3nB,GACXA,EAAEqmB,kBAEFhgB,SAAS5B,oBAAoB,UAAWkjB,GACxCthB,SAAS5B,oBAAoB,YAAagjB,GAC1CphB,SAAS5B,oBAAoB,YAAagjB,GAC1CphB,SAAS5B,oBAAoB,WAAYkjB,EAC3C,EAEA,OACErnB,EAAA,MAAA,CACE2M,UAAW4F,GAAU5F,GAAU,MAAOA,GAAU,cAChD4a,YAAaP,EACbQ,aAAcR,EACd/R,QAAS,SAACvV,UAAOA,EAACqmB,iBAAiB,GAGzC,CCjDM,SAAY0B,GAChBjnB,GAMA,IAAM2P,EAASC,KACT8W,EAAQlT,GAAO,MACrBrD,EAA0BC,GAAS,CAAE,GAA9BjN,EAAO+jB,EAAAA,GAAAA,EACd/W,EAAA,GAAQY,EAAab,KAAba,SAERR,GAAU,WAER,GAAIZ,EAAOwX,aAAeT,EAAMtgB,QAAS,CACvC,IAAMghB,EAAYV,EAAMtgB,QAAQghB,UAEP,iBAALA,GAClBF,EAAS,CACPG,IAAKD,GAGV,CACH,EAAG,CAACV,IAEJ,IAiDQpN,EAjDQgO,EAAG,WAAetnB,OAAqB4Q,MAArB5Q,EAAMsZ,OAAO3X,IAAiB,IAEhD,SACdzC,GAIAA,EAAEqmB,kBAEE+B,KAGFvW,EACEwW,GACEvnB,EAAMsI,OACS,IAAfpJ,EAAEsmB,UALa7V,EAAOhO,KAKY6gB,YAClCxiB,EAAMsZ,OAAO3X,KAAKoiB,SAI1B,EAwCA,OACEvkB,EAAA,KAAA6L,EAAA,CACEnL,IAAKwmB,EACL,iBAAgB1mB,EAAMsZ,QAAUtZ,EAAMsZ,OAAOrS,GAC7CkF,UAAW4F,GACT5F,GAAU,MACVmb,IAAenb,GAAU,KAAM,QAAU,KACzCwD,EAAOwX,YAAchb,GAAU,KAAM,SAAW,KAChDwD,EAAOxD,UAAU6O,IAEnBvG,QAASA,EACTtR,MACKwM,EAAAA,CAAAA,EAAAA,EAAOxM,MAAM6X,GACb,CACDjC,SAAU/Y,EAAMsZ,OAAOP,SACvB5D,MAAOnV,EAAMsZ,OAAOnE,OAEnBhS,EACAnD,EAAMmD,OAEXqkB,UA1DY,SAACtoB,GAEXooB,KAA4B,KAAZpoB,EAAEuoB,OACpBhT,EAAQvV,EAEZ,EAsDIwoB,QAAS1nB,EAAM0nB,QAAU,EAAI1nB,EAAM0nB,aAAU9W,EAC7C+R,QAAS3iB,EAAM2iB,QAAU,EAAI3iB,EAAM2iB,aAAU/R,IAjCzC0I,EAAStZ,EAAMsZ,QAIY,qBAAfvT,WACTuT,EAAOvT,WAAW,KAAM,KAAM/F,EAAMsZ,QAEpCA,EAAOvT,WALI,CAAA,EAiCbuhB,IAAe,CAAE/S,SAAU,GAAM,CAAA,GAEtC/U,EAAK,MAAA,CAAA2M,UAAWA,GAAU,KAAM,iBAxDRyE,IAAtB5Q,EAAMsZ,OAAOtT,KACHhG,EAACsZ,OAAOtT,UAGM4K,IAAxB5Q,EAAMsZ,OAAOnC,OAEb3X,EAACgY,GAAc,CACbC,SAAUzX,EAAMsZ,OAAOnC,OAAOlQ,GAC9BjH,MAAO,CACLsZ,OAAQtZ,EAAMsZ,gBAgDnBgO,KAAgB9nB,EAAC8kB,GAAIjZ,EAAA,CAAC/C,MAAOtI,EAAMsI,OAAWtI,EAAMsZ,OAAO3X,OAtF7B3B,EAAMsZ,OAAOM,WAwF1C5Z,EAAMsI,MAAQqH,EAAOsB,OAAOsS,eAAe7jB,OAAS,GAClDF,EAAC2mB,GAAM,CAAC7M,OAAQtZ,EAAMsZ,OAAQoN,MAAOA,IAI/C,UCzHqBiB,KACnB,IA8CY9e,EA9CA8G,EAAGC,KACHqB,EAAGhB,GAAY,SAAC3L,GAAUA,OAAAA,EAAM2M,MAAM,GAkDlD,OAAIA,EAGEzR,EAAA,QAAA,CAAAS,IAAKgR,EAAOhK,GACZkF,UAAW4F,GAAU5F,GAAU,SAAUwD,EAAOxD,UAAUyb,SATpD/e,EAAG8O,GAAOyB,cAAcnI,EAAOvF,UAE7BlD,IAAI,SAACQ,EAAK8C,GAAa+b,OAvBnB,SAAC7e,EAAgB8C,EAAkBgc,GAEnD,IAAiBxM,EAAG3D,GAAO2D,YAAYrK,EAAOvF,SAE9C,OACGlM,EAAAwjB,GACEha,KAAAA,EAAIR,IAAI,SAAC4S,GACR,OAAIA,EAAIpP,OAAmB,KA7Bd,SACnBsN,EACAxN,EACAic,EACAD,GAEA,IAA6BE,ECjBjBA,SACd1O,EACAxN,EACAgc,GAEA,IAAMG,EAAQtQ,GAAO8D,aAAanC,GACf4O,EAAGJ,EAAYhc,EAIlC,MAAO,CACL4b,QAJcjhB,KAAKqO,MAAMoT,EAAgBD,EAAQA,EAAQC,GAKzDvF,QAJerJ,EAAO5N,SAAW4N,EAAO5N,QAAQhM,QAAW,EAM/D,CDGiCsoB,CAC3B1O,EACAxN,EACAgc,GAGF,OACGtoB,EAAAynB,GACC,CAAA3N,OAAQA,EACRhR,MAAOyf,EACPpF,QAVaA,EAAAA,QAWb+E,QAXWS,EAAPT,SAcV,CAWeU,CACLhN,EACAtP,EACAwP,EAAYpa,QAAQka,GACpB0M,EAEJ,GAGN,CAKqCD,CAAU7e,EAAK8C,EAAUjD,EAAKnJ,OAAO,IAe5E,IAAA,KElCsB2oB,GAAG,SAACpX,GAAc,gBAAM3M,GAC5C,OAAA+G,EAAA,CAAA,EACK/G,EACH2M,CAAAA,OAAQA,GAEZ,CAAC,WCpCoBqX,KACnB,MAAe1Y,KACT6F,EAAWjC,GAAO,MAChBzC,EAAab,KAAba,SAMR,OAJAR,GAAU,WACJkF,GAAU1E,EDgCS,SAAC0E,UAAcnR,SAAAA,GACxC,YACKA,EAAK,CACRmR,SAAUA,GAEd,CAAC,CCrC0BvE,CAAoBuE,GAC7C,EAAG,CAACA,IAGFjW,EAAA,QAAA,CACEU,IAAKuV,EACLtB,KAAK,OACLhI,UAAW4F,GAAU5F,GAAU,SAAUwD,EAAOxD,UAAUoc,OAC1DplB,MAAKkI,EAAA,CAAA,EACAsE,EAAOxM,MAAMolB,MACb,CACDjG,OAAQ3S,EAAO2S,UAInB9iB,EAACmoB,GACD,MAAAnoB,EAAC6jB,SAGP,CC5BgBmF,SAAAA,KACd,IAAgCpY,EAAAA,IAAS,GAAlCqY,EAAQtY,EAAA,GAAEuY,EAAWvY,EAAA,GACbwY,EAAGnV,GAAO,QACV5D,KAQf,OANAW,GAAU,WACkC,IAAtCoY,EAAUviB,QAAQzG,SAASD,QAC7BgpB,GAAY,EAEhB,EAAG,CAACC,IAEAF,EAGEjpB,EAAA,MAAA,CAAAU,IAAKyoB,EACLxc,UAAW4F,GAAU5F,GAAU,QAASwD,EAAOxD,UAAU8E,QACzD9N,MAAKkI,EAAA,CAAA,EAAOsE,EAAOxM,MAAM8N,SAEzBzR,EAACgY,IAAe7B,SAAUU,EAAAA,eAAesB,UAMjD,IAAA,CCxBgBiR,SAAAA,KACd,IAAMC,EAAYrV,GAAO,MACOpD,EAAAA,IAAS,GAAlCqY,EAAUC,EAAAA,GAAAA,EACjBvY,EAAA,KAAeP,KAQf,OANAW,GAAU,WACkC,IAAtCsY,EAAUziB,QAAQzG,SAASD,QAC7BgpB,GAAY,EAEhB,EAAG,CAACG,IAEAJ,EAGEjpB,EAAA,MAAA,CAAAU,IAAK2oB,EACL1c,UAAW4F,GAAU5F,GAAU,UAAWwD,EAAOxD,UAAU2c,QAC3D3lB,MAAKkI,EAAA,CAAA,EAAOsE,EAAOxM,MAAM2lB,SAEzBtpB,EAACgY,IAAe7B,SAAUU,EAAAA,eAAeoM,UAMjD,IAAA,CChBgBsG,SAAAA,KACd,IAAYpZ,EAAGC,KACPmB,EAAab,KAAba,SACFsL,EAASpM,GAAY,SAAC3L,GAAK,OAAUA,EAAC+X,MAAM,KACrCpM,GAAY,SAAC3L,GAAK,OAAUA,EAACsB,IAAI,GAChC6P,EAAGxF,GAAY,SAAC3L,GAAK,OAAUA,EAACmR,QAAQ,GACzCqC,E/DpByrB,CAAC1R,QAAQ,M+DsBzsB4iB,EAAkBvD,GAAoB,WAAA,IAC1C1U,EJRgC,SAACzM,GACnC,OACKA,EAAAA,CAAAA,EAAAA,GACH+X,OAAQ5d,EAAOglB,SAEnB,GIGuC,8BAGd9T,QAAAA,QAAAA,EAAOwB,SAASpG,WAAS0R,KAAA,SAAtC7W,GACNmL,EJLiB,SAACnL,GAAkB,OAAA,SAACtB,GACzC,OAAKsB,EAELyF,EAAA,GACK/G,EAAK,CACRsB,KAAMA,EACNyW,OAAQ5d,EAAOwqB,UAEnB,CAAC,CIHc/X,CAAgBtL,IAGzBlE,WAAW,WACTqP,EJ3BkCzM,SAAAA,GACxC,OAAIA,EAAM+X,SAAW5d,EAAOwqB,OAC1B5d,EAAA,GACK/G,EACH+X,CAAAA,OAAQ5d,EAAOilB,WAIZpf,CACT,EImBM,EAAG,EAAG,+DACP,SAAQpF,GACPoX,GAAI3H,MAAMzP,GACV6R,EJH+BzM,SAAAA,GACnC,OAAA+G,EAAA,CAAA,EACK/G,EAAK,CACRsB,KAAM,KACNyW,OAAQ5d,EAAO6T,OAEnB,EIFK,GAAA,OAAAuJ,QAAA/J,QAAAoX,GAAAA,EAAAzM,KAAAyM,EAAAzM,KAAA,WAAA,QAAA,EACF,CAAA,MAAAvd,GAAA,OAAA2c,QAAAC,OAAA5c,EAAA,CAAA,EAAEyQ,EAAO4S,sBAwBV,OAtBAhS,GAAU,WASR,OALAQ,EAASG,GAAkBvB,EAAOsB,SAElC+X,IACArZ,EAAOwB,SAAS1H,GAAG,UAAWuf,GAEvB,WAAA,OAAYrZ,EAACwB,SAASxH,IAAI,UAAWqf,EAAgB,CAC9D,EAAG,IAEHzY,GAAU,WACJZ,EAAOsB,QAAUoL,IAAW5d,EAAOwqB,QAAUrjB,MAAAA,GAAAA,EAAMlG,QAGrDqR,EACEG,GAAkBvB,EAAOsB,OAAO4G,YAAYlI,EAAQ8F,EAAUqC,IAGpE,EAAG,CAAClS,EAAM+J,EAAQmI,IAIdtY,EAAA,MAAA,CAAA2U,KAAK,gBACLhI,UAAW4F,GACT,SACA5F,GAAU,aACVkQ,IAAW5d,EAAOglB,QAAUtX,GAAU,WAAa,KACnDwD,EAAOxD,UAAU4L,WAEnB5U,MAAKkI,EAAA,CAAA,EACAsE,EAAOxM,MAAM4U,UACb,CACD5C,MAAOxF,EAAOwF,SAIjBkH,IAAW5d,EAAOglB,SACjBjkB,EAAA,MAAA,CAAK2M,UAAWA,GAAU,iBAG5B3M,EAACgpB,GAED,MAAAhpB,SAAK2M,UAAWA,GAAU,WAAYhJ,MAAO,CAAEmf,OAAQ3S,EAAO2S,SAC5D9iB,EAAC8oB,GACH,OAEA9oB,EAACopB,GAAe,MAEhBppB,EAAA,MAAA,CAAKU,IAAK4X,EAAS7Q,GAAG,cAAckF,UAAWA,GAAU,UAG/D,CCnFMgd,IAAKA,gBAAA,SAAAve,GAIT,SAAY+E,EAAAA,GAAwB,IAAA/H,EAKD,OAJjCA,EAAOgD,EAAAhL,KAAAmB,OAAAA,MAJF4O,YACAwH,EAAAA,EAAAA,cAILvP,EAAK+H,QAAS,IAAUoS,IACrBC,OAAO,CAAEoH,SAAQC,EAAAzhB,GAAQkb,aAAkBuG,EAAAzhB,KAC3CC,OAAO8H,GACV/H,EAAKuP,OAASvP,EAAK+H,OAAOwH,OAC5BvP,CAAA,CAVSE,EAAAqhB,EAAAve,GAUR,kBAmEA,SAjEM0e,aAAA,SAAa3Z,GAElB,OADA5O,KAAK4O,OAAO9H,OAAO8H,OAErB,EAAC5H,EAEDrC,cAAA,WACE,OAAOlG,EAAEiO,GAAcoU,SAAU,CAC/B7d,MAAOjD,KAAK4O,OACZhQ,SAAUH,EAAEupB,GAAW,CAAA,IAE3B,EAQAQ,EAAAA,YAAA,WAaE,OAZKxoB,KAAK4O,QAAW5O,KAAK4O,OAAOoI,WAC/BzB,GAAI3H,MACF,wEACA,GAIJ5N,KAAKyoB,UAGLrlB,EAAOpD,KAAK2E,gBAAiB3E,KAAK4O,OAAOoI,WAElChX,IACT,EAACgH,EAKDyhB,QAAA,WACEzoB,KAAK4O,OAAOwB,SAASsN,aAErBta,EAAO,KAAMpD,KAAK4O,OAAOoI,UAC3B,EAAChQ,EAQD5D,OAAA,SAAO4T,GAKL,OAJKA,GACHzB,GAAI3H,MAAM,oCAAoC,GAG5CoJ,EAAUlS,WAAWnG,OAAS,GAChC4W,GAAI3H,MAAK,yBACkBoJ,EAAS,2EAGrChX,OAEDA,KAAK4O,OAAOoI,UAAYA,EACxB5T,EAAOpD,KAAK2E,gBAAiBqS,GAEtBhX,KACT,EAACooB,CAAA,CA7EQ,CAAQ9f,qGhETkqB,WAAa,MAAM,CAACjD,QAAQ,KAAK"}