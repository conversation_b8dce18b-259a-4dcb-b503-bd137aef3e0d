import { BASE_URL, navigateTo } from '../../router.js';
import ApiService from '../../services/api.js';

export default class AuthOTPView {
    constructor() {
        this.state = {
            title: 'Verify Your Identity',
            subtitle: 'Please enter the verification code sent to your phone',
            logo: {
                src: 'assets/images/brand/logo/logo.svg',
                alt: 'LockedPay'
            },
            formFields: {
                code: {
                    id: 'otp',
                    label: 'VERIFICATION CODE',
                    type: 'text',
                    placeholder: '• • • • • •',
                    required: true
                }
            },
            links: {
                resend: {
                    text: 'Resend Code',
                    href: '#!',
                    class: 'text-white fw-medium'
                },
                back: {
                    text: 'Change Phone Number',
                    href: `${BASE_URL}/login`,
                    class: 'text-white'
                }
            },
            error: {
                visible: false,
                message: ''
            }
        };

        // Get stored registration data
        this.loginPhone = JSON.parse(localStorage.getItem('login_phone') || '{}');
        if (!this.loginPhone) {
            navigateTo(`${BASE_URL}/login`);
        }
    }

    getErrorMessage() {
        if (!this.state.error.visible) return '';
        return `
            <div class="alert alert-danger alert-dismissible fade show rounded-3 border-0 shadow-sm" role="alert">
                <div class="d-flex align-items-center">
                    <i class="bi bi-exclamation-circle-fill me-2"></i>
                    <div class="flex-grow-1">${this.state.error.message}</div>
                    <button type="button" class="btn-close small shadow-none" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            </div>
        `;
    }

    getLoginForm() {
        return `
            <form class="needs-validation" novalidate>
                ${this.getErrorMessage()}
                <div class="text-center mb-4">
                    <div class="phone-badge bg-primary bg-opacity-10 text-white px-3 py-2 rounded-pill d-inline-flex align-items-center">
                        <i class="bi bi-phone me-2 text-white"></i>
                        <span class="text-dark">+268 ${this.loginPhone}</span>
                    </div>
                </div>
                
                ${this.getOTPInputs()}
                
                <div class="d-grid gap-3">
                    <button id="verify_otp" class="btn btn-primary btn-lg rounded-3 shadow-sm" type="submit">
                        <i class="bi bi-shield-check me-2"></i>Verify Code
                    </button>
                    
                    <div class="text-center">
                        <div class="timer mb-2">
                            <small class="text-white">Code expires in: <span id="timer" class="fw-bold text-white">05:00</span></small>
                        </div>
                        <div class="d-flex justify-content-center gap-3">
                            <a href="${this.state.links.resend.href}" class="${this.state.links.resend.class}" id="resendCode">
                                <i class="bi bi-arrow-clockwise text-white me-1"></i>${this.state.links.resend.text}
                            </a>
                            <span class="text-white">•</span>
                            <a href="${this.state.links.back.href}" class="${this.state.links.back.class}">
                                <i class="bi bi-arrow-left text-white me-1"></i>${this.state.links.back.text}
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        `;
    }

    render() {
        return `
            <main class="container-fluid bg-light min-vh-100">
                <div class="row align-items-center justify-content-center min-vh-100">
                    <div class="col-11 col-sm-8 col-md-6 col-lg-5 col-xl-4">
                        <div class="card border-0 rounded-4 shadow-sm auth-card">
                            <div class="card-body p-4 p-sm-5">
                                <div class="text-center mb-5">
                                    <a href="#!" class="d-inline-block mb-4">
                                        <img src="${this.state.logo.src}" 
                                             class="img-fluid" style="height: 48px;"
                                             alt="${this.state.logo.alt}" />
                                    </a>
                                    <h4 class="fw-bold text-white mb-2">${this.state.title}</h4>
                                    <p class="text-white mb-0">${this.state.subtitle}</p>
                                </div>
                                ${this.getLoginForm()}
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        `;
    }

    async handleVerification(event) {
        event.preventDefault();
        const btn = document.getElementById('verify_otp');
        const inputs = document.querySelectorAll('[data-otp-input]');
        const otp = Array.from(inputs).map(input => input.value).join('');

        if (otp.length !== 6) {
            this.showError('Please enter the complete verification code.');
            return;
        }

        try {
            btn.innerHTML = `<span class="spinner-border spinner-border-sm me-2" role="status"></span>Verifying...`;
            btn.disabled = true;

            const response = await ApiService.verifyOTP({
                phone: `${this.loginPhone}`,
                otp: otp
            });

            localStorage.setItem('user-token', response.token);
            window.is_authenticated = true;
            localStorage.setItem('is_authenticated', 'true');
            
            navigateTo(`${BASE_URL}/`);
        } catch (error) {
            console.error('Verification failed:', error);
            this.showError(this.getErrorMessageFromResponse(error));
            btn.innerHTML = `<i class="bi bi-shield-check me-2"></i>Verify Code`;
            btn.disabled = false;
        }
    }

    getErrorMessageFromResponse(error) {
        if (error.status === 401) return 'Invalid verification code';
        if (error.status === 404) return 'User not found';
        if (error.status === 429) return 'Too many attempts. Please try again later.';
        if (!navigator.onLine) return 'No internet connection. Please check your network.';
        return 'An unexpected error occurred. Please try again.';
    }

    showError(message) {
        this.state.error = { visible: true, message };
        const formContainer = document.querySelector('.card-body');
        if (formContainer) {
            formContainer.innerHTML = this.getLoginForm();
            this.initializeEventListeners();
            this.initializeOTPInputs();
        }
    }

    getOTPInputs() {
        return `
            <div class="otp-inputs d-flex gap-2 mb-4 justify-content-center flex-wrap">
                ${Array(6).fill().map((_, i) => `
                    <input type="tel" inputmode="numeric"
                        class="form-control form-control-lg text-center fw-bold shadow-none bg-light-soft"
                        maxlength="1"
                        data-otp-input
                        style="width: 40px; min-width: 0; flex: 1 1 40px; max-width: 50px;"
                        ${i === 0 ? 'autofocus' : ''}
                    >
                `).join('')}
            </div>
        `;
    }

    initializeOTPInputs() {
        const inputs = document.querySelectorAll('[data-otp-input]');
        inputs.forEach((input, index) => {
            input.addEventListener('input', (e) => {
                e.target.value = e.target.value.replace(/[^0-9]/g, '').slice(0, 1);
                if (e.target.value && index < inputs.length - 1) {
                    inputs[index + 1].focus();
                }
            });

            input.addEventListener('keydown', (e) => {
                if (e.key === 'Backspace' && !e.target.value && index > 0) {
                    inputs[index - 1].focus();
                }
            });

            input.addEventListener('paste', (e) => {
                e.preventDefault();
                const pastedData = e.clipboardData.getData('text').replace(/[^0-9]/g, '').slice(0, 6);
                inputs.forEach((input, i) => {
                    input.value = pastedData[i] || '';
                });
            });
        });
    }

    startTimer() {
        let timeLeft = 300;
        const timerElement = document.getElementById('timer');
        const resendButton = document.getElementById('resendCode');
        
        resendButton.style.opacity = '0.5';
        resendButton.style.pointerEvents = 'none';
        
        const timer = setInterval(() => {
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            
            timerElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            
            if (timeLeft === 0) {
                clearInterval(timer);
                timerElement.innerHTML = '<span class="text-danger">Expired</span>';
                resendButton.style.opacity = '1';
                resendButton.style.pointerEvents = 'auto';
            }
            timeLeft--;
        }, 1000);
    }

    async handleResendCode() {
        const resendButton = document.getElementById('resendCode');
        try {
            resendButton.innerHTML = `<span class="spinner-border spinner-border-sm me-2"></span>Resending...`;
            resendButton.style.pointerEvents = 'none';

            await ApiService.resendOTP({ phone: this.loginPhone });
            
            this.startTimer();
            resendButton.innerHTML = `<i class="bi bi-arrow-clockwise me-1"></i>${this.state.links.resend.text}`;
        } catch (error) {
            console.error('Failed to resend code:', error);
            this.showError('Failed to resend verification code. Please try again.');
            resendButton.innerHTML = `<i class="bi bi-arrow-clockwise me-1"></i>${this.state.links.resend.text}`;
            resendButton.style.pointerEvents = 'auto';
        }
    }

    initializeEventListeners() {
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', (e) => this.handleVerification(e));
        }

        const resendButton = document.getElementById('resendCode');
        if (resendButton) {
            resendButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleResendCode();
            });
        }

        const errorCloseBtn = document.querySelector('.alert .btn-close');
        if (errorCloseBtn) {
            errorCloseBtn.addEventListener('click', () => {
                this.state.error = { visible: false, message: '' };
            });
        }
    }

    preventBackNavigation() {
        history.pushState(null, null, location.href);
        window.onpopstate = () => history.go(1);
    }

    afterRender() {
        this.initializeEventListeners();
        this.initializeOTPInputs();
        this.startTimer();
        this.preventBackNavigation();
    }
}