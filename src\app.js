import Header from './components/header.js';
import { BASE_URL, navigateTo } from './router.js';
import { initThemeToggle } from '../assets/js/vendors/color-modes.js'; // Adjust path as needed

class App {
    constructor() {
        this.header = new Header();
        this.initAuth();
    }

    init() {
        // Check authentication before rendering anything
        const currentPath = window.location.pathname.replace(/\/$/, '');
        const publicRoutes = [BASE_URL + '/login', BASE_URL + '/auth_otp'];
        const requiresAuth = !publicRoutes.includes(currentPath);

        // If this is a protected route and user is not authenticated, don't render
        if (requiresAuth && !window.is_authenticated) {
            return; // Let the inline script handle the redirect
        }

        // Initialize theme toggle before rendering UI
        initThemeToggle();

        // Only render header if authenticated or on public route
        document.getElementById('app').innerHTML = this.header.render();
        // Initialize header functionality
        this.header.afterRender();
    }

    initAuth() {
        // Restore authentication state
        window.is_authenticated = localStorage.getItem('is_authenticated') === 'true';
        
        // Redirect if not authenticated
        if (!window.is_authenticated && window.location.pathname !== BASE_URL + '/login') {
            navigateTo(BASE_URL + '/login');
        }
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const app = new App();
    app.init();
});