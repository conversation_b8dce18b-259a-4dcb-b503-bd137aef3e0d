export function initThemeToggle() {
  "use strict";
  const getStoredTheme = () => localStorage.getItem("theme");
  const getPreferredTheme = () => {
      let storedTheme = getStoredTheme();
      // Force light theme if nothing is stored
      if (!storedTheme) {
          storedTheme = "light";
          localStorage.setItem("theme", "light");
      }
      return storedTheme;
  };
  const applyTheme = theme => {
      if (theme === "auto" && window.matchMedia("(prefers-color-scheme: dark)").matches) {
          document.documentElement.setAttribute("data-bs-theme", "dark");
      } else {
          document.documentElement.setAttribute("data-bs-theme", theme);
      }
  };

  const updateActiveIcon = (theme, focus = false) => {
      const themeText = document.querySelector(".bs-theme-text");
      const activeButton = document.querySelector(`[data-bs-theme-value="${theme}"]`);
      document.querySelectorAll("[data-bs-theme-value]").forEach(btn => {
          btn.classList.remove("active");
          btn.setAttribute("aria-pressed", "false");
      });

      if (activeButton) {
          activeButton.classList.add("active");
          activeButton.setAttribute("aria-pressed", "true");
          if (focus) activeButton.focus();

          const iconElement = activeButton.querySelector(".theme-icon");
          const activeIconContainer = document.querySelector(".theme-icon-active");
          if (iconElement && activeIconContainer) {
              activeIconContainer.innerHTML = iconElement.outerHTML;
          }
      }
  };

  // Set initial theme
  applyTheme(getPreferredTheme());
  updateActiveIcon(getPreferredTheme());

  // Update on system preference change (if auto)
  window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change", () => {
      const currentTheme = getStoredTheme();
      if (currentTheme !== "light" && currentTheme !== "dark") {
          applyTheme(getPreferredTheme());
      }
  });

  // Set up click listeners
  document.querySelectorAll("[data-bs-theme-value]").forEach(btn => {
      btn.addEventListener("click", () => {
          const theme = btn.getAttribute("data-bs-theme-value");
          localStorage.setItem("theme", theme);
          applyTheme(theme);
          updateActiveIcon(theme, true);
      });
  });
}