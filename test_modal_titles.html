<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modal Title Text Color Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <link href="assets/css/alerts.css" rel="stylesheet">
    <style>
        body {
            background-color: #0A3D72;
            color: white;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .modal-content {
            background-color: #0A3D72;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .modal-header {
            background-color: rgba(255, 255, 255, 0.1);
        }
        .modal-body {
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Modal Title Text Color Test</h1>
        <p>This page tests that all modal titles have white text color.</p>

        <div class="test-section">
            <h3>Bootstrap Modal Tests</h3>
            <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#successModal">Success Modal</button>
            <button class="btn btn-warning me-2" data-bs-toggle="modal" data-bs-target="#warningModal">Warning Modal</button>
            <button class="btn btn-danger me-2" data-bs-toggle="modal" data-bs-target="#errorModal">Error Modal</button>
            <button class="btn btn-info me-2" data-bs-toggle="modal" data-bs-target="#infoModal">Info Modal</button>
        </div>

        <div class="test-section">
            <h3>SweetAlert Tests</h3>
            <button class="btn btn-success me-2" onclick="showSuccessAlert()">Success Alert</button>
            <button class="btn btn-warning me-2" onclick="showWarningAlert()">Warning Alert</button>
            <button class="btn btn-danger me-2" onclick="showErrorAlert()">Error Alert</button>
            <button class="btn btn-info me-2" onclick="showInfoAlert()">Info Alert</button>
        </div>

        <div class="test-section">
            <h3>Application Modal Tests</h3>
            <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#newTransactionModal">New Transaction</button>
            <button class="btn btn-secondary me-2" data-bs-toggle="modal" data-bs-target="#confirmModal">Confirm Modal</button>
        </div>
    </div>

    <!-- Success Modal -->
    <div class="modal fade" id="successModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-white">
                        <i class="bi bi-check-circle-fill me-2 text-success"></i>Success
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>This is a success modal with white title text.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Warning Modal -->
    <div class="modal fade" id="warningModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-white">
                        <i class="bi bi-exclamation-triangle-fill me-2 text-warning"></i>Warning
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>This is a warning modal with white title text.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-warning" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Error Modal -->
    <div class="modal fade" id="errorModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-white">
                        <i class="bi bi-x-circle-fill me-2 text-danger"></i>Error
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>This is an error modal with white title text.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Info Modal -->
    <div class="modal fade" id="infoModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-white">
                        <i class="bi bi-info-circle-fill me-2 text-info"></i>Information
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>This is an info modal with white title text.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-info" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- New Transaction Modal -->
    <div class="modal fade" id="newTransactionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-white">
                        <i class="bi bi-plus-circle me-2"></i>New Payment
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>This is a new transaction modal with white title text.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary">Create Payment</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Confirm Modal -->
    <div class="modal fade" id="confirmModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-light">
                    <h5 class="modal-title text-white">
                        <i class="bi bi-shield-check me-2"></i>Confirm Payment Details
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>This is a confirmation modal with white title text.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary">Confirm</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        function showSuccessAlert() {
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'This is a success alert with white title text.',
                customClass: {
                    popup: 'my-swal-popup',
                    title: 'my-swal-title',
                    content: 'my-swal-content',
                    confirmButton: 'my-confirm-button',
                    htmlContainer: 'my-swal-text'
                }
            });
        }

        function showWarningAlert() {
            Swal.fire({
                icon: 'warning',
                title: 'Warning!',
                text: 'This is a warning alert with white title text.',
                customClass: {
                    popup: 'my-swal-popup',
                    title: 'my-swal-title',
                    content: 'my-swal-content',
                    confirmButton: 'my-confirm-button',
                    htmlContainer: 'my-swal-text'
                }
            });
        }

        function showErrorAlert() {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'This is an error alert with white title text.',
                customClass: {
                    popup: 'my-swal-popup',
                    title: 'my-swal-title',
                    content: 'my-swal-content',
                    confirmButton: 'my-confirm-button',
                    htmlContainer: 'my-swal-text'
                }
            });
        }

        function showInfoAlert() {
            Swal.fire({
                icon: 'info',
                title: 'Information',
                text: 'This is an info alert with white title text.',
                customClass: {
                    popup: 'my-swal-popup',
                    title: 'my-swal-title',
                    content: 'my-swal-content',
                    confirmButton: 'my-confirm-button',
                    htmlContainer: 'my-swal-text'
                }
            });
        }

        // Test that all modal titles are white on page load
        document.addEventListener('DOMContentLoaded', function() {
            const modalTitles = document.querySelectorAll('.modal-title');
            let allWhite = true;
            
            modalTitles.forEach(title => {
                const computedStyle = window.getComputedStyle(title);
                const color = computedStyle.color;
                
                // Check if color is white (rgb(255, 255, 255) or #ffffff)
                if (color !== 'rgb(255, 255, 255)' && color !== '#ffffff' && color !== 'white') {
                    console.warn('Modal title not white:', title, 'Color:', color);
                    allWhite = false;
                }
            });
            
            if (allWhite) {
                console.log('✅ All modal titles have white text color');
            } else {
                console.error('❌ Some modal titles do not have white text color');
            }
        });
    </script>
</body>
</html>
