import { logout } from './router.js';

let timeout;
const inactivityTime = 5 * 60 * 1000; // 5 minutes in milliseconds

// Function to run after 5 minutes of inactivity
function runAfterInactivity() {
    // Clear the timeout to prevent multiple calls
    clearTimeout(timeout);

    Swal.fire({
        icon: 'info',
        title: 'Session Timeout',
        text: 'Your session has timed out due to inactivity. You will be redirected to the login page.',
        confirmButtonText: 'OK',
        allowOutsideClick: false,
        allowEscapeKey: false,
        customClass: {
            popup: 'my-swal-popup',
            title: 'my-swal-title',
            content: 'my-swal-content',
            confirmButton: 'my-confirm-button',
            htmlContainer: 'my-swal-text'
        }
    }).then((result) => {
        // Only logout after user confirms the dialog
        if (result.isConfirmed || result.isDismissed) {
            logout();
        }
    });
}

// Reset the inactivity timer
function resetTimer() {
    clearTimeout(timeout);
    timeout = setTimeout(runAfterInactivity, inactivityTime);
}

// Set up event listeners for user activity
window.onload = resetTimer;           // When the window loads
document.onmousemove = resetTimer;    // When the mouse is moved
document.onkeypress = resetTimer;     // When a key is pressed
document.ontouchstart = resetTimer;   // For touch devices
document.onclick = resetTimer;        // On mouse click
document.onscroll = resetTimer;       // On scroll

// Initialize the inactivity timer
resetTimer();