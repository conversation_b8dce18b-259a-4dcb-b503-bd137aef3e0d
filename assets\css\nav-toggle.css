/* Navigation toggle styles */
#main-wrapper {
    transition: all 0.3s ease-in-out;
}

#main-wrapper.toggled .navbar-vertical {
    margin-left: -16.25rem;
}

#main-wrapper.toggled #app-content {
    margin-left: 0;
}

/* Mobile styles */
@media (max-width: 768px) {
    #main-wrapper.toggled .navbar-vertical {
        margin-left: 0;
    }
    
    #main-wrapper.toggled #app-content {
        margin-left: 16.25rem;
    }
}

/* Hover effect for toggle button */
#nav-toggle:hover {
    opacity: 0.8;
    cursor: pointer;
}

/* Enhanced UI Styles */
.hover-lift {
    transition: all 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #012D5A 0%, #1e3a8a 100%);
}

.stat-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.help-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.stat-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.hero-section .card {
    overflow: hidden;
    position: relative;
}

.hero-section .card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
    pointer-events: none;
}

.empty-state i {
    opacity: 0.5;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.btn {
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.accordion-button:not(.collapsed) {
    background-color: rgba(13, 110, 253, 0.1);
    border-color: rgba(13, 110, 253, 0.2);
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

@media (max-width: 768px) {
    .hero-section .display-6 {
        font-size: 2rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
}
