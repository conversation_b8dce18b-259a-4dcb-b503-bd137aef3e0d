<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI Freeze Fixes Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .test-pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>UI Freeze Fixes Test Suite</h1>
        <p class="text-muted">This page tests the fixes for modal closing, timeout popup, and file upload UI freeze issues.</p>

        <!-- Modal Closing Test -->
        <div class="test-section">
            <h3>1. Modal Closing Test</h3>
            <p>Tests rapid modal opening/closing to check for UI freezes</p>
            <button id="testModalBtn" class="btn btn-primary">Test Modal Closing</button>
            <button id="rapidModalBtn" class="btn btn-warning">Rapid Modal Test</button>
            <div id="modalTestResult" class="test-result d-none"></div>
        </div>

        <!-- Timeout Popup Test -->
        <div class="test-section">
            <h3>2. Timeout Popup Test</h3>
            <p>Tests timeout popup behavior to ensure no UI freeze</p>
            <button id="testTimeoutBtn" class="btn btn-danger">Test Timeout Popup</button>
            <div id="timeoutTestResult" class="test-result d-none"></div>
        </div>

        <!-- File Upload Test -->
        <div class="test-section">
            <h3>3. File Upload Test</h3>
            <p>Tests file upload processing to check for UI freezes</p>
            <input type="file" id="testFileInput" class="form-control mb-2" accept="image/*,.pdf,.doc,.docx">
            <button id="testFileBtn" class="btn btn-success">Test File Processing</button>
            <div id="fileTestResult" class="test-result d-none"></div>
            <div id="filePreview" class="mt-2"></div>
        </div>

        <!-- Memory Leak Test -->
        <div class="test-section">
            <h3>4. Memory Leak Test</h3>
            <p>Tests for event listener cleanup and memory leaks</p>
            <button id="testMemoryBtn" class="btn btn-info">Test Memory Management</button>
            <div id="memoryTestResult" class="test-result d-none"></div>
        </div>
    </div>

    <!-- Test Modal -->
    <div class="modal fade" id="testModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Test Modal</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>This is a test modal for checking UI freeze issues.</p>
                    <div class="attachment-section">
                        <input type="file" class="file-input d-none" accept="image/*,.pdf,.doc,.docx">
                        <input type="file" class="camera-input d-none" accept="image/*" capture="environment">
                        <div class="attachment-preview mt-3 d-none">
                            <div class="attachment-name"></div>
                            <img class="image-preview d-none" style="max-width: 100%; max-height: 200px;">
                        </div>
                        <button type="button" class="btn btn-outline-primary attach-file-btn">
                            Add Attachment
                        </button>
                        <button type="button" class="btn btn-sm btn-danger remove-attachment d-none">
                            Remove
                        </button>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary">Save</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // Test utilities
        function showTestResult(elementId, success, message) {
            const element = document.getElementById(elementId);
            element.className = `test-result ${success ? 'test-pass' : 'test-fail'}`;
            element.textContent = message;
            element.classList.remove('d-none');
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // Modal closing test
        document.getElementById('testModalBtn').addEventListener('click', async () => {
            try {
                const modal = new bootstrap.Modal(document.getElementById('testModal'));
                modal.show();
                
                await sleep(500);
                modal.hide();
                
                await sleep(500);
                showTestResult('modalTestResult', true, 'Modal opening/closing test passed - no UI freeze detected');
            } catch (error) {
                showTestResult('modalTestResult', false, `Modal test failed: ${error.message}`);
            }
        });

        // Rapid modal test
        document.getElementById('rapidModalBtn').addEventListener('click', async () => {
            try {
                const modalElement = document.getElementById('testModal');
                
                for (let i = 0; i < 5; i++) {
                    const modal = new bootstrap.Modal(modalElement);
                    modal.show();
                    await sleep(100);
                    modal.hide();
                    await sleep(100);
                }
                
                showTestResult('modalTestResult', true, 'Rapid modal test passed - no UI freeze detected');
            } catch (error) {
                showTestResult('modalTestResult', false, `Rapid modal test failed: ${error.message}`);
            }
        });

        // Timeout popup test
        document.getElementById('testTimeoutBtn').addEventListener('click', async () => {
            try {
                // Simulate timeout popup without actually logging out
                await Swal.fire({
                    icon: 'info',
                    title: 'Test Timeout',
                    text: 'This is a test timeout popup.',
                    confirmButtonText: 'OK',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    timer: 2000,
                    timerProgressBar: true
                });
                
                showTestResult('timeoutTestResult', true, 'Timeout popup test passed - no UI freeze detected');
            } catch (error) {
                showTestResult('timeoutTestResult', false, `Timeout test failed: ${error.message}`);
            }
        });

        // File upload test
        document.getElementById('testFileBtn').addEventListener('click', async () => {
            const fileInput = document.getElementById('testFileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                showTestResult('fileTestResult', false, 'Please select a file first');
                return;
            }
            
            try {
                // Simulate file processing
                const preview = document.getElementById('filePreview');
                preview.innerHTML = '<div class="spinner-border" role="status"></div> Processing file...';
                
                await sleep(1000); // Simulate processing time
                
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        preview.innerHTML = `<img src="${e.target.result}" style="max-width: 200px; max-height: 200px;">`;
                    };
                    reader.readAsDataURL(file);
                } else {
                    preview.innerHTML = `<p>File: ${file.name} (${file.size} bytes)</p>`;
                }
                
                showTestResult('fileTestResult', true, 'File processing test passed - no UI freeze detected');
            } catch (error) {
                showTestResult('fileTestResult', false, `File test failed: ${error.message}`);
            }
        });

        // Memory leak test
        document.getElementById('testMemoryBtn').addEventListener('click', async () => {
            try {
                let eventListenerCount = 0;
                
                // Create and destroy multiple elements with event listeners
                for (let i = 0; i < 100; i++) {
                    const element = document.createElement('button');
                    element.id = `test-btn-${i}`;
                    element.addEventListener('click', () => eventListenerCount++);
                    document.body.appendChild(element);
                    
                    // Simulate cleanup
                    setTimeout(() => {
                        if (element.parentNode) {
                            element.parentNode.removeChild(element);
                        }
                    }, 10);
                }
                
                await sleep(500);
                showTestResult('memoryTestResult', true, 'Memory management test passed - no memory leaks detected');
            } catch (error) {
                showTestResult('memoryTestResult', false, `Memory test failed: ${error.message}`);
            }
        });

        // Monitor for UI freezes
        let lastTime = performance.now();
        function checkUIResponsiveness() {
            const currentTime = performance.now();
            const timeDiff = currentTime - lastTime;
            
            if (timeDiff > 100) { // If more than 100ms between frames, UI might be freezing
                console.warn(`Potential UI freeze detected: ${timeDiff}ms gap`);
            }
            
            lastTime = currentTime;
            requestAnimationFrame(checkUIResponsiveness);
        }
        
        checkUIResponsiveness();
    </script>
</body>
</html>
