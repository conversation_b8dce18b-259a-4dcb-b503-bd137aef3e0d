.input-group {
    position: relative;
    display: flex;
    width: 100%;
}

.input-group .input-group-text {
    transition: all 0.3s ease;
    flex-shrink: 0;
    min-width: 90px;
}

.input-group input {
    transition: all 0.3s ease;
    flex: 1 1 0%;
    min-width: 0;
}

/* This is the key change - using :focus-within on the parent to detect input focus */
.input-group:focus-within .input-group-text {
    display: none;
    width: 0;
    padding: 0;
    margin: 0;
    border: none;
    opacity: 0;
    min-width: 0;
}

/* Ensure proper border radius when span is hidden */
.input-group:focus-within input {
    border-top-right-radius: 0.375rem !important;
    border-bottom-right-radius: 0.375rem !important;
}