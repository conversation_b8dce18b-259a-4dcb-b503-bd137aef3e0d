export default class Alert {
    constructor() {}
    
    static showAlert(formElement, message, type = 'success') {
        // Remove any existing alerts
        const existingAlerts = formElement.querySelectorAll('.alert');
        existingAlerts.forEach(alert => alert.remove());
    
        // Create and insert new alert
        const alertHTML = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        formElement.insertAdjacentHTML('afterbegin', alertHTML);
    
        // Get the newly inserted alert
        const newAlert = formElement.querySelector('.alert');
        if (newAlert) {
            // Initialize Bootstrap alert
            const bsAlert = new bootstrap.Alert(newAlert);
    
            // Auto dismiss after 5 seconds
            setTimeout(() => {
                bsAlert.close();
                // Remove from DOM after animation
                newAlert.addEventListener('closed.bs.alert', () => {
                    newAlert.remove();
                });
            }, 5000);
        }
    }
}