<svg width="300" height="50" viewBox="0 0 300 50" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .padlock {
        fill: #FFD000; /* Start color of gradient */
      }
      .shackle {
        fill: none;
        stroke: #FFB300; /* End color of gradient */
        stroke-width: 3;
        stroke-linecap: round;
      }
      .text {
        font-family: sans-serif; /* Keeping a similar sans-serif font */
        font-size: 40px;
        fill: #FFD000; /* Start color of gradient */
        dominant-baseline: middle;
        text-anchor: start;
      }
      .bold {
        font-weight: bold;
      }
    </style>
  </defs>
  <g>
    <path class="padlock" d="M40 15 H 20 A 5 5 0 0 0 15 20 V 40 A 5 5 0 0 0 20 45 H 40 A 5 5 0 0 0 45 40 V 20 A 5 5 0 0 0 40 15 Z M30 30 A 5 5 0 1 1 30 20 A 5 5 0 0 1 30 30 Z"/>
    <path class="shackle" d="M30 15 V 10 A 8 8 0 0 0 14 10 V 15 M30 15 V 10 A 8 8 0 0 1 46 10 V 15"/>
    <text x="60" y="30" class="text bold">Locked</text>
    <text x="200" y="30" class="text">Pay</text>
  </g>
</svg>