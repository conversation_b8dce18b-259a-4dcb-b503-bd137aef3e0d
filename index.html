<!DOCTYPE html>
<html lang="en">

<head>
  <!-- Required meta tags -->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="author" content="Centurion Tech" />

    <!-- Favicon icon-->
    <link rel="shortcut icon" type="image/x-icon" href="assets/images/favicon.jpg" />
    
    <!-- Color modes -->
    <script type="module" src="assets/js/vendors/color-modes.js"></script>

    <!-- Libs CSS -->
    <link href="assets/libs/bootstrap-icons/font/bootstrap-icons.min.css" rel="stylesheet" />
    <link href="assets/libs/@mdi/font/css/materialdesignicons.min.css" rel="stylesheet" />
    <link href="assets/libs/simplebar/dist/simplebar.min.css" rel="stylesheet" />
    <link href="assets/libs/dropzone/dist/dropzone.css" rel="stylesheet">
    <link href="assets/libs/gridjs/theme/mermaid.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">

    <!-- Theme CSS -->
    <link rel="stylesheet" href="assets/css/theme.css">
    <link rel="stylesheet" href="assets/css/asthetic.css">
    <link rel="stylesheet" href="assets/css/nav-toggle.css">
    <link rel="stylesheet" href="assets/css/input-group.css">
    <link rel="stylesheet" href="assets/css/alerts.css">
    <title>Locked Pay</title>
</head>

<body>
    <!-- Immediate auth check to prevent content flash -->
    <script>
        (function() {
            const currentPath = window.location.pathname.replace(/\/$/, '');
            const basePath = '/LP_Merchant_Client';
            const publicRoutes = [basePath + '/login', basePath + '/auth_otp'];
            const requiresAuth = !publicRoutes.includes(currentPath);
            const isAuthenticated = localStorage.getItem('is_authenticated') === 'true';

            // If accessing protected route without auth, redirect immediately
            if (requiresAuth && !isAuthenticated) {
                window.location.replace(basePath + '/login');
            }
        })();
    </script>

    <div id="app"></div>

    <!-- Scripts -->
    <script type="module" src="src/app.js"></script>
    <script type="module" src="src/router.js"></script>
    <script type="module" src="src/views/auth/sso-login.js"></script>

    <!-- Libs JS -->
    <script src="assets/libs/jquery/jquery.min.js"></script>
    <script src="assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/libs/feather-icons/dist/feather.min.js"></script>
    <script src="assets/libs/simplebar/dist/simplebar.min.js"></script>
    <script src="assets/libs/dropzone/dist/min/dropzone.min.js"></script>
    <script src="assets/libs/sweetalert/sweetalert.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/gridjs/dist/gridjs.umd.js"></script>

    <!-- Theme JS -->
    <script src="assets/js/theme.js"></script>
    <script type="module" defer src="src/timeout.js"></script>

    <script src="assets/libs/apexcharts/dist/apexcharts.min.js"></script>
    <script src="assets/js/vendors/chart.js"></script>
</body>
</html>