import ApiService from '../services/api.js';
import Alert from '../components/alert.js';

import * as Templates from './homeUiTemplates.js';
import * as UIManager from './homeUiManager.js';

export default class HomeView {
    constructor() {
        this.header = null;
        this.sidebar = null;
        this.merchants = [];
        this.publicReceivers = [];
        this.vouchers = [];

        this.loadCoreComponents();
    }

    async loadCoreComponents() {
        try {
            const headerModule = await import('../components/header.js');
            const sidebarModule = await import('../components/sidebar.js');

            this.header = new headerModule.default();
            this.sidebar = new sidebarModule.default();
            this.updateView();
        } catch (error) {
            console.error("Fatal Error: Failed to load core components (Header/Sidebar):", error);
            const mainWrapper = document.getElementById('main-wrapper');
            if (mainWrapper) {
                mainWrapper.innerHTML = `<div class="container mt-5"><div class="alert alert-danger" role="alert">
                    Error loading page components. Please try refreshing the page. If the problem persists, contact support.
                    </div></div>`;
            }
        }
    }

    render() {
        return `<div id="main-wrapper" class="main-wrapper"></div>`;
    }

    afterRender() {
        // Placeholder for any logic after initial render
    }

    updateView() {
        const mainWrapper = document.getElementById('main-wrapper');
        if (!mainWrapper) {
            console.error("Critical: main-wrapper element not found. Cannot update view.");
            return;
        }
        if (!this.header || !this.sidebar) {
            console.warn("Header or Sidebar component not loaded yet. View update deferred.");
            return;
        }

        // Render initial layout with loading spinner for vouchers section
        mainWrapper.innerHTML = Templates.mainPageLayoutTemplate(
            this.header.render(),
            this.sidebar.render(),
            Templates.getMainContentTemplate(),
            Templates.newTransactionModalTemplate(),
            Templates.newPublicReceiverTransactionModalTemplate(),
            Templates.newDirectPayModalTemplate(),
            `<div>${Templates.getPublicReceiversTemplate()}</div>`,
            `<div>${Templates.getQuickHelpTemplate()}</div>`,
            `<div id="vouchers-section"><span class="spinner-border"></span> Loading vouchers...</div>`,
            `<div>${Templates.getTransactionsTemplate()}</div>`
        );

        requestAnimationFrame(() => {
            if (this.header.afterRender) this.header.afterRender();
            if (this.sidebar.afterRender) this.sidebar.afterRender();

            UIManager.initializeEventListeners(this);
            UIManager.setGreeting();
            UIManager.requestPublicReceivers();
    
            this.requestVouchersData();

            if (window.feather) {
                feather.replace();
            }

            UIManager.initializeMerchantData().then(() => {
                // Optionally store merchants if needed
                // this.merchants = UIManager.getInitializedMerchants();
            });

            // Now fetch and render vouchers asynchronously
            this.loadAndRenderVouchers();

            // Load recent transactions
            this.loadRecentTransactions();

            UIManager.setupTransactionModalToggles();
        });
    }

    // --- Business Logic / Orchestration Methods ---

    async createTransactionStepOne(event, paymentType) {
        const modalContent = event.target.closest('.modal-content');
        const modalBody = modalContent?.querySelector('.modal-body');

        if (!modalBody) {
            console.error("Cannot find modal body for validation messages.");
            return;
        }

        const merchantID = document.getElementById('merchants')?.value;
        const branchID   = document.getElementById('branches')?.value;
        const amount     = document.getElementById('amount')?.value;
        const payFrom    = document.getElementById('pay_from')?.value;
        const reference  = document.getElementById('default_ref')?.value;

        let errors = [];
        if (!merchantID) errors.push("Merchant is required.");
        if (document.getElementById('branches') && !document.getElementById('branches').disabled && !branchID) {
             errors.push("Branch is required when available.");
        }
        if (!amount || parseFloat(amount) <= 0) errors.push("A valid positive amount is required.");
        if (!payFrom) errors.push("Payment platform selection is required.");
        else if (payFrom !== "MTN_MOMO") errors.push("Only MTN MoMo is currently available. Other payment platforms are coming soon.");

        if (errors.length > 0) {
            Alert.showAlert(modalBody, errors.join('\n'), 'warning');
            return;
        }
        const modalElement = document.getElementById('newTransactionModal');
        const extraRefs = UIManager.collectExtraReferences(modalElement);

        try {
            const price = await ApiService.getPricing(amount, "voucher", payFrom);
            const charge = (Number(price.service_charge)).toFixed(2);
            const total = (Number(price.total_amount)).toFixed(2);
            UIManager.showConfirmTransactionModal(this, paymentType, merchantID, amount, charge, total, payFrom, reference, extraRefs);

        } catch (error) {
            console.error("Error in createTransactionStepOne (getting pricing):", error);
            Alert.showAlert(modalBody, `Error: ${error.message || 'Could not retrieve pricing information.'}`, 'danger');
        }
    }

    async createTransactionStepTwo(event, extraRefs, attachmentData) {
        const modalContent = event.target.closest('.modal-content');
        const modalBody = modalContent?.querySelector('.modal-body');
        if (!modalBody) {
            console.error("Cannot find modal body for error messages.");
            return;
        }

        const btn = document.getElementById('authorize_transaction');
        if (btn) {
            btn.innerHTML = `<span class="spinner-grow spinner-grow-sm" role="status" aria-hidden="true"></span> Waiting for Authorization...`;
            btn.disabled = true;
        }

        const branchId = document.getElementById('c_merchant')?.value;
        const amount = document.getElementById('c_amount')?.value;
        const charge = document.getElementById("c_charge")?.value;
        const total = document.getElementById("c_total")?.value;
        const payFrom = document.getElementById('c_pay_from')?.value;
        const reference = document.getElementById('c_default_ref')?.value;

        const payload = {
            branch_id: branchId,
            amount: parseFloat(amount),
            charge: parseFloat(charge),
            total_amount: parseFloat(total),
            pay_from: payFrom,
            default_reference: reference,
            extra_reference: extraRefs.map(item => item.value),
            phone: localStorage.getItem('login_phone'),
            mode: "CUSTOMER_INITIATED",
            is_public_biller: false
        };
        
        try {
            const response = await ApiService.createVoucherWithAttachment(payload, attachmentData)

            if (response.message == "Voucher created successfully") {
                $("#confirmTransactionModal").modal("hide");
                UIManager.showSuccessModal(
                    "Voucher Created", 
                    `Payment has been created, your payment code is: ${response.voucher_code}.<br> This code will expire at: ${response.expires_at}`,
                    () => this.requestVouchersData()
                );
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: response.message || 'Failed to create voucher. Please make sure you have sufficient balance.',
                    confirmButtonText: 'OK',
                    customClass: {
                        popup: 'my-swal-popup',
                        title: 'my-swal-title',
                        content: 'my-swal-content',
                        htmlContainer: 'my-swal-text',
                        confirmButton: 'my-confirm-button'
                    }
                });
            }
        } catch (error) {
            const existingModal = document.getElementById("confirmTransactionModal");
            if (existingModal) {
                const bsModal = bootstrap.Modal.getInstance(existingModal);
                bsModal.hide();
            }

            console.error("Error in createTransactionStepTwo (creating voucher):", error);
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: error || error.message || 'Failed to create voucher. Please make sure you have sufficient balance.',
                confirmButtonText: 'OK',
                customClass: {
                    popup: 'my-swal-popup',
                    title: 'my-swal-title',
                    content: 'my-swal-content',
                    htmlContainer: 'my-swal-text',
                    confirmButton: 'my-confirm-button'
                }
            });
        } finally {
            if (btn) {
                btn.innerHTML = `<i class="bi bi-shield-lock me-2"></i><span>Confirm & Create Voucher</span>`;
                btn.disabled = false;
            }
        }
    }

    async payNowWithMOMO(event) {
        const modalContent = event.target.closest('.modal-content');
        const modalBody = modalContent?.querySelector('.modal-body');
        if (!modalBody) {
            console.error("Cannot find modal body for messages.");
            return;
        }

        const branchID = document.getElementById('branches')?.value;
        const amount = document.getElementById('amount')?.value;
        const payFrom = document.getElementById('pay_from')?.value;
        const reference = document.getElementById('default_ref')?.value;

        if (payFrom !== "MTN_MOMO") {
            Alert.showAlert(modalBody, "Please select MTN Mobile Money to use this payment option.", 'warning');
            return;
        }
    
        let errors = [];
        if (!branchID) errors.push("Branch is required.");
        if (!amount || parseFloat(amount) <= 0) errors.push("A valid positive amount is required.");
        if (errors.length > 0) {
            Alert.showAlert(modalBody, errors.join('\n'), 'warning');
            return;
        }

        const modalElement = document.getElementById('newTransactionModal');
        const attachmentData = UIManager.getAttachmentData(modalElement);

        let price   = await ApiService.getPricing(amount, "direct", "MTN_MOMO");
        let charge  = (Number(price['service_charge'])).toFixed(2);
        let total   = (Number(price['total_amount'])).toFixed(2);

        const extraRefs = UIManager.collectExtraReferences(modalElement);

        const payload = {
            branch_id: branchID,
            amount: Number(amount),
            charge: Number(charge),
            total_amount: Number(total),
            pay_from: "MTN_MOMO",
            default_reference: reference,
            extra_reference: extraRefs.map(item => item.value),
            phone: localStorage.getItem('login_phone'),
            mode: "DIRECT_MOMO",
            is_public_biller: false
        }

        const btn = document.getElementById('pay_now');
        if (btn) {
            btn.innerHTML = `<span class="spinner-grow spinner-grow-sm" role="status" aria-hidden="true"></span> Waiting for Authorization...`;
            btn.disabled = true;
        }

        try {
            const response = await ApiService.createVoucherWithAttachment(payload, attachmentData)

            if (response.message == "MOMO Payment completed successfully") {
                $("#confirmTransactionModal").modal("hide");
                UIManager.showSuccessModal(
                    "Direct MOMO payment initiated", 
                    "Payment has been created, Thank you for using our service!",
                    () => this.requestVouchersData()
                );
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: response.message || 'Failed to make payment. Please make sure you have sufficient balance.',
                    confirmButtonText: 'OK',
                    customClass: {
                        popup: 'my-swal-popup',
                        title: 'my-swal-title',
                        content: 'my-swal-content',
                        htmlContainer: 'my-swal-text',
                        confirmButton: 'my-confirm-button'
                    }
                });
            }
        } catch (error) {
            const existingModal = document.getElementById("confirmTransactionModal");
            if (existingModal) {
                const bsModal = bootstrap.Modal.getInstance(existingModal);
                bsModal.hide();
            }

            console.error("Error in createTransactionStepTwo (creating voucher):", error);
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: error || error.message || 'Failed to make payment. Please make sure you have sufficient balance.',
                confirmButtonText: 'OK',
                customClass: {
                    popup: 'my-swal-popup',
                    title: 'my-swal-title',
                    content: 'my-swal-content',
                    htmlContainer: 'my-swal-text',
                    confirmButton: 'my-confirm-button'
                }
            });
        } finally {
            if (btn) {
                btn.innerHTML = `<i class="bi bi-lightning-charge me-2"></i><span>Confirm & Pay With MTN MoMo</span>`;
                btn.disabled = false;
            }
        }
    }

    async payPublicReceiver(event, extraRefs, attachmentData) {
        const modalContent = event.target.closest('.modal-content');
        const modalBody = modalContent?.querySelector('.modal-body');
        if (!modalBody) {
            console.error("Cannot find modal body for error messages.");
            return;
        }

        const btn = document.getElementById('initiate-public-pay');
        if (btn) {
            btn.innerHTML = `<span class="spinner-grow spinner-grow-sm" role="status" aria-hidden="true"></span> Waiting for Authorization...`;
            btn.disabled = true;
        }

        const receiverID = document.getElementById('confirm-receiver-id')?.value;
        const amount     = document.getElementById('confirm-amount-p')?.value;
        const payFrom    = document.getElementById('confirm-payfrom-p')?.value;
        const charge     = document.getElementById('confirm-charge-p')?.value;
        const total      = document.getElementById('confirm-total-p')?.value;
        const reference  = document.getElementById('confirm-ref-p')?.value;

        const payload = {
            branch_id: receiverID,
            amount: Number(amount),
            charge: Number(charge),
            total_amount: Number(total),
            pay_from: payFrom,
            default_reference: reference,
            extra_reference: extraRefs.map(item => item.value),
            phone: localStorage.getItem('login_phone'),
            mode: "DIRECT_MOMO",
            is_public_biller: true
        };

        try {
            const response = await ApiService.createVoucherWithAttachment(payload, attachmentData)

            if (response.success) {
                $("#confirmPublicPayModal").modal("hide");
                UIManager.showSuccessModal(
                    "Payment Successful",
                    `Successfully paid SZL ${amount} to the biller.`,
                    () => {
                        this.requestPublicReceiversData();
                        this.requestVouchersData();
                    }
                );
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: response.message || 'Failed to process payment to biller.',
                    confirmButtonText: 'OK',
                    customClass: {
                        popup: 'my-swal-popup',
                        title: 'my-swal-title',
                        content: 'my-swal-content',
                        htmlContainer: 'my-swal-text',
                        confirmButton: 'my-confirm-button'
                    }
                });
            }
        } catch (error) {
            const existingModal = document.getElementById("confirmPublicPayModal");
            if (existingModal) {
                const bsModal = bootstrap.Modal.getInstance(existingModal);
                bsModal.hide();
            }

            console.error("Error paying public receiver:", error);
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: error || error.message || 'Failed to process payment to biller.',
                confirmButtonText: 'OK',
                customClass: {
                    popup: 'my-swal-popup',
                    title: 'my-swal-title',
                    content: 'my-swal-content',
                    htmlContainer: 'my-swal-text',
                    confirmButton: 'my-confirm-button'
                }
            });
        } finally {
            if (btn) {
                btn.innerHTML = `<i class="bi bi-shield-lock me-2"></i><span>Confirm & Pay</span>`;
                btn.disabled = false;
            }
        }
    }

    async payDirectMerchant(event, extraRefs, attachmentData) {
        const modalContent = event.target.closest('.modal-content');
        const modalBody = modalContent?.querySelector('.modal-body');
        if (!modalBody) {
            console.error("Cannot find modal body for error messages.");
            return;
        }

        const btn = document.getElementById('initiate-direct-pay');
        if (btn) {
            btn.innerHTML = `<span class="spinner-grow spinner-grow-sm" role="status" aria-hidden="true"></span> Waiting for Authorization...`;
            btn.disabled = true;
        }

        const merchantCode = document.getElementById('confirm-merchant-code-d')?.value;
        const amount     = document.getElementById('confirm-amount-d')?.value;
        const payFrom    = document.getElementById('confirm-payfrom-d')?.value;
        const charge     = document.getElementById('confirm-charge-d')?.value;
        const total      = document.getElementById('confirm-total-d')?.value;
        const reference  = document.getElementById('confirm-ref-d')?.value;

        const payload = {
            branch_id: 0, // Set branch_id to 0 for direct payments
            merchant_code: merchantCode, // Use merchant code as branch identifier for direct payments
            amount: Number(amount),
            charge: Number(charge),
            total_amount: Number(total),
            pay_from: payFrom,
            default_reference: reference,
            extra_reference: extraRefs.map(item => item.value),
            phone: localStorage.getItem('login_phone'),
            mode: "DIRECT_MOMO",
            is_public_biller: false
        };

        try {
            const response = await ApiService.createDirectMerchantPayment(payload, attachmentData)

            if (response.success) {
                $("#confirmDirectPayModal").modal("hide");
                UIManager.showSuccessModal(
                    "Payment Successful",
                    `Successfully paid SZL ${amount} to merchant ${merchantCode}.`,
                    () => {
                        this.requestVouchersData();
                    }
                );
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: response.message || 'Failed to process payment to merchant.',
                    confirmButtonText: 'OK',
                    customClass: {
                        popup: 'my-swal-popup',
                        title: 'my-swal-title',
                        content: 'my-swal-content',
                        htmlContainer: 'my-swal-text',
                        confirmButton: 'my-confirm-button'
                    }
                });
            }
        } catch (error) {
            const existingModal = document.getElementById("confirmDirectPayModal");
            if (existingModal) {
                const bsModal = bootstrap.Modal.getInstance(existingModal);
                bsModal.hide();
            }

            console.error("Error paying direct merchant:", error);
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: error || error.message || 'Failed to process payment to merchant.',
                confirmButtonText: 'OK',
                customClass: {
                    popup: 'my-swal-popup',
                    title: 'my-swal-title',
                    content: 'my-swal-content',
                    htmlContainer: 'my-swal-text',
                    confirmButton: 'my-confirm-button'
                }
            });
        } finally {
            if (btn) {
                btn.innerHTML = `<i class="bi bi-shield-lock me-2"></i><span>Confirm & Pay</span>`;
                btn.disabled = false;
            }
        }
    }

    async createVoucher(voucherData) {
        try {
            const modalElement = document.getElementById('newTransactionModal');
            const attachmentData = getAttachmentData(modalElement);
            const response = await ApiService.createVoucherWithAttachment(voucherData, attachmentData);
            showSuccessModal("Voucher Created", {
                message: "Voucher has been created successfully",
                details: response
            });
        } catch (error) {
            console.error("Error creating voucher:", error);
            Alert.showAlert(document.querySelector('.modal-body'), 
                'Failed to create voucher. Please try again.', 'danger');
        }
    }

    // --- Data Fetching and UI Update Orchestration ---
    async requestPublicReceiversData() {
        const receiversTable = document.getElementById('receiversTable');
        if (!receiversTable) return;
        receiversTable.innerHTML = "<p><em>Loading public receivers...</em></p>";
        try {
            console.log("Requesting public receivers...");
            UIManager.requestPublicReceivers()
        } catch (error) {
            console.error("Failed to fetch/display public receivers:", error);
            receiversTable.innerHTML = "<p class='text-danger'>Could not load public receivers list.</p>";
        }
    }

    async requestVouchersData() {
        const vouchersTable = document.getElementById('vouchersTable');
        if (!vouchersTable) return;
        vouchersTable.innerHTML = "<p><em>Loading transaction history...</em></p>";
        try {
            UIManager.requestVouchers();
        } catch (error) {
            console.error("Failed to fetch/display vouchers:", error);
            vouchersTable.innerHTML = "<p class='text-danger'>Could not load transaction history.</p>";
        }
    }

    async loadAndRenderVouchers() {
        console.log("Loading and rendering vouchers...");

        const vouchersSection = document.getElementById('vouchers-section');
        if (!vouchersSection) {
            console.warn("vouchers-section element not found.");
            return;
        }

        // Optional loading state
        vouchersSection.innerHTML = `<p>Loading vouchers...</p>`;

        try {
            const userPhone = localStorage.getItem('login_phone');
            if (!userPhone) {
                console.warn("No login_phone found in localStorage.");
                vouchersSection.innerHTML = `<p>No user phone available.</p>`;
                return;
            }

            const response = await ApiService.getVouchers(userPhone);

            // Normalize response
            const allVouchers = Array.isArray(response)
                ? response
                : (response && Array.isArray(response.vouchers))
                    ? response.vouchers
                    : [];

            // Categorize vouchers safely
            const activeVouchers = allVouchers.filter(v => v?.status?.toLowerCase() === 'active');
            const redeemedVouchers = allVouchers.filter(v => v?.status?.toLowerCase() === 'redeemed');
            const expiredVouchers = allVouchers.filter(v => v?.status?.toLowerCase() === 'expired');

            console.log("Vouchers loaded:", {
                active: activeVouchers.length,
                redeemed: redeemedVouchers.length,
                expired: expiredVouchers.length
            });

            // Render template with categorized vouchers counts
            vouchersSection.innerHTML = Templates.getStatsContentTemplate(
                activeVouchers.length,
                redeemedVouchers.length,
                expiredVouchers.length
            );

            // Rebind dynamic event listeners
            UIManager.initializeEventListeners(this);

        } catch (error) {
            console.error("Failed to load vouchers:", error);
            vouchersSection.innerHTML = `<p class="text-danger">Failed to load vouchers. Please try again later.</p>`;
        }
    }

    async loadRecentTransactions() {
        console.log("Loading recent transactions...");

        try {
            // Call the existing requestVouchers function which handles the transactions table
            UIManager.requestVouchers();
        } catch (error) {
            console.error("Error loading recent transactions:", error);

            // Fallback: show error state in transactions table
            const tbody = document.getElementById("transactions-tbody");
            const emptyMsg = document.getElementById("transactions-empty");

            if (tbody) {
                tbody.innerHTML = '';
            }
            if (emptyMsg) {
                emptyMsg.classList.remove('d-none');
                emptyMsg.innerHTML = `
                    <div class="text-center py-5">
                        <div class="empty-state">
                            <i class="bi bi-exclamation-triangle display-4 text-warning mb-3"></i>
                            <h6 class="text-white">Failed to load transactions</h6>
                            <p class="text-white small mb-3">There was an error loading your transaction history</p>
                            <button class="btn btn-outline-primary btn-sm" onclick="window.location.reload()">
                                <i class="bi bi-arrow-clockwise me-1"></i>
                                Retry
                            </button>
                        </div>
                    </div>
                `;
            }
        }
    }

}