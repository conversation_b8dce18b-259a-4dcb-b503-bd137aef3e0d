# Complete API Documentation - Locked Payments System

## Base URL
- **Production**: `https://lockedpay.centurionbd.com`
- **Development**: `http://localhost:8080`

## Authentication
Most endpoints require JWT authentication via `Authorization: Bearer <token>` header.

## Rate Limiting
- **Login endpoints**: 50 attempts per hour
- **OTP endpoints**: 30 attempts per 15 minutes

---

## 🔐 Authentication Endpoints

### POST `/api/auth/login/password`
**Description**: Login with username and password  
**Authentication**: None required  
**Rate Limited**: Yes (50/hour)

**Request Body**:
```json
{
  "username": "your_username",
  "password": "your_password",
  "merchant_id": "123456"  // 4-6 digit merchant code (required for merchant users)
}
```

**Success Response (200)**:
```json
{
  "message": "Login successful",
  "token": "jwt-token-here",
  "user_id": 123,
  "username": "your_username",
  "role": "merchant_default_admin",
  "merchant_code": "123456",
  "merchant_id": 5147,
  "merchant_name": "Your Store"
}
```

**Error Responses**:
- `400`: Invalid request format
- `401`: Invalid username or password

---

### POST `/api/auth/otp/request`
**Description**: Request OTP for login  
**Authentication**: None required  
**Rate Limited**: Yes (30/15min)

**Request Body**:
```json
{
  "phone": "+**********"
}
```

**Success Response (200)**:
```json
{
  "message": "OTP sent successfully",
  "expires_in": 300
}
```

---

### POST `/api/auth/otp/verify`
**Description**: Verify OTP and login  
**Authentication**: None required  
**Rate Limited**: Yes (30/15min)

**Request Body**:
```json
{
  "phone": "+**********",
  "otp": "123456"
}
```

**Success Response (200)**:
```json
{
  "message": "Login successful",
  "token": "jwt-token-here",
  "user_id": 123,
  "username": "<EMAIL>",
  "role": "user"
}
```

---

### POST `/api/auth/password-reset/request`
**Description**: Request password reset via email  
**Authentication**: None required  
**Rate Limited**: Yes (50/hour)

**Request Body**:
```json
{
  "email": "<EMAIL>"
}
```

**Success Response (200)**:
```json
{
  "message": "Password reset email sent"
}
```

---

### POST `/api/auth/password-reset/confirm`
**Description**: Confirm password reset with token  
**Authentication**: None required  
**Rate Limited**: Yes (50/hour)

**Request Body**:
```json
{
  "token": "reset-token-here",
  "new_password": "new-password-min-8-chars"
}
```

**Success Response (200)**:
```json
{
  "message": "Password reset successful"
}
```

---

### POST `/api/auth/password-reset/sms/request`
**Description**: Request SMS password reset  
**Authentication**: None required  
**Rate Limited**: Yes (50/hour)

**Request Body**:
```json
{
  "merchant_code": "123456",
  "phone": "+**********"
}
```

**Success Response (200)**:
```json
{
  "message": "SMS code sent successfully",
  "expires_in": 300
}
```

---

### POST `/api/auth/password-reset/sms/confirm`
**Description**: Confirm SMS password reset  
**Authentication**: None required  
**Rate Limited**: Yes (50/hour)

**Request Body**:
```json
{
  "merchant_code": "123456",
  "phone": "+**********",
  "sms_code": "123456",
  "new_password": "new-password-min-8-chars"
}
```

**Success Response (200)**:
```json
{
  "message": "Password reset successful"
}
```

---

### POST `/api/auth/sso`
**Description**: Single Sign-On authentication  
**Authentication**: None required

**Request Body**:
```json
{
  "token": "external-sso-token",
  "provider": "external-provider"
}
```

**Success Response (200)**:
```json
{
  "message": "SSO login successful",
  "token": "jwt-token-here",
  "user_id": 123,
  "username": "<EMAIL>"
}
```

---

## 🏪 Merchant Onboarding

### POST `/api/momo/verify-merchant`
**Description**: Verify merchant via MTN MOMO  
**Authentication**: None required

**Request Body**:
```json
{
  "phone": "+**********",
  "email": "<EMAIL>",
  "otp": "123456"
}
```

**Success Response (200)**:
```json
{
  "status": true,
  "message": "Merchant found",
  "token": "jwt-token-here",
  "merchant_code": "123456",
  "merchant_id": 789,
  "merchant_name": "ABC Store",
  "admin_user": {
    "user_id": 123,
    "username": "abcstore1234",
    "password": "LockedPay123!",
    "message": "Please change this password on first login"
  }
}
```

---

## 👥 User Management

### POST `/api/users/admin_username`
**Description**: Create merchant admin username  
**Authentication**: Required

**Request Body**:
```json
{
  "merchant_id": 123,
  "username": "new_admin_username"
}
```

**Success Response (201)**:
```json
{
  "message": "Admin username created successfully",
  "user_id": 456,
  "username": "new_admin_username"
}
```

---

### PUT `/api/users/new_password/{id}`
**Description**: Update initial admin password  
**Authentication**: Required

**Request Body**:
```json
{
  "merchant_id": 5147,
  "new_password": "new-password-min-8-chars"
}
```

**Success Response (200)**:
```json
{
  "message": "Password updated successfully"
}
```

---

### PUT `/api/users/update_password/{id}`
**Description**: Update user password  
**Authentication**: Required

**Request Body**:
```json
{
  "old_password": "current-password",
  "new_password": "new-password-min-8-chars"
}
```

**Success Response (200)**:
```json
{
  "message": "Password updated successfully"
}
```

---

### PUT `/api/users/basic_info/{id}`
**Description**: Update user basic information  
**Authentication**: Required

**Request Body**:
```json
{
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "phone": "79997045"
}
```

**Success Response (200)**:
```json
{
  "message": "User updated successfully"
}
```

---

### PUT `/api/users/admin_username/{id}`
**Description**: Update admin username  
**Authentication**: Required

**Request Body**:
```json
{
  "username": "new_username"
}
```

**Success Response (200)**:
```json
{
  "message": "Username updated successfully"
}
```

---

### GET `/api/users/credentials/status/{id}`
**Description**: Get user credential completion status  
**Authentication**: Required

**Success Response (200)**:
```json
{
  "user_id": 123,
  "is_complete": true,
  "username_set": true,
  "password_set": true
}
```

---

### GET `/api/users/admin_profile/{id}`
**Description**: Get admin profile information  
**Authentication**: Required

**Success Response (200)**:
```json
{
  "user_id": 123,
  "username": "admin_user",
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "phone": "79997045",
  "role": "merchant_admin",
  "merchant_id": 456
}
```

---

### GET `/api/users/debug_password/{id}`
**Description**: Debug password hash (temporary endpoint)  
**Authentication**: Required

**Success Response (200)**:
```json
{
  "user_id": "123",
  "username": "test_user",
  "password_hash": "$2a$10$...",
  "hash_length": 60,
  "test_password": "LockedPay123!",
  "verification_result": true,
  "verification_error": "none"
}
```

---

## 🏢 Merchant Management

### PUT `/api/merchants/{id}`
**Description**: Update merchant information  
**Authentication**: Required  
**Content-Type**: `multipart/form-data`

**Form Data**:
```
merchant_name: "ABC Store"
trading_name: "ABC Trading"
merchant_industry: "Retail"
merchant_sub_industry: "Electronics"
merchant_phone: "79997045"
merchant_email: "<EMAIL>"
is_active: "true"
logo: [file upload]
```

**Success Response (200)**:
```json
{
  "message": "Merchant updated successfully",
  "merchant_id": 123,
  "logo_url": "/static/merchants/logo_123.jpg"
}
```

---

### GET `/api/merchants/{user_id}`
**Description**: Get merchant by admin user ID  
**Authentication**: Required

**Success Response (200)**:
```json
{
  "id": 123,
  "name": "ABC Store",
  "trading_name": "ABC Trading",
  "industry": "Retail",
  "sub_industry": "Electronics",
  "phone": "79997045",
  "email": "<EMAIL>",
  "logo_url": "/static/merchants/logo_123.jpg",
  "is_active": true,
  "merchant_code": "123456"
}
```

---

### GET `/api/merchants/active`
**Description**: List all active merchants with branches  
**Authentication**: Required

**Success Response (200)**:
```json
[
  {
    "id": 123,
    "name": "ABC Store",
    "trading_name": "ABC Trading",
    "branches": [
      {
        "id": 1,
        "address": "123 Main St",
        "branch_code": "ABC001",
        "phone": "79997045",
        "email": "<EMAIL>"
      }
    ]
  }
]
```

---

## 🏪 Branch Management

### POST `/api/merchants/branches`
**Description**: Create a new merchant branch  
**Authentication**: Required

**Request Body**:
```json
{
  "merchant_id": 123,
  "address": "1st Street, John Doe Drive, ABC",
  "phone": "76000000",
  "email": "<EMAIL>",
  "branch_code": "123456"  // Optional, auto-generated if not provided
}
```

**Success Response (201)**:
```json
{
  "message": "Branch created successfully",
  "branch_id": 456,
  "branch_code": "ABC001"
}
```

---

### PUT `/api/merchants/branches/{branch_id}`
**Description**: Update merchant branch  
**Authentication**: Required

**Request Body**:
```json
{
  "address": "Updated address",
  "phone": "76000001",
  "email": "<EMAIL>"
}
```

**Success Response (200)**:
```json
{
  "message": "Branch updated successfully"
}
```

---

### DELETE `/api/merchants/branches/{branch_id}`
**Description**: Delete merchant branch  
**Authentication**: Required

**Success Response (200)**:
```json
{
  "message": "Branch deleted successfully"
}
```

---

## 👨‍💼 Cashier Management

### POST `/api/merchants/cashiers/{branch_id}`
**Description**: Create a new cashier for a branch  
**Authentication**: Required

**Request Body**:
```json
{
  "phone": "79997045",
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "username": "johndoe",
  "password": "password123"
}
```

**Success Response (200)**:
```json
{
  "message": "Cashier created successfully",
  "user_id": 789,
  "username": "johndoe"
}
```

---

### GET `/api/merchants/cashiers/{user_id}`
**Description**: List cashiers for a merchant admin  
**Authentication**: Required

**Success Response (200)**:
```json
[
  {
    "user_id": 789,
    "username": "johndoe",
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "phone": "79997045",
    "branch_id": 456,
    "branch_address": "123 Main St"
  }
]
```

---

### PUT `/api/merchants/cashiers/{user_id}`
**Description**: Update cashier information  
**Authentication**: Required

**Request Body**:
```json
{
  "cashier_phone": "79997046",
  "cashier_first_name": "Jane",
  "cashier_last_name": "Smith",
  "cashier_email": "<EMAIL>"
}
```

**Success Response (200)**:
```json
{
  "message": "Cashier updated successfully"
}
```

---

### DELETE `/api/merchants/cashiers/{user_id}`
**Description**: Delete a cashier  
**Authentication**: Required

**Success Response (200)**:
```json
{
  "message": "Cashier deleted successfully"
}
```

---

### GET `/api/merchants/cashiers/branch/{user_id}`
**Description**: Get cashier's branch ID  
**Authentication**: Required

**Success Response (200)**:
```json
{
  "branch_id": 456
}
```

---

## 👥 Merchant User Management

### POST `/api/merchants/users/{merchant_code}`
**Description**: Create a merchant user  
**Authentication**: Required

**Request Body**:
```json
{
  "branch_id": "1",
  "phone": "79997045",
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "username": "johndoe",
  "role": "admin",
  "password": "password123"
}
```

**Success Response (201)**:
```json
{
  "message": "Merchant user created successfully",
  "user_id": 789,
  "username": "johndoe"
}
```

---

### GET `/api/merchants/admin-users/{user_id}`
**Description**: Get merchant users for admin  
**Authentication**: Required

**Success Response (200)**:
```json
[
  {
    "user_id": 789,
    "username": "johndoe",
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "phone": "79997045",
    "role": "admin",
    "is_active": true
  }
]
```

---

### PUT `/api/merchants/admin-users/{user_id}`
**Description**: Update merchant user  
**Authentication**: Required

**Request Body**:
```json
{
  "first_name": "Jane",
  "last_name": "Smith",
  "email": "<EMAIL>",
  "phone": "79997046",
  "role": "manager"
}
```

**Success Response (200)**:
```json
{
  "message": "Merchant user updated successfully"
}
```

---

### DELETE `/api/merchants/admin-users/{user_id}`
**Description**: Delete merchant user  
**Authentication**: Required

**Success Response (200)**:
```json
{
  "message": "Merchant user deleted successfully"
}
```

---

## 🎫 Voucher Management

### POST `/api/vouchers/create/v2`
**Description**: Create a new voucher  
**Authentication**: Required  
**Content-Type**: `multipart/form-data`

**Form Data**:
```
branch_id: 123
phone: "79997045"
amount: 100.00
charge: 5.00
total_amount: 105.00
pay_from: "MOMO"
default_reference: "Payment for goods"
extra_reference: "Additional info"
mode: "CUSTOMER_INITIATED"  // CUSTOMER_INITIATED, MERCHANT_INITIATED, DIRECT_MOMO
is_public_biller: false
```

**Success Response (200)**:
```json
{
  "message": "Voucher created successfully",
  "voucher_id": 456,
  "voucher_code": "ABC123",
  "otp": "123456",
  "expires_at": "2024-01-01T12:00:00Z"
}
```

---

### GET `/api/vouchers/client/{phone}`
**Description**: Get vouchers by phone number  
**Authentication**: Required

**Success Response (200)**:
```json
[
  {
    "id": 456,
    "voucher_code": "ABC123",
    "amount": 100.00,
    "status": "active",
    "expires_at": "2024-01-01T12:00:00Z",
    "merchant_name": "ABC Store",
    "branch_address": "123 Main St"
  }
]
```

---

### GET `/api/vouchers/admin/{user_id}`
**Description**: Get redeemed vouchers for merchant admin  
**Authentication**: Required

**Success Response (200)**:
```json
[
  {
    "voucher_id": 456,
    "voucher_code": "ABC123",
    "amount": 100.00,
    "redeemed_at": "2024-01-01T12:00:00Z",
    "customer_phone": "79997045",
    "cashier_name": "John Doe",
    "branch_address": "123 Main St"
  }
]
```

---

### GET `/api/vouchers/admin/stats/{user_id}`
**Description**: Get voucher statistics for admin  
**Authentication**: Required

**Success Response (200)**:
```json
{
  "total_vouchers": 100,
  "active_vouchers": 25,
  "redeemed_vouchers": 70,
  "cancelled_vouchers": 5,
  "total_amount": 10000.00,
  "redeemed_amount": 7000.00
}
```

---

### POST `/api/vouchers/verify/{user_id}`
**Description**: Verify voucher code (cashier)  
**Authentication**: Required

**Request Body**:
```json
{
  "voucher_code": "ABC123",
  "pin_code": "123456"
}
```

**Success Response (200)**:
```json
{
  "valid": true,
  "voucher_id": 456,
  "amount": 100.00,
  "customer_phone": "79997045",
  "expires_at": "2024-01-01T12:00:00Z"
}
```

---

### POST `/api/vouchers/redeem/{user_id}`
**Description**: Redeem voucher (cashier)  
**Authentication**: Required

**Request Body**:
```json
{
  "voucher_code": "ABC123",
  "pin_code": "123456",
  "amount_used": 100.00
}
```

**Success Response (200)**:
```json
{
  "message": "Voucher redeemed successfully",
  "voucher_id": 456,
  "amount_used": 100.00,
  "remaining_amount": 0.00
}
```

---

### POST `/api/vouchers/cancel/{id}`
**Description**: Cancel active voucher  
**Authentication**: Required

**Success Response (200)**:
```json
{
  "message": "Voucher cancelled successfully",
  "refund_amount": 95.00,
  "cancellation_fee": 5.00
}
```

---

### GET `/api/vouchers/direct_momo/{branch_id}`
**Description**: Get active direct MOMO vouchers by branch  
**Authentication**: Required

**Success Response (200)**:
```json
[
  {
    "voucher_id": 456,
    "voucher_code": "ABC123",
    "amount": 100.00,
    "customer_phone": "79997045",
    "created_at": "2024-01-01T10:00:00Z",
    "expires_at": "2024-01-01T12:00:00Z"
  }
]
```

---

## 🎁 Gift Voucher Management

### POST `/api/gift-vouchers/create`
**Description**: Create individual gift voucher  
**Authentication**: Required

**Request Body**:
```json
{
  "amount": 100.00,
  "recipient_name": "John Doe",
  "recipient_phone": "79997045",
  "personal_message": "Happy Birthday!",
  "occasion": "Birthday",
  "merchant_id": 123,
  "sender_phone": "79997046",
  "sender_name": "Jane Smith",
  "hide_amount": false
}
```

**Success Response (201)**:
```json
{
  "message": "Gift voucher created successfully",
  "voucher_id": 789,
  "voucher_code": "GV123456",
  "pin_code": "1234"
}
```

---

### POST `/api/gift-vouchers/events`
**Description**: Create group event for gift vouchers  
**Authentication**: Required

**Request Body**:
```json
{
  "event_name": "John's Birthday",
  "occasion": "Birthday",
  "organizer_name": "Jane Smith",
  "merchant_id": 123,
  "event_date": "2024-01-01",
  "event_photo": "base64-image-data"
}
```

**Success Response (201)**:
```json
{
  "message": "Event created successfully",
  "event_id": "EVT123456",
  "event_name": "John's Birthday"
}
```

---

### POST `/api/gift-vouchers/events/{event_id}/contribute`
**Description**: Contribute to group event  
**Authentication**: Required

**Request Body**:
```json
{
  "amount": 50.00,
  "contributor_phone": "79997047",
  "contributor_name": "Bob Wilson",
  "personal_message": "Best wishes!"
}
```

**Success Response (200)**:
```json
{
  "message": "Contribution added successfully",
  "contribution_id": 456,
  "total_amount": 150.00
}
```

---

### GET `/api/gift-vouchers/events/{event_id}`
**Description**: Get event details  
**Authentication**: Required

**Success Response (200)**:
```json
{
  "event": {
    "id": 123,
    "eventId": "EVT123456",
    "eventName": "John's Birthday",
    "occasion": "Birthday",
    "organizerName": "Jane Smith",
    "merchantId": 123,
    "merchantName": "ABC Store",
    "totalAmount": 150.00,
    "status": "active",
    "createdAt": "2024-01-01T10:00:00Z",
    "eventDate": "2024-01-01"
  }
}
```

---

### POST `/api/gift-vouchers/verify`
**Description**: Verify gift voucher  
**Authentication**: Required

**Request Body**:
```json
{
  "voucher_code": "GV123456",
  "pin_code": "1234"
}
```

**Success Response (200)**:
```json
{
  "valid": true,
  "voucher_id": 789,
  "amount": 100.00,
  "recipient_name": "John Doe",
  "sender_name": "Jane Smith",
  "personal_message": "Happy Birthday!",
  "expires_at": "2024-12-31T23:59:59Z"
}
```

---

### POST `/api/gift-vouchers/redeem/{user_id}`
**Description**: Redeem gift voucher  
**Authentication**: Required

**Request Body**:
```json
{
  "voucher_code": "GV123456",
  "pin_code": "1234",
  "amount_used": 100.00
}
```

**Success Response (200)**:
```json
{
  "message": "Gift voucher redeemed successfully",
  "voucher_id": 789,
  "amount_used": 100.00,
  "remaining_amount": 0.00
}
```

---

### GET `/api/gift-vouchers/recipient/{phone}`
**Description**: Get gift vouchers by recipient phone  
**Authentication**: Required

**Success Response (200)**:
```json
[
  {
    "voucher_id": 789,
    "voucher_code": "GV123456",
    "amount": 100.00,
    "sender_name": "Jane Smith",
    "personal_message": "Happy Birthday!",
    "status": "active",
    "expires_at": "2024-12-31T23:59:59Z"
  }
]
```

---

### GET `/api/gift-vouchers/sent`
**Description**: Get gift vouchers sent by user  
**Authentication**: Required

**Success Response (200)**:
```json
[
  {
    "voucher_id": 789,
    "voucher_code": "GV123456",
    "amount": 100.00,
    "recipient_name": "John Doe",
    "personal_message": "Happy Birthday!",
    "status": "active",
    "created_at": "2024-01-01T10:00:00Z"
  }
]
```

---

### GET `/api/gift-vouchers/events/contributed`
**Description**: Get events contributed to by phone  
**Authentication**: Required

**Success Response (200)**:
```json
[
  {
    "event_id": "EVT123456",
    "event_name": "John's Birthday",
    "contribution_amount": 50.00,
    "total_event_amount": 150.00,
    "event_date": "2024-01-01",
    "status": "active"
  }
]
```

---

### POST `/api/gift-vouchers/bulk-upload`
**Description**: Bulk create gift vouchers  
**Authentication**: Required  
**Content-Type**: `multipart/form-data`

**Form Data**:
```
csv_file: [CSV file upload]
merchant_id: 123
```

**Success Response (200)**:
```json
{
  "message": "Bulk upload completed",
  "total_processed": 100,
  "successful": 95,
  "failed": 5,
  "errors": [
    {
      "row": 10,
      "error": "Invalid phone number"
    }
  ]
}
```

---

### GET `/api/gift-vouchers/merchant/stats/{user_id}`
**Description**: Get merchant gift voucher statistics  
**Authentication**: Required

**Success Response (200)**:
```json
{
  "total_vouchers": 500,
  "active_vouchers": 150,
  "redeemed_vouchers": 300,
  "expired_vouchers": 50,
  "total_amount": 50000.00,
  "redeemed_amount": 30000.00
}
```

---

### GET `/api/gift-vouchers/merchant/redeemed/{user_id}`
**Description**: Get merchant redeemed gift vouchers  
**Authentication**: Required

**Success Response (200)**:
```json
[
  {
    "voucher_id": 789,
    "voucher_code": "GV123456",
    "amount": 100.00,
    "redeemed_at": "2024-01-01T12:00:00Z",
    "recipient_name": "John Doe",
    "cashier_name": "Jane Smith",
    "branch_address": "123 Main St"
  }
]
```

---

### GET `/api/gift-vouchers/merchant/active/{user_id}`
**Description**: Get merchant active gift vouchers  
**Authentication**: Required

**Success Response (200)**:
```json
[
  {
    "voucher_id": 789,
    "voucher_code": "GV123456",
    "amount": 100.00,
    "recipient_name": "John Doe",
    "sender_name": "Jane Smith",
    "created_at": "2024-01-01T10:00:00Z",
    "expires_at": "2024-12-31T23:59:59Z"
  }
]
```

---

### GET `/api/gift-vouchers/merchant/analytics/{user_id}`
**Description**: Get merchant gift voucher analytics  
**Authentication**: Required

**Success Response (200)**:
```json
{
  "daily_stats": [
    {
      "date": "2024-01-01",
      "vouchers_created": 10,
      "vouchers_redeemed": 8,
      "total_amount": 1000.00
    }
  ],
  "monthly_stats": [
    {
      "month": "2024-01",
      "vouchers_created": 300,
      "vouchers_redeemed": 250,
      "total_amount": 30000.00
    }
  ]
}
```

---

### POST `/api/gift-vouchers/admin/cleanup/redeemed-images`
**Description**: Manual cleanup of redeemed gift voucher images  
**Authentication**: Required (Admin only)

**Success Response (200)**:
```json
{
  "message": "Cleanup completed",
  "files_deleted": 25,
  "space_freed": "150MB"
}
```

---

### POST `/api/gift-vouchers/admin/cleanup/expired-images`
**Description**: Manual cleanup of expired gift voucher images  
**Authentication**: Required (Admin only)

**Success Response (200)**:
```json
{
  "message": "Cleanup completed",
  "files_deleted": 10,
  "space_freed": "60MB"
}
```

---

## 💰 Price Calculation

### GET `/api/price/calculate`
**Description**: Calculate charges for transactions  
**Authentication**: Required

**Query Parameters**:
- `amount` (required): Transaction amount
- `type` (optional): Transaction type

**Success Response (200)**:
```json
{
  "amount": 100.00,
  "charge": 5.00,
  "total_amount": 105.00,
  "charge_percentage": 5.0
}
```

---

## 📱 Basic Info

### GET `/api/basicinfo/{phone}`
**Description**: Get username from MOMO phone number  
**Authentication**: Required

**Success Response (200)**:
```json
{
  "phone": "79997045",
  "username": "john_doe",
  "first_name": "John",
  "last_name": "Doe"
}
```

---

## 🏪 Public Receivers

### GET `/api/public-receivers/active`
**Description**: Get visible public receivers  
**Authentication**: None required

**Success Response (200)**:
```json
[
  {
    "id": 123,
    "merchant_id": 456,
    "merchant_name": "ABC Store",
    "logo_url": "/static/merchants/logo_456.jpg",
    "branch_address": "123 Main St",
    "branch_phone": "79997045",
    "ranking": 1,
    "is_visible": true
  }
]
```

---

## 🔒 Internal API

### POST `/api/internal/auth/validate`
**Description**: Internal authentication validation  
**Authentication**: Internal API key

**Request Body**:
```json
{
  "token": "jwt-token-to-validate"
}
```

**Success Response (200)**:
```json
{
  "valid": true,
  "user_id": 123,
  "username": "<EMAIL>",
  "role": "merchant_admin"
}
```

---

### POST `/api/internal/auth/user`
**Description**: Internal user authentication  
**Authentication**: Internal API key

**Request Body**:
```json
{
  "username": "<EMAIL>",
  "password": "password123"
}
```

**Success Response (200)**:
```json
{
  "authenticated": true,
  "user_id": 123,
  "username": "<EMAIL>",
  "role": "merchant_admin"
}
```

---

## 💳 Digital Wallet & PayCard

### POST `/api/merchants/digital-wallet/create`
**Description**: Create digital wallet  
**Authentication**: Required

**Request Body**:
```json
{
  "user_id": 123,
  "initial_balance": 100.00
}
```

**Success Response (201)**:
```json
{
  "message": "Wallet created successfully",
  "wallet_id": "WLT123456",
  "balance": 100.00
}
```

---

### POST `/api/merchants/digital-wallet/balance`
**Description**: Get wallet balance  
**Authentication**: Required

**Request Body**:
```json
{
  "wallet_id": "WLT123456"
}
```

**Success Response (200)**:
```json
{
  "wallet_id": "WLT123456",
  "balance": 250.00,
  "currency": "SZL"
}
```

---

### POST `/api/merchants/digital-wallet/topup`
**Description**: Top up wallet  
**Authentication**: Required

**Request Body**:
```json
{
  "wallet_id": "WLT123456",
  "amount": 100.00,
  "payment_method": "MOMO"
}
```

**Success Response (200)**:
```json
{
  "message": "Top up successful",
  "transaction_id": "TXN789",
  "new_balance": 350.00
}
```

---

### POST `/api/merchants/digital-wallet/transfer`
**Description**: Transfer funds between wallets  
**Authentication**: Required

**Request Body**:
```json
{
  "from_wallet_id": "WLT123456",
  "to_wallet_id": "WLT789012",
  "amount": 50.00,
  "description": "Payment for services"
}
```

**Success Response (200)**:
```json
{
  "message": "Transfer successful",
  "transaction_id": "TXN790",
  "from_balance": 300.00,
  "to_balance": 150.00
}
```

---

### POST `/api/merchants/digital-wallet/transactions`
**Description**: Get wallet transaction history  
**Authentication**: Required

**Request Body**:
```json
{
  "wallet_id": "WLT123456",
  "limit": 50,
  "offset": 0
}
```

**Success Response (200)**:
```json
{
  "transactions": [
    {
      "transaction_id": "TXN789",
      "type": "topup",
      "amount": 100.00,
      "description": "Wallet top up",
      "timestamp": "2024-01-01T12:00:00Z",
      "balance_after": 350.00
    }
  ],
  "total_count": 25
}
```

---

### GET `/api/merchants/digital-wallet/transfers`
**Description**: Get transfer history  
**Authentication**: Required

**Success Response (200)**:
```json
[
  {
    "transfer_id": "TRF123",
    "from_wallet_id": "WLT123456",
    "to_wallet_id": "WLT789012",
    "amount": 50.00,
    "description": "Payment for services",
    "timestamp": "2024-01-01T12:00:00Z",
    "status": "completed"
  }
]
```

---

### POST `/api/merchants/paycard/create`
**Description**: Create PayCard  
**Authentication**: Required

**Request Body**:
```json
{
  "wallet_id": "WLT123456",
  "card_type": "virtual",
  "spending_limit": 1000.00
}
```

**Success Response (201)**:
```json
{
  "message": "PayCard created successfully",
  "card_id": "CRD123456",
  "card_number": "**********123456",
  "card_type": "virtual"
}
```

---

### GET `/api/merchants/paycard/number/{card_number}`
**Description**: Get PayCard details by card number  
**Authentication**: Required

**Success Response (200)**:
```json
{
  "card_id": "CRD123456",
  "card_number": "**********123456",
  "card_type": "virtual",
  "status": "active",
  "spending_limit": 1000.00,
  "balance": 500.00
}
```

---

### PUT `/api/merchants/paycard/id/{card_id}`
**Description**: Update PayCard settings  
**Authentication**: Required

**Request Body**:
```json
{
  "spending_limit": 1500.00,
  "status": "active"
}
```

**Success Response (200)**:
```json
{
  "message": "PayCard updated successfully"
}
```

---

### PUT `/api/merchants/paycard/id/{card_id}/pin`
**Description**: Update PayCard PIN  
**Authentication**: Required

**Request Body**:
```json
{
  "new_pin": "1234",
  "confirm_pin": "1234"
}
```

**Success Response (200)**:
```json
{
  "message": "PIN updated successfully"
}
```

---

### POST `/api/merchants/paycard/id/{card_id}/activate-physical`
**Description**: Activate physical PayCard  
**Authentication**: Required

**Request Body**:
```json
{
  "activation_code": "ACT123456"
}
```

**Success Response (200)**:
```json
{
  "message": "Physical card activated successfully"
}
```

---

### POST `/api/merchants/paycard/id/{card_id}/request-physical`
**Description**: Request physical PayCard  
**Authentication**: Required

**Request Body**:
```json
{
  "delivery_address": "123 Main St, City, Country",
  "delivery_phone": "79997045"
}
```

**Success Response (200)**:
```json
{
  "message": "Physical card requested successfully",
  "request_id": "REQ123456",
  "estimated_delivery": "2024-01-15"
}
```

---

### GET `/api/merchants/paycard/id/{card_id}/transactions`
**Description**: Get PayCard transaction history  
**Authentication**: Required

**Success Response (200)**:
```json
[
  {
    "transaction_id": "TXN123",
    "amount": 25.00,
    "merchant": "ABC Store",
    "description": "Purchase",
    "timestamp": "2024-01-01T12:00:00Z",
    "status": "completed"
  }
]
```

---

### GET `/api/merchants/paycard/wallet/{wallet_id}`
**Description**: Get all PayCards for a wallet  
**Authentication**: Required

**Success Response (200)**:
```json
[
  {
    "card_id": "CRD123456",
    "card_number": "****1234",
    "card_type": "virtual",
    "status": "active",
    "spending_limit": 1000.00
  }
]
```

---

### POST `/api/merchants/paycard/transaction`
**Description**: Process PayCard transaction  
**Authentication**: Required

**Request Body**:
```json
{
  "card_id": "CRD123456",
  "amount": 25.00,
  "merchant_id": 789,
  "description": "Purchase at ABC Store"
}
```

**Success Response (200)**:
```json
{
  "message": "Transaction processed successfully",
  "transaction_id": "TXN123",
  "remaining_balance": 475.00
}
```

---

### POST `/api/merchants/paycard/activate`
**Description**: Activate card with code  
**Authentication**: Required

**Request Body**:
```json
{
  "card_number": "**********123456",
  "activation_code": "ACT123456"
}
```

**Success Response (200)**:
```json
{
  "message": "Card activated successfully"
}
```

---

### POST `/api/merchants/paycard/resend-activation`
**Description**: Resend activation code  
**Authentication**: Required

**Request Body**:
```json
{
  "card_id": "CRD123456"
}
```

**Success Response (200)**:
```json
{
  "message": "Activation code resent successfully"
}
```

---

## 📱 Service Subscriptions

### GET `/api/merchants/service-subscription/services`
**Description**: Get available subscription services  
**Authentication**: Required

**Success Response (200)**:
```json
[
  {
    "service_id": "SVC123",
    "name": "Premium Features",
    "description": "Access to premium features",
    "price": 29.99,
    "billing_cycle": "monthly"
  }
]
```

---

### POST `/api/merchants/service-subscription/subscribe`
**Description**: Subscribe to a service  
**Authentication**: Required

**Request Body**:
```json
{
  "wallet_id": "WLT123456",
  "service_id": "SVC123",
  "billing_cycle": "monthly"
}
```

**Success Response (200)**:
```json
{
  "message": "Subscription successful",
  "subscription_id": "SUB123456",
  "next_billing_date": "2024-02-01"
}
```

---

### POST `/api/merchants/service-subscription/cancel`
**Description**: Cancel service subscription  
**Authentication**: Required

**Request Body**:
```json
{
  "subscription_id": "SUB123456"
}
```

**Success Response (200)**:
```json
{
  "message": "Subscription cancelled successfully",
  "cancellation_date": "2024-01-01",
  "refund_amount": 15.00
}
```

---

### GET `/api/merchants/service-subscription/wallet`
**Description**: Get wallet subscriptions  
**Authentication**: Required

**Query Parameters**:
- `wallet_id` (required): Wallet ID

**Success Response (200)**:
```json
[
  {
    "subscription_id": "SUB123456",
    "service_name": "Premium Features",
    "status": "active",
    "next_billing_date": "2024-02-01",
    "amount": 29.99
  }
]
```

---

## 📁 Static File Endpoints

### GET `/static/vouchers/{filename}`
**Description**: Access uploaded voucher files  
**Authentication**: None required

### GET `/static/merchants/{filename}`
**Description**: Access uploaded merchant files (logos, etc.)  
**Authentication**: None required

### GET `/static/gift-cards/{filename}`
**Description**: Access uploaded gift card files  
**Authentication**: None required

---

## 📊 Common Response Codes

- **200**: Success
- **201**: Created
- **400**: Bad Request (Invalid input)
- **401**: Unauthorized (Invalid/missing token)
- **403**: Forbidden (Insufficient permissions)
- **404**: Not Found
- **429**: Too Many Requests (Rate limited)
- **500**: Internal Server Error

---

## 🔧 Common Error Response Format

```json
{
  "error": "Error description",
  "details": "Additional error details",
  "expected_format": {
    "field1": "expected_value_type",
    "field2": "expected_value_type"
  }
}
```

---

## 📝 Notes

1. **Authentication**: Most endpoints require JWT token in `Authorization: Bearer <token>` header
2. **Rate Limiting**: Login and OTP endpoints have rate limiting
3. **File Uploads**: Use `multipart/form-data` for file uploads
4. **Merchant Code**: Use 4-6 digit merchant code (not database ID) for merchant-related operations
5. **Phone Numbers**: Should be in format without country code (e.g., "79997045")
6. **Dates**: ISO 8601 format (e.g., "2024-01-01T12:00:00Z")
7. **Amounts**: Decimal numbers (e.g., 100.00)

---

*This documentation covers all endpoints currently available in the Locked Payments system. For additional details or clarifications, refer to the Swagger documentation at `/swagger/index.html` when the server is running.*
